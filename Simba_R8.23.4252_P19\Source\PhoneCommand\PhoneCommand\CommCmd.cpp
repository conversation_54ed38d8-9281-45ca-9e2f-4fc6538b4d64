#include "StdAfx.h"
#include "CommCmd.h"
#include "LoadPhaseCheckConfig.h"
#include <phdef.h>
///
extern "C"
{
#include "CRC\crc16.h"
}

///
#define LOW4BITS(a)                         ((BYTE)((((BYTE)(((BYTE)(a)) <<4)))>>4))
#define HI4BITS(a)                          ((BYTE)(((BYTE)(a))>>4))
#define MAKE1BYTEBY2BYTES(h4,l4)            ((BYTE)((h4)<<4)|(l4))

#define MISCDATA_PHASECHECK_OFFET           (0)
#define MAX_PHASECHECK_SIZE                 (4*1024)
#define MISCDATA_MAX_SIZE                   (1024*1024)
#define MISCDATA_CUSTOMER_GOLDEN_OFFSET     (64*1024)
#define MAX_CUSTOMER_SIZE                   (64*1024)
//////////////////////////////////////////////////////////////////////////
CCommCmd::CCommCmd(void)
{
}

CCommCmd::~CCommCmd(void)
{
}

SPRESULT CCommCmd::GetSWVer(PC_SWVER_TYPE eType, LPVOID lpBuff, uint32 u32BufSize)
{
    if (NULL == lpBuff || IsBadWritePtr(lpBuff, u32BufSize))
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid parameters!", __FUNCTION__);
        return SP_E_PHONE_INVALID_PARAMETER;
    }
    else
    {
        ZeroMemory(lpBuff, u32BufSize);
    }

    uint8 u8SubType = ZERO_SUBTYPE;
    switch (eType)
    {
    case CP:
        LogFmtStrA(SPLOGLV_INFO, "Get modem SW version:");
        u8SubType = 0x00;
        break;
    case AP:
        LogFmtStrA(SPLOGLV_INFO, "Get AP SW version:");
        u8SubType = 0x02;
        break;
    default:
        LogFmtStrA(SPLOGLV_ERROR, "Invalid version type %d", eType);
        return SP_E_PHONE_INVALID_PARAMETER;
    }

    DeclareDiagHeader(hd, DIAG_SWVER_F, u8SubType);
    return SendAndRecv(hd, NULL, 0, hd, lpBuff, u32BufSize, NULL, m_dwTimeOut);
}

SPRESULT CCommCmd::ULACheckLicenseNBD(void* lpModel, uint32 u32ModelLen, void* lpRecvBuff, uint32 u32RecvBuffLen)
{
    if (NULL == lpModel || NULL == lpRecvBuff)
    {
        LogFmtStrA(SPLOGLV_ERROR, "[%s] Error Code = %d, Invalid parameters!", __FUNCTION__, SP_E_PHONE_INVALID_PARAMETER);
        return SP_E_PHONE_INVALID_PARAMETER;
    }
    else
    {
        char szSendBuf[30] = { NULL };
        uint32 sendLen = 30;
        uint32 recvLen = 0;

        if (u32ModelLen > sendLen)
        {
            LogFmtStrA(SPLOGLV_ERROR, "[%s] Error Code = %d, ModelLen > %d!", __FUNCTION__, SP_E_PHONE_INVALID_PARAMETER, sendLen);
            return SP_E_PHONE_INVALID_PARAMETER;
        }
        memcpy(szSendBuf, lpModel, u32ModelLen);

        LogFmtStrA(SPLOGLV_INFO, "ULACheckLicenseNBD:");
        DeclareDiagHeader(hd, 5, 0x2F);
        DeclareDiagHeader(rd, 5, 0x2F);


        int nOperCode = SendAndRecv(hd, (const void*)szSendBuf, sendLen, rd, (void*)m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);

        if (SP_OK == nOperCode)
        {
            if (recvLen < 1)
            {
                nOperCode = SP_E_PHONE_INVALID_LENGTH;
                LogFmtStrA(SPLOGLV_ERROR, "[%s] Error Code = %d, Invalid response length %d < 1!", __FUNCTION__, nOperCode, recvLen);
                return nOperCode;
            }
            else
            {

                memcpy(lpRecvBuff, (const void*)(m_diagBuff), u32RecvBuffLen);
            }
        }

        return nOperCode;
    }
}

SPRESULT CCommCmd::ULACheckLicense(void* lpRecvBuff, uint32 u32RecvBuffLen)
{
    if (NULL == lpRecvBuff)
    {
        LogFmtStrA(SPLOGLV_ERROR, "[%s] Error Code = %d, Invalid parameters!", __FUNCTION__, SP_E_PHONE_INVALID_PARAMETER);
        return SP_E_PHONE_INVALID_PARAMETER;
    }
    else
    {
        uint32 recvLen = 0;

        LogFmtStrA(SPLOGLV_INFO, "ULACheckLicense:");
        DeclareDiagHeader(hd, 5, 0x2F);
        DeclareDiagHeader(rd, 5, 0x2F);


        int nOperCode = SendAndRecv(hd, NULL, 0, rd, (void*)m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);

        if (SP_OK == nOperCode)
        {
            if (recvLen < 1)
            {
                nOperCode = SP_E_PHONE_INVALID_LENGTH;
                LogFmtStrA(SPLOGLV_ERROR, "[%s] Error Code = %d, Invalid response length %d < 1!", __FUNCTION__, nOperCode, recvLen);
                return nOperCode;
            }
            else
            {
                memcpy(lpRecvBuff, (const void*)(m_diagBuff), u32RecvBuffLen);
            }
        }

        return nOperCode;
    }
}

SPRESULT CCommCmd::ULAReadUIDNBD(LPBYTE lpSendBuff, uint32 u32SendBuffLen, UINT* pUidBitLen, LPBYTE lpRecvUidBuffer, uint32 u32RecvLen, uint32* pulUidBufLen)
{
    if (NULL == lpSendBuff || lpRecvUidBuffer == NULL)
    {
        LogFmtStrA(SPLOGLV_ERROR, "[%s] Error Code = %d, Invalid parameters!", __FUNCTION__, SP_E_PHONE_INVALID_PARAMETER);
        return SP_E_PHONE_INVALID_PARAMETER;
    }

    if (u32SendBuffLen > 8)
    {
        LogFmtStrA(SPLOGLV_INFO, "[%s]: key length is more than 8!", __FUNCTION__);
        return SP_E_PHONE_INVALID_PARAMETER;
    }

    LogFmtStrA(SPLOGLV_INFO, "ULAReadUIDNBD:");
    DeclareDiagHeader(hd, 5, 0x30);
    DeclareDiagHeader(rd, 5, 0x30);

    uint32 recvLen = 0;
    int nOperCode = SendAndRecv(hd, lpSendBuff, u32SendBuffLen, rd, (void*)m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);

    if (SP_OK == nOperCode)
    {
        if (recvLen != 5 && recvLen != 9)
        {
            nOperCode = SP_E_PHONE_INVALID_LENGTH;
            LogFmtStrA(SPLOGLV_ERROR, "[%s] Error Code = %d, Invalid response length %d!", __FUNCTION__, nOperCode, recvLen);
            return nOperCode;
        }

        //BufRcv[0] == 0 新生成UID成功返回；BufRcv[0] == 0x0F UID已存在，并成功返回；其他都是errorcode
        if ((m_diagBuff[0] != 0) && (m_diagBuff[0] != 0x0F))
        {
            nOperCode = SP_E_PHONE_INVALID_DATA;
            LogFmtStrA(SPLOGLV_ERROR, "[%s] Error Code = %d, Invalid response!", __FUNCTION__, nOperCode);
            return nOperCode;
        }

        if (recvLen == 5)  //nStatus + 4 Bytes(32bits) UID
        {
            *pUidBitLen = 32;
            *pulUidBufLen = 4;
            if (u32RecvLen < *pulUidBufLen)
            {
                nOperCode = SP_E_PHONE_BUFFER_TOO_SMALL;
                LogFmtStrA(SPLOGLV_ERROR, "[%s] Error Code = %d, lpRecvUidBuffer < %d!", __FUNCTION__, nOperCode, *pulUidBufLen);
                return nOperCode;
            }

            memcpy(lpRecvUidBuffer, (const void*)(m_diagBuff + 1), *pulUidBufLen);
        }
        else if (recvLen == 9) //nStatus + 8 Bytes(64bits) UID
        {
            *pUidBitLen = 64;
            *pulUidBufLen = 8;
            if (u32RecvLen < *pulUidBufLen)
            {
                nOperCode = SP_E_PHONE_BUFFER_TOO_SMALL;
                LogFmtStrA(SPLOGLV_ERROR, "[%s] Error Code = %d, lpRecvUidBuffer < %d!", __FUNCTION__, nOperCode, *pulUidBufLen);
                return nOperCode;
            }
            memcpy(lpRecvUidBuffer, (const void*)(m_diagBuff + 1), *pulUidBufLen);
        }

    }

    return nOperCode;

}

SPRESULT CCommCmd::ULAReadUID(LPBYTE lpSendBuff, uint32 u32SendBuffLen, UINT* pUidBitLen, LPBYTE lpRecvUidBuffer, uint32 u32RecvLen, uint32* pulUidBufLen)
{
    if (NULL == lpSendBuff || lpRecvUidBuffer == NULL)
    {
        LogFmtStrA(SPLOGLV_ERROR, "[%s] Error Code = %d, Invalid parameters!", __FUNCTION__, SP_E_PHONE_INVALID_PARAMETER);
        return SP_E_PHONE_INVALID_PARAMETER;
    }

    if (u32SendBuffLen > 8)
    {
        LogFmtStrA(SPLOGLV_INFO, "[%s]: key length is more than 8!", __FUNCTION__);
        return SP_E_PHONE_INVALID_PARAMETER;
    }

    LogFmtStrA(SPLOGLV_INFO, "ULAReadUID:");
    DeclareDiagHeader(hd, 5, 0x30);
    DeclareDiagHeader(rd, 5, 0x30);

    uint32 recvLen = 0;
    int nOperCode = SendAndRecv(hd, lpSendBuff, u32SendBuffLen, rd, (void*)m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);

    if (SP_OK == nOperCode)
    {
        if (recvLen < 12)
        {
            nOperCode = SP_E_PHONE_INVALID_LENGTH;
            LogFmtStrA(SPLOGLV_ERROR, "[%s] Error Code = %d, Invalid response length %d!", __FUNCTION__, nOperCode, recvLen);
            return nOperCode;
        }

        //BufRcv[0] == 0 新生成UID成功返回；BufRcv[0] == 0x0F UID已存在，并成功返回；其他都是errorcode
        if ((m_diagBuff[0] != 0) && (m_diagBuff[0] != 0x0F))
        {
            nOperCode = SP_E_PHONE_INVALID_DATA;
            LogFmtStrA(SPLOGLV_ERROR, "[%s] Error Code = %d, Invalid response!", __FUNCTION__, nOperCode);
            return nOperCode;
        }

        if (recvLen >= 12)  //nStatus + 3 Byte padding + 8 Bytes(32bits) UID
        {
            *pUidBitLen = 64;
            *pulUidBufLen = 8;
            if (u32RecvLen < *pulUidBufLen)
            {
                nOperCode = SP_E_PHONE_BUFFER_TOO_SMALL;
                LogFmtStrA(SPLOGLV_ERROR, "[%s] Error Code = %d, lpRecvUidBuffer < %d!", __FUNCTION__, nOperCode, *pulUidBufLen);
                return nOperCode;
            }

            memcpy(lpRecvUidBuffer, (const void*)(m_diagBuff + 4), *pulUidBufLen);
        }
    }

    return nOperCode;

}

SPRESULT CCommCmd::ULAWriteLicense(void* lpBuff, uint32 u32BuffLen)
{
    if (NULL == lpBuff)
    {
        LogFmtStrA(SPLOGLV_ERROR, "[%s] Error Code = %d, Invalid parameters!", __FUNCTION__, SP_E_PHONE_INVALID_PARAMETER);
        return SP_E_PHONE_INVALID_PARAMETER;
    }
    else
    {
        LogFmtStrA(SPLOGLV_INFO, "ULAWriteLicense:");
        DeclareDiagHeader(hd, 5, 0x31);
        DeclareDiagHeader(rd, 5, 0x31);

        BYTE BufRcv[1] = { 0 }; //flag

        uint32 recvLen = 0;
        MessageBox("lpBuff: %s" , lpBuff);
        int nOperCode = SendAndRecv(hd, lpBuff, u32BuffLen, rd, (void*)m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);


        if (SP_OK == nOperCode)
        {
            if (recvLen < 1)
            {
                nOperCode = SP_E_PHONE_INVALID_LENGTH;
                LogFmtStrA(SPLOGLV_ERROR, "[%s] Error Code = %d, Invalid response length %d < 1!", __FUNCTION__, nOperCode, recvLen);
                return nOperCode;
            }

            memcpy(BufRcv, (const void*)(m_diagBuff), 1);

            if (BufRcv[0] != 0)
            {
                nOperCode = SP_E_PHONE_INVALID_DATA;
                LogFmtStrA(SPLOGLV_ERROR, "[%s] Error Code = %d, Invalid response %d!", __FUNCTION__, nOperCode, BufRcv[0]);
                return nOperCode;
            }
        }

        return nOperCode;
    }
}
SPRESULT CCommCmd::GetUID(uint8 UID[20])
{
    LogRawStrA(SPLOGLV_INFO, "Loading UID:");
    ZeroMemory((void*)UID, sizeof(uint8) * 20);

    uint32   u32UID0 = 0;
    uint32   u32UID1 = 0;
    DeclareDiagHeader(hd, DIAG_SYSTEM_F, 0x20);

    uint8 bufSend[2] = { 0 };
    uint8 bufRecv[6] = { 0 };
    uint32   recvLen = 0;

    bufSend[0] = 0;
    bufSend[1] = 0;
    CHKRESULT(SendAndRecv(hd, &bufSend, 2, hd, (void*)bufRecv, sizeof(bufRecv), &recvLen, m_dwTimeOut));
    u32UID0 = MAKELONG(MAKEWORD(bufRecv[2], bufRecv[3]), MAKEWORD(bufRecv[4], bufRecv[5]));

    bufSend[0] = 1;
    bufSend[1] = 0;
    CHKRESULT(SendAndRecv(hd, &bufSend, 2, hd, (void*)bufRecv, sizeof(bufRecv), &recvLen, m_dwTimeOut));
    u32UID1 = MAKELONG(MAKEWORD(bufRecv[2], bufRecv[3]), MAKEWORD(bufRecv[4], bufRecv[5]));

    uint8  LOTID5 = (uint8)(((u32UID0 & 0x00fc0000) >> 18) + 48);
    uint8  LOTID4 = (uint8)(((u32UID0 & 0x0003f000) >> 12) + 48);
    uint8  LOTID3 = (uint8)(((u32UID0 & 0x00000fc0) >> 6) + 48);
    uint8  LOTID2 = (uint8)((u32UID0 & 0x0000003f) + 48);
    uint8  LOTID1 = (uint8)(((u32UID1 & 0x7e000000) >> 25) + 48);
    uint8  LOTID0 = (uint8)(((u32UID1 & 0x01f80000) >> 19) + 48);
    uint8 WaferID = (uint8)((u32UID1 & 0x0007c000) >> 14);
    uint8      X = (uint8)((u32UID1 & 0x00003f89) >> 7);
    uint8      Y = (uint8)(u32UID1 & 0x0000007f);

    sprintf_s((char*)UID, 20, "%c%c%c%c%c%c%02d%03d%03d", LOTID5, LOTID4, LOTID3, LOTID2, LOTID1, LOTID0, WaferID, X, Y);

    return SP_OK;
}

SPRESULT CCommCmd::GetUID_V2(LPVOID lpBuff, UINT32 u32BufLen)
{
    if (NULL == lpBuff || 0 == u32BufLen || IsBadWritePtr(lpBuff, u32BufLen))
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid parameters!", __FUNCTION__);
        return SP_E_PHONE_INVALID_PARAMETER;
    }
    else
    {
        LogRawStrA(SPLOGLV_INFO, "Get UID V2:");
    }

    DeclareDiagHeader(hd, DIAG_SYSTEM_F, 0x32);

    uint32 recvLen = 0;
    SPRESULT res = SendAndRecv(hd, NULL, 0, hd, (void*)m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);
    if (SP_OK == res)
    {
        if (0 == recvLen)
        {
            LogFmtStrA(SPLOGLV_ERROR, "[GetUID] Invalid response length %d!", recvLen);
            return SP_E_PHONE_INVALID_LENGTH;
        }

        if (u32BufLen < recvLen)
        {
            memcpy(lpBuff, m_diagBuff, u32BufLen);
            res = SP_E_PHONE_BUFFER_TOO_SMALL;
            LogFmtStrA(SPLOGLV_WARN, "[GetUID] Receive buffer is too small %d < %d", u32BufLen, recvLen);
        }
        else
        {
            memcpy(lpBuff, m_diagBuff, recvLen);
        }
    }

    return res;
}

SPRESULT CCommCmd::LoadSecureBootState(UINT32* pu32State)
{
    if (NULL == pu32State)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid parameter!", __FUNCTION__);
        return SP_E_PHONE_INVALID_PARAMETER;
    }

    DeclareDiagHeader(hd, DIAG_SYSTEM_F, 0x24);
    DeclareDiagHeader(rd, DIAG_SYSTEM_F, 0x24);

    UINT32 recvLen = 0;
    SPRESULT res = SendAndRecv(hd, NULL, 0, hd, (void*)m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);
    if (SP_OK == res)
    {
        if (0 == recvLen)
        {
            LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid response length %d!", __FUNCTION__, recvLen);
            return SP_E_PHONE_INVALID_LENGTH;
        }

        *pu32State = m_diagBuff[0];
        LogFmtStrA(SPLOGLV_INFO, "SecureBoot is %s", (STATE_ENABLED == *pu32State) ? "enabled" : "not enabled");
    }

    return res;
}

SPRESULT CCommCmd::SecureBootProgramV1(void)
{
    CHKRESULT(engpcSecureBootProgramHash());
    CHKRESULT(engpcSecureBootEnable());

    return SP_OK;
}

SPRESULT CCommCmd::engpcSecureBootProgramHash(void)
{
    LogFmtStrA(SPLOGLV_INFO, "%s:", __FUNCTION__);

    DeclareDiagHeader(hd, DIAG_SYSTEM_F, 0x21);
    DeclareDiagHeader(rd, DIAG_SYSTEM_F, 0x21);

    uint32 u32revSize = 0;
    SPRESULT res = SendAndRecv(hd, NULL, 0, rd, (void*)m_diagBuff, sizeof(m_diagBuff), &u32revSize, 6000);
    if (SP_OK == res)
    {
        if (u32revSize < 1)
        {
            LogFmtStrA(SPLOGLV_ERROR, "[%s] Invalid response length %d!", __FUNCTION__, u32revSize);
            return SP_E_PHONE_INVALID_LENGTH;
        }

        if (1 == m_diagBuff[0])
        {
            return SP_OK;
        }
        else
        {
            LogFmtStrA(SPLOGLV_ERROR, "SecureBoot program hash failed, Code = %d", m_diagBuff[0]);
            return SP_E_PHONE_INVALID_DATA;
        }
    }

    return res;
}

SPRESULT CCommCmd::engpcSecureBootEnable(void)
{
    LogFmtStrA(SPLOGLV_INFO, "%s:", __FUNCTION__);

    DeclareDiagHeader(hd, DIAG_SYSTEM_F, 0x22);
    DeclareDiagHeader(rd, DIAG_SYSTEM_F, 0x22);

    uint32 u32revSize = 0;
    SPRESULT res = SendAndRecv(hd, NULL, 0, rd, (void*)m_diagBuff, sizeof(m_diagBuff), &u32revSize);
    if (SP_OK == res)
    {
        if (u32revSize < 1)
        {
            LogFmtStrA(SPLOGLV_ERROR, "[%s] Invalid response length %d!", __FUNCTION__, u32revSize);
            return SP_E_PHONE_INVALID_LENGTH;
        }

        if (0 == m_diagBuff[0])
        {
            LogFmtStrA(SPLOGLV_ERROR, "[%s] Enable SecureBoot failed!", __FUNCTION__);
            return SP_E_PHONE_INVALID_DATA;
        }
    }

    return res;
}

SPRESULT CCommCmd::EnableArmLog(BOOL bEnable)
{
    uint8 u8SubType = ZERO_SUBTYPE;
    if (bEnable)
    {
        LogRawStrA(SPLOGLV_INFO, "Enable ARM Logel:");
        u8SubType = ARM_LOG_ENABLE;
    }
    else
    {
        LogRawStrA(SPLOGLV_INFO, "Disable ARM Logel:");
        u8SubType = ARM_LOG_DISABLE;
    }

    DeclareDiagHeader(hd, DIAG_LOG_F, u8SubType);
    return SendAndRecv(hd, NULL, 0, hd, NULL, 0, NULL, m_dwTimeOut);
}

SPRESULT CCommCmd::PressKeyboard(uint32 u32Code)
{
    LogFmtStrA(SPLOGLV_INFO, "Press keyboard: Key = %d", u32Code);

    COMM_SOFTKEY_T key;
    key.SignalCode = Convert16((uint16)SOFTKEY);
    key.Size = Convert16((uint16)(sizeof(key)));
    key.key_code = Convert32(u32Code);

    DeclareDiagHeader(hd, DIAG_SOFTKEY_F, DIAG_NORMAL_KEY_MSG_INFO_F);
    return SendAndRecv(hd, (const void*)& key, sizeof(key), hd, NULL, 0, NULL, m_dwTimeOut);
}

SPRESULT CCommCmd::VibrateOn(BOOL bOn)
{
    uint8 u8SubType = ZERO_SUBTYPE;
    if (bOn)
    {
        LogRawStrA(SPLOGLV_INFO, "Vibrate On:");
        u8SubType = CURRENT_TEST_VIBRATOR_ON;
    }
    else
    {
        LogRawStrA(SPLOGLV_INFO, "Vibrate Off:");
        u8SubType = CURRENT_TEST_STOP;
    }

    DeclareDiagHeader(hd, DIAG_CURRENT_TEST_F, u8SubType);
    return SendAndRecv(hd, NULL, 0, hd, NULL, 0, NULL, m_dwTimeOut);
}

SPRESULT CCommCmd::LedOn(BOOL bOn)
{
    uint8 u8SubType = ZERO_SUBTYPE;
    if (bOn)
    {
        LogRawStrA(SPLOGLV_INFO, "Led On:");
        u8SubType = CURRENT_TEST_LED_ON;
    }
    else
    {
        LogRawStrA(SPLOGLV_INFO, "Led Off:");
        u8SubType = CURRENT_TEST_STOP;
    }

    DeclareDiagHeader(hd, DIAG_CURRENT_TEST_F, u8SubType);
    return SendAndRecv(hd, NULL, 0, hd, NULL, 0, NULL, m_dwTimeOut);
}

SPRESULT CCommCmd::GetVoltage(uint32* lpu32mV)
{
    CheckValidPointer(lpu32mV);

    LogRawStrA(SPLOGLV_INFO, "Get Voltage: ");

    DeclareDiagHeader(hd, DIAG_POWER_SUPPLY_F, ZERO_SUBTYPE);
    uint32   vol = 0;
    SPRESULT res = SendAndRecv(hd, NULL, 0, hd, (void*)& vol, sizeof(vol), NULL, m_dwTimeOut);
    if (SP_OK == res)
    {
        uint32 Voltage = Convert32(vol);
        int nUnit = (Voltage & 0xFF000000) >> 24;
        *lpu32mV = Voltage & 0x00FFFFFF;
        if (0/*10mV*/ == nUnit)
        {
            *lpu32mV *= 10;
        }

        LogFmtStrA(SPLOGLV_INFO, "Voltage = %d mV", *lpu32mV);
    }

    return res;
}

SPRESULT CCommCmd::LoadRegister(PC_ACCESS_MODE_E eMode, uint32 u32Addr, uint32 u32Count, LPVOID lpData)
{
    LogFmtStrA(SPLOGLV_INFO, "Load register: mode = %d, addr = 0x%4X, count = %d", eMode, u32Addr, u32Count);

    uint32 u32Size = 0;
    switch (eMode)
    {
    case PC_BYTE_ACCESS:
        u32Size = u32Count * sizeof(BYTE);
        break;
    case PC_WORD_ACCESS:
        u32Size = u32Count * sizeof(WORD);
        break;
    case PC_DWORD_ACCESS:
        u32Size = u32Count * sizeof(DWORD);
        break;
    default:
        break;
    }

    if (NULL == lpData || IsBadWritePtr(lpData, u32Size))
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid parameters!", __FUNCTION__);
        return SP_E_PHONE_INVALID_PARAMETER;
    }

    DIAG_MCU_READ_T req;
    req.SignalCode = Convert16((uint16)MCU_READ);
    req.Size = Convert16((uint16)sizeof(req));
    req.mode = Convert32((uint32)eMode);
    req.addr = Convert32((uint32)u32Addr);
    req.access_size = Convert32((uint32)u32Count);

    DeclareDiagHeader(hd, DIAG_MCU_F, ZERO_SUBTYPE);
    uint32 u32revSize = 0;
    SPRESULT res = SendAndRecv(hd, (const void*)& req, sizeof(req), hd, (void*)m_diagBuff, sizeof(m_diagBuff), &u32revSize, m_dwTimeOut);
    if (SP_OK == res)
    {
        /// DIAG_MCU_READ_T + data
        uint32 u32ExpSize = sizeof(req) + u32Size;
        CheckResponseLength(u32revSize, u32ExpSize);

        uint8* lpStart = (uint8*)& m_diagBuff[sizeof(req)];
        switch (eMode)
        {
        case PC_WORD_ACCESS:
            Convert16(lpStart, u32Size);
            break;
        case PC_DWORD_ACCESS:
            Convert32(lpStart, u32Size);
            break;
        default:
            break;
        }

        CopyMemory(lpData, (const void*)lpStart, u32Size);
    }

    return res;
}

SPRESULT CCommCmd::SaveRegister(PC_ACCESS_MODE_E eMode, uint32 u32Addr, uint32 u32Count, LPCVOID lpData)
{
    LogFmtStrA(SPLOGLV_INFO, "Save register: mode = %d, addr = 0x%4X, count = %d", eMode, u32Addr, u32Count);

    uint32 u32Size = 0;
    switch (eMode)
    {
    case PC_BYTE_ACCESS:
        u32Size = u32Count * sizeof(BYTE);
        break;
    case PC_WORD_ACCESS:
        u32Size = u32Count * sizeof(WORD);
        break;
    case PC_DWORD_ACCESS:
        u32Size = u32Count * sizeof(DWORD);
        break;
    default:
        break;
    }

    if (NULL == lpData || IsBadReadPtr(lpData, u32Size))
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid parameters!", __FUNCTION__);
        return SP_E_PHONE_INVALID_PARAMETER;
    }

    DIAG_MCU_WRITE_T req;
    req.SignalCode = Convert16((uint16)MCU_WRITE);
    req.Size = Convert16((uint16)(sizeof(req) + u32Size));
    req.mode = Convert32((uint32)eMode);
    req.addr = Convert32((uint32)u32Addr);
    req.access_size = Convert32((uint32)u32Count);

    /// Request Data: DIAG_MCU_WRITE_T + data
    CopyMemory((void*)& m_diagBuff[0], (const void*)& req, sizeof(req));
    uint32 u32SendSize = sizeof(req) + u32Size;
    if (u32SendSize > MAX_DIAG_BUFF_SIZE)
    {
        LogFmtStrA(SPLOGLV_ERROR, "Too much input data. %d", u32SendSize);
        return SP_E_PHONE_INVALID_PARAMETER;
    }
    else
    {
        /// Memory copying
        uint8* lpStart = (uint8*)& m_diagBuff[sizeof(req)];
        CopyMemory((void*)lpStart, lpData, u32Size);
        switch (eMode)
        {
        case PC_WORD_ACCESS:
            Convert16(lpStart, u32Size);
            break;
        case PC_DWORD_ACCESS:
            Convert32(lpStart, u32Size);
            break;
        default:
            break;
        }
    }

    DeclareDiagHeader(hd, DIAG_MCU_F, ZERO_SUBTYPE);
    return SendAndRecv(hd, (const void*)m_diagBuff, u32SendSize, hd, NULL, 0, NULL, m_dwTimeOut);
}

void CCommCmd::IMEI_Str2NV(const BYTE szImei[MAX_IMEI_STR_LENGTH], BYTE nvImei[MAX_IMEI_NV_LENGTH])
{
    ZeroMemory((void*)nvImei, MAX_IMEI_NV_LENGTH);

    BYTE temp1 = (BYTE)(szImei[0] - '0');
    BYTE temp2 = 0;

    // lower 4 bits of 1st byte must be 0xA 
    nvImei[0] = (BYTE)MAKE1BYTEBY2BYTES(temp1, (BYTE)0xA);

    for (int i = 1; i < MAX_IMEI_NV_LENGTH; i++)
    {
        temp1 = (BYTE)(szImei[2 * i - 0] - '0');
        temp2 = (BYTE)(szImei[2 * i - 1] - '0');
        nvImei[i] = (BYTE)MAKE1BYTEBY2BYTES(temp1, temp2);
    }
}

SPRESULT CCommCmd::IMEI_NV2Str(const BYTE nvImei[MAX_IMEI_NV_LENGTH], BYTE szImei[MAX_IMEI_STR_LENGTH])
{
    SPRESULT res = SP_OK;
    ZeroMemory((void*)szImei, MAX_IMEI_STR_LENGTH);

    BYTE tmpImei[MAX_IMEI_STR_LENGTH] = { 0 };
    for (int i = 0; i < (MAX_IMEI_NV_LENGTH - 1); i++)
    {
        tmpImei[i * 2 + 0] = HI4BITS(nvImei[i + 0]);
        tmpImei[i * 2 + 1] = LOW4BITS(nvImei[i + 1]);
    }

    tmpImei[14] = HI4BITS(nvImei[7]);


    try 
    {
        sprintf_s((char*)szImei, MAX_IMEI_STR_LENGTH, "%0x%0x%0x%0x%0x%0x%0x%0x%0x%0x%0x%0x%0x%0x%0x", tmpImei[0], tmpImei[1], tmpImei[2], tmpImei[3], tmpImei[4], \
            tmpImei[5], tmpImei[6], tmpImei[7], tmpImei[8], tmpImei[9], \
            tmpImei[10], tmpImei[11], tmpImei[12], tmpImei[13], tmpImei[14]);
    }
    catch(const std::exception& e)
    {
        LogFmtStrA(SPLOGLV_ERROR, "Invalid IMEI, %s",e.what());
        res = SP_E_SECURITY_SIMLOCK_INVALID_IMEI;
    }

    for (int i = 0; i < MAX_IMEI_STR_LENGTH - 1; i++)
    {
        if (tmpImei[i] >= 10)
        {
            LogFmtStrA(SPLOGLV_ERROR, "Invalid IMEI, IMEI cotains character that is not 0-9 number.");
            res = SP_E_SECURITY_SIMLOCK_INVALID_IMEI;
            return res;
        }
    }

    return res;
}

void CCommCmd::BTAddr_Str2NV(const BYTE szAddr[MAX_BT_ADDR_STR_LENGTH], BYTE nvAddr[MAX_BT_ADDR_NV_LENGTH])
{
    int  i = 0;
    WORD wAddr[MAX_BT_ADDR_NV_LENGTH * 2] = { 0 };
    BYTE uAddr[MAX_BT_ADDR_NV_LENGTH] = { 0 };

    for (i = 0; i < MAX_BT_ADDR_STR_LENGTH - 1; i++)
    {
        if (szAddr[i] >= '0' && szAddr[i] <= '9')
        {
            wAddr[i] = (WORD)(szAddr[i] - '0');
        }
        else if (szAddr[i] >= 'A' && szAddr[i] <= 'F')
        {
            wAddr[i] = (WORD)(szAddr[i] - 'A' + 10);
        }
        else if (szAddr[i] >= 'a' && szAddr[i] <= 'f')
        {
            wAddr[i] = (WORD)(szAddr[i] - 'a' + 10);
        }
        else
        {
            wAddr[i] = 0x00;
        }
    }

    for (i = 0; i < MAX_BT_ADDR_NV_LENGTH; i++)
    {
        uAddr[i] = (BYTE)((wAddr[2 * i]) * 16 + wAddr[2 * i + 1]);
    }

    for (i = 0; i < MAX_BT_ADDR_NV_LENGTH; i++)
    {
        nvAddr[i] = uAddr[MAX_BT_ADDR_NV_LENGTH - 1 - i];
    }
}

void CCommCmd::WIFIAddr_Str2NV(const BYTE szAddr[MAX_WIFI_ADDR_STR_LENGTH], BYTE nvAddr[MAX_WIFI_ADDR_NV_LENGTH])
{
    int  i = 0;
    WORD wAddr[MAX_WIFI_ADDR_NV_LENGTH * 2] = { 0 };
    BYTE uAddr[MAX_WIFI_ADDR_NV_LENGTH] = { 0 };

    for (i = 0; i < MAX_WIFI_ADDR_STR_LENGTH - 1; i++)
    {
        if (szAddr[i] >= '0' && szAddr[i] <= '9')
        {
            wAddr[i] = (WORD)(szAddr[i] - '0');
        }
        else if (szAddr[i] >= 'A' && szAddr[i] <= 'F')
        {
            wAddr[i] = (WORD)(szAddr[i] - 'A' + 10);
        }
        else if (szAddr[i] >= 'a' && szAddr[i] <= 'f')
        {
            wAddr[i] = (WORD)(szAddr[i] - 'a' + 10);
        }
        else
        {
            wAddr[i] = 0x00;
        }
    }

    for (i = 0; i < MAX_WIFI_ADDR_NV_LENGTH; i++)
    {
        uAddr[i] = (BYTE)((wAddr[2 * i]) * 16 + wAddr[2 * i + 1]);
    }

    for (i = 0; i < MAX_WIFI_ADDR_NV_LENGTH; i++)
    {
        nvAddr[i] = uAddr[i];
    }
}

SPRESULT CCommCmd::LoadProductData(LPPC_PRODUCT_DATA lpData, uint32 u32TimeOut /* = TIMEOUT_3S*/)
{
    CheckValidPointer(lpData);

    LogFmtStrA(SPLOGLV_INFO, "Load product data (IMEI/WIFI/BT MAC.): TimeOut = %d ms", u32TimeOut);

    uint8 u8SubType = (uint8)(lpData->u32OperMask);
    u8SubType |= 0x80; /// Load 

    DeclareDiagHeader(wh, DIAG_DIRECT_NV, u8SubType);
    DeclareDiagHeader(rh, DIAG_DIRECT_NV, 0x01);

    uint16    crc = 0x0000;
    uint32 u32revSize = 0;
    SPRESULT  res = SendAndRecv(wh, (const void*)& crc, sizeof(crc), rh, (void*)m_diagBuff, sizeof(m_diagBuff), &u32revSize, u32TimeOut);
    if (SP_OK == res)
    {
        REF_NVWriteDirect_T nv;
        uint32 u32ExpSize = sizeof(nv) + sizeof(crc);
        CheckResponseLength(u32revSize, u32ExpSize);

        uint16 u16CalcCRC = crc16(0, (unsigned char const*)& m_diagBuff[0], u32revSize - sizeof(crc));
        uint16 u16RecvCRC = Convert16(*((uint16*)& m_diagBuff[u32revSize - sizeof(crc)]));
        if (u16CalcCRC != u16RecvCRC)
        {
            LogFmtStrA(SPLOGLV_ERROR, "CRC check failed! (calculate)0x%X != (received)0x%X", u16CalcCRC, u16RecvCRC);
            return SP_E_PHONE_INVALID_CRC;
        }

        CopyMemory((void*)& nv, (const void*)& m_diagBuff[0], sizeof(nv));

        /// IMEI1 - IMEI4
        if (FNAMASK_RW_IMEI1 == (lpData->u32OperMask & FNAMASK_RW_IMEI1))
        {
            res = IMEI_NV2Str(nv.imei1, lpData->szImei1);
            LogFmtStrA(SPLOGLV_INFO, "IMEI1 = %s", lpData->szImei1);
            CHKRESULT(res);
        }

        if (FNAMASK_RW_IMEI2 == (lpData->u32OperMask & FNAMASK_RW_IMEI2))
        {
            res = IMEI_NV2Str(nv.imei2, lpData->szImei2);
            LogFmtStrA(SPLOGLV_INFO, "IMEI2 = %s", lpData->szImei2);
            CHKRESULT(res);
        }

        if (FNAMASK_RW_IMEI3 == (lpData->u32OperMask & FNAMASK_RW_IMEI3))
        {
            res = IMEI_NV2Str(nv.imei3, lpData->szImei3);
            LogFmtStrA(SPLOGLV_INFO, "IMEI3 = %s", lpData->szImei3);
            CHKRESULT(res);
        }

        if (FNAMASK_RW_IMEI4 == (lpData->u32OperMask & FNAMASK_RW_IMEI4))
        {
            res = IMEI_NV2Str(nv.imei4, lpData->szImei4);
            LogFmtStrA(SPLOGLV_INFO, "IMEI4 = %s", lpData->szImei4);
            CHKRESULT(res);
        }

        /// BT
        if (FNAMASK_RW_BTADDR == (lpData->u32OperMask & FNAMASK_RW_BTADDR))
        {
            ZeroMemory((void*)lpData->szBTAddr, sizeof(lpData->szBTAddr));
            sprintf_s((char*)lpData->szBTAddr, MAX_BT_ADDR_STR_LENGTH, "%02X%02X%02X%02X%02X%02X", \
                nv.BTAddr[5], nv.BTAddr[4], nv.BTAddr[3], nv.BTAddr[2], nv.BTAddr[1], nv.BTAddr[0]);
            LogFmtStrA(SPLOGLV_INFO, "BT Addr. = %s", lpData->szBTAddr);
        }

        // WIFI
        if (FNAMASK_RW_WIFIADDR == (lpData->u32OperMask & FNAMASK_RW_WIFIADDR))
        {
            ZeroMemory((void*)lpData->szWIFIAddr, sizeof(lpData->szWIFIAddr));
            sprintf_s((char*)lpData->szWIFIAddr, MAX_WIFI_ADDR_STR_LENGTH, "%02X%02X%02X%02X%02X%02X", \
                nv.WIFIAddr[0], nv.WIFIAddr[1], nv.WIFIAddr[2], nv.WIFIAddr[3], nv.WIFIAddr[4], nv.WIFIAddr[5]);
            LogFmtStrA(SPLOGLV_INFO, "WIFI Addr. = %s", lpData->szWIFIAddr);
        }
    }

    return res;
}

SPRESULT CCommCmd::SaveProductData(LPCPC_PRODUCT_DATA lpData, uint32 u32TimeOut /* = TIMEOUT_3S */)
{
    CheckValidPointer(lpData);

    LogFmtStrA(SPLOGLV_INFO, "Save product data (IMEI/WIFI/BT MAC.): TimeOut = %d ms", u32TimeOut);

    uint8 u8SubType = 0;
    REF_NVWriteDirect_T nv;
    ZeroMemory((void*)& nv, sizeof(nv));

    /// IMEI1
    if (FNAMASK_RW_IMEI1 == (lpData->u32OperMask & FNAMASK_RW_IMEI1))
    {
        u8SubType |= FNAMASK_RW_IMEI1;
        IMEI_Str2NV(lpData->szImei1, nv.imei1);
    }

    /// IMEI2
    if (FNAMASK_RW_IMEI2 == (lpData->u32OperMask & FNAMASK_RW_IMEI2))
    {
        u8SubType |= FNAMASK_RW_IMEI2;
        IMEI_Str2NV(lpData->szImei2, nv.imei2);
    }

    /// IMEI3
    if (FNAMASK_RW_IMEI3 == (lpData->u32OperMask & FNAMASK_RW_IMEI3))
    {
        u8SubType |= FNAMASK_RW_IMEI3;
        IMEI_Str2NV(lpData->szImei3, nv.imei3);
    }

    /// IMEI4
    if (FNAMASK_RW_IMEI4 == (lpData->u32OperMask & FNAMASK_RW_IMEI4))
    {
        u8SubType |= FNAMASK_RW_IMEI4;
        IMEI_Str2NV(lpData->szImei4, nv.imei4);
    }

    /// BT
    if (FNAMASK_RW_BTADDR == (lpData->u32OperMask & FNAMASK_RW_BTADDR))
    {
        u8SubType |= FNAMASK_RW_BTADDR;
        BTAddr_Str2NV(lpData->szBTAddr, nv.BTAddr);
    }

    /// WIFI
    if (FNAMASK_RW_WIFIADDR == (lpData->u32OperMask & FNAMASK_RW_WIFIADDR))
    {
        u8SubType |= FNAMASK_RW_WIFIADDR;
        WIFIAddr_Str2NV(lpData->szWIFIAddr, nv.WIFIAddr);
    }

    /// CRC
    uint32 u32Len = sizeof(nv);
    uint16    crc = Convert16(crc16(0, (unsigned char const*)& nv, u32Len));
    CopyMemory((void*)& m_diagBuff[0], (const void*)& nv, u32Len);
    CopyMemory((void*)& m_diagBuff[u32Len], (const void*)& crc, sizeof(crc));

    u8SubType |= 0x00; // Write
    DeclareDiagHeader(wh, DIAG_DIRECT_NV, u8SubType);
    wh.sn = 0;
    DeclareDiagHeader(rh, DIAG_DIRECT_NV, 0x01);
    rh.sn = 0;

    return SendAndRecv(wh, (const void*)& m_diagBuff[0], u32Len + sizeof(crc), rh, NULL, 0, NULL, u32TimeOut);
}

SPRESULT CCommCmd::LoadProductInfo(SPPH_MAGIC eMagic, LPVOID lpBuff, uint32 u32BytesToRead, uint32 u32TimeOut)
{
    if (NULL == lpBuff || IsBadWritePtr(lpBuff, u32BytesToRead))
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid parameters!", __FUNCTION__);
        return SP_E_PHONE_INVALID_PARAMETER;
    }

    LogFmtStrA(SPLOGLV_INFO, "Load product info. (PhaseCheck): Magic = %d, Bytes to read = %d, TimeOut = %d", eMagic, u32BytesToRead, u32TimeOut);

    SPRESULT res = SP_OK;
    switch (eMagic)
    {
    case SPAUTO:
        res = LoadProductInfo(lpBuff, u32BytesToRead, u32TimeOut);
        break;
    case SP09:
    case SP15:
        res = SP09_LoadProdInfo(lpBuff, u32BytesToRead, u32TimeOut);
        break;
    case SP25:
        res = SP05_LoadProdInfo(lpBuff, u32BytesToRead, MISCDATA_CUSTOMER_GOLDEN_OFFSET, u32TimeOut);
        break;
    default:
        res = SP05_LoadProdInfo(lpBuff, u32BytesToRead, MISCDATA_PHASECHECK_OFFET, u32TimeOut);
        break;
    }

    return res;
}

SPRESULT CCommCmd::SaveProductInfo(SPPH_MAGIC eMagic, LPCVOID lpBuff, uint32 u32BytesToWrite, uint32 u32TimeOut)
{
    if (NULL == lpBuff || u32BytesToWrite < 4/*Magic*/ || IsBadReadPtr(lpBuff, u32BytesToWrite))
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid parameters!", __FUNCTION__);
        return SP_E_PHONE_INVALID_PARAMETER;
    }

    LogFmtStrA(SPLOGLV_INFO, "Save product info. (PhaseCheck): Magic = %d, Bytes to save = %d, TimeOut = %d", eMagic, u32BytesToWrite, u32TimeOut);

    SPRESULT res = SP_OK;
    switch (eMagic)
    {
    case SPAUTO:
        res = SaveProductInfo(lpBuff, u32BytesToWrite, u32TimeOut);
        break;
    case SP09:
    case SP15:
        res = SP09_SaveProdInfo(lpBuff, u32BytesToWrite, u32TimeOut);
        break;
    case SP25:
        res = SP05_SaveProdInfo(lpBuff, u32BytesToWrite, MISCDATA_CUSTOMER_GOLDEN_OFFSET, u32TimeOut);
        break;
    default:
        res = SP05_SaveProdInfo(lpBuff, u32BytesToWrite, MISCDATA_PHASECHECK_OFFET, u32TimeOut);
        break;
    }

    return res;
}

SPRESULT CCommCmd::SP05_LoadProdInfo(void* lpBuff, uint32 u32BytesToRead, uint32 u32Offset, uint32 u32TimeOut)
{
    if (u32Offset >= MISCDATA_MAX_SIZE/*1MB*/)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid offset, %d < %d", __FUNCTION__, u32Offset, MISCDATA_MAX_SIZE);
        return SP_E_PHONE_INVALID_PARAMETER;
    }

    if (u32BytesToRead > MISCDATA_MAX_SIZE - u32Offset)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Data size is too big %d < %d", __FUNCTION__, u32BytesToRead, (MISCDATA_MAX_SIZE - u32Offset));
        return SP_E_PHONE_INVALID_PARAMETER;
    }

    DeclareDiagHeader(wh, DIAG_NVITEM_F, NVITEM_PRODUCT_CTRL_READ);
    DeclareDiagHeader(rh, DIAG_NVITEM_F, ZERO_SUBTYPE);
    wh.sn = 0x00; // If SPECIAL_SN, subtype will not be checked
    rh.sn = 0x00;

    SPRESULT res = SP_OK;
    if (u32Offset > MAX_PHASECHECK_SIZE/*4KB*/ || u32BytesToRead > MAX_PHASECHECK_SIZE)
    {
        // 这里粗暴处理，若要读取大于PHASECHECK的区域，直接使用新命令（Bug779941）
        wh.subtype = NVITEM_PRODUCT_CTRL_READ_EX;

        // Each package cannot exceed 7.5KB because of the limit of ENGPC buffer size 
        CONST uint32 MAX_PKG_SIZE = (uint32)(7.5 * 1024);

        uint32 u32Base = u32Offset;
        uint32 u32Size = 0;
        uint32 u32Left = u32BytesToRead;
        uint8* lpData = (uint8*)lpBuff;

        do
        {
            u32Size = u32Left > MAX_PKG_SIZE ? MAX_PKG_SIZE : u32Left;

            DIAG_MISCDATA_REQ_HEADER head;
            head.offset = Convert32(u32Base);
            head.length = Convert32(u32Size);

            uint32 u32revSize = 0;
            CHKRESULT(SendAndRecv(wh, (const void*)& head, sizeof(head), rh, (void*)m_diagBuff, sizeof(m_diagBuff), &u32revSize, u32TimeOut));

            // Response Data: offset (4 bytes) + length (4 bytes) + data[x]
            uint32 u32ExpSize = u32Size + sizeof(head);
            CheckResponseLength(u32revSize, u32ExpSize);

            CopyMemory(lpData, (const void*)& m_diagBuff[sizeof(head)], u32Size);

            u32Left = u32Left - u32Size;
            u32Base = u32Base + u32Size;
            lpData = lpData + u32Size;

        } while (u32Left > 0);

        return SP_OK;
    }
    else
    {
        DIAG_SP05_LOAD_REQ req;
        req.offset = Convert16((uint16)u32Offset);
        req.length = Convert16((uint16)u32BytesToRead);
        uint32 u32revSize = 0;
        res = SendAndRecv(wh, (const void*)& req, sizeof(req), rh, (void*)m_diagBuff, sizeof(m_diagBuff), &u32revSize, u32TimeOut);
        if (SP_OK == res)
        {
            // Response Data: offset (2 bytes) + length (2 bytes) + data
            uint32 u32ExpSize = u32BytesToRead + sizeof(req);
            CheckResponseLength(u32revSize, u32ExpSize);

            CopyMemory(lpBuff, (const void*)& m_diagBuff[sizeof(req)], u32BytesToRead);
        }
    }

    return res;
}

SPRESULT CCommCmd::SP05_SaveProdInfo(const void* lpBuff, uint32 u32BytesToWrite, uint32 u32Offset, uint32 u32TimeOut)
{
    if (u32Offset >= MISCDATA_MAX_SIZE/*1MB*/)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid offset, %d < %d", __FUNCTION__, u32Offset, MISCDATA_MAX_SIZE);
        return SP_E_PHONE_INVALID_PARAMETER;
    }

    if (u32BytesToWrite > MISCDATA_MAX_SIZE - u32Offset)
    {
        // Cannot be exceeded 1MBytes
        LogFmtStrA(SPLOGLV_ERROR, "%s: Data size is too big %d <= %d", __FUNCTION__, u32BytesToWrite, (MISCDATA_MAX_SIZE - u32Offset));
        return SP_E_PHONE_INVALID_PARAMETER;
    }

    DeclareDiagHeader(wh, DIAG_NVITEM_F, NVITEM_PRODUCT_CTRL_WRITE);
    DeclareDiagHeader(rh, DIAG_NVITEM_F, ZERO_SUBTYPE);
    wh.sn = 0x00; // If SPECIAL_SN, subtype will not be checked
    rh.sn = 0x00;

    if (u32Offset > MAX_PHASECHECK_SIZE/*4KB*/ || u32BytesToWrite > MAX_PHASECHECK_SIZE)
    {
        // 这里粗暴处理，若要读取大于PHASECHECK的区域，直接使用新命令（Bug779941）
        wh.subtype = NVITEM_PRODUCT_CTRL_WRITE_EX;

        // offset + length + data[x]
        DIAG_MISCDATA_REQ_HEADER head;

        // Each package cannot exceed 7.5KB because of the limit of ENGPC buffer size 
        CONST uint32 MAX_PKG_SIZE = (uint32)(7.5 * 1024);
        uint32 u32Base = u32Offset;
        uint32 u32Size = 0;
        uint32 u32Left = u32BytesToWrite;
        uint8* lpData = (uint8*)lpBuff;
        do
        {
            u32Size = u32Left > MAX_PKG_SIZE ? MAX_PKG_SIZE : u32Left;

            head.offset = u32Base;
            head.length = u32Size;

            // offset + length + data[x]
            CopyMemory((void*)& m_diagBuff[0], (const void*)& head, sizeof(head));
            CopyMemory((void*)& m_diagBuff[sizeof(head)], (const void*)lpData, u32Size);

            uint32 u32SendSize = sizeof(head) + u32Size;
            CHKRESULT(SendAndRecv(wh, (const void*)m_diagBuff, u32SendSize, rh, NULL, 0, NULL, u32TimeOut));

            u32Left = u32Left - u32Size;
            u32Base = u32Base + u32Size;
            lpData = lpData + u32Size;

        } while (u32Left > 0);

        return SP_OK;
    }
    else
    {
        DIAG_SP05_LOAD_REQ req;
        req.offset = Convert16((uint16)u32Offset);
        req.length = Convert16((uint16)u32BytesToWrite);
        CopyMemory((void*)& m_diagBuff[0], (const void*)& req, sizeof(req));
        CopyMemory((void*)& m_diagBuff[sizeof(req)], (const void*)lpBuff, u32BytesToWrite);

        uint32 u32SendSize = sizeof(req) + u32BytesToWrite;
        return SendAndRecv(wh, (const void*)m_diagBuff, u32SendSize, rh, NULL, 0, NULL, u32TimeOut);
    }
}

SPRESULT CCommCmd::SP09_LoadProdInfo(void* lpBuff, uint32 u32BytesToRead, uint32 u32TimeOut)
{
    DeclareDiagHeader(wh, DIAG_DIRECT_PHSCHK, 0x81);
    DeclareDiagHeader(rh, DIAG_DIRECT_PHSCHK, 0x01);

    uint16   crc = 0;
    uint32 u32revSize = 0;
    SPRESULT res = SendAndRecv(wh, (const void*)& crc, sizeof(crc), rh, (void*)m_diagBuff, sizeof(m_diagBuff), &u32revSize, u32TimeOut);
    if (SP_OK == res)
    {
        uint32 u32ExpSize = sizeof(crc) + u32BytesToRead;
        CheckResponseLength(u32revSize, u32ExpSize);

        /// Check CRC 
        uint16 u16CalcCRC = crc16(0, (unsigned char const*)m_diagBuff, u32BytesToRead);
        uint16 u16RecvCRC = Convert16(*((uint16*)& m_diagBuff[u32BytesToRead]));
        if (u16CalcCRC != u16RecvCRC)
        {
            LogFmtStrA(SPLOGLV_ERROR, "CRC check failed (0x%X != 0x%X)", u16RecvCRC, u16CalcCRC);
            return SP_E_PHONE_INVALID_CRC;
        }
        else
        {
            CopyMemory(lpBuff, (const void*)& m_diagBuff[0], u32BytesToRead);
        }
    }

    return res;
}

SPRESULT CCommCmd::SP09_SaveProdInfo(const void* lpBuff, uint32 u32BytesToWrite, uint32 u32TimeOut)
{
    uint16 crc = Convert16(crc16(0, (unsigned char const*)lpBuff, u32BytesToWrite));
    CopyMemory((void*)& m_diagBuff[0], lpBuff, u32BytesToWrite);
    CopyMemory((void*)& m_diagBuff[u32BytesToWrite], (const void*)& crc, sizeof(crc));

    DeclareDiagHeader(wh, DIAG_DIRECT_PHSCHK, 0x01);
    DeclareDiagHeader(rh, DIAG_DIRECT_PHSCHK, 0x01);

    uint32 u32SendSize = u32BytesToWrite + sizeof(crc);
    return SendAndRecv(wh, (const void*)& m_diagBuff[0], u32SendSize, rh, NULL, 0, NULL, u32TimeOut);
}

SPRESULT CCommCmd::LoadProductInfo(LPVOID lpBuff, uint32 u32BytesToRead, uint32 u32TimeOut)
{
    uint32   u32Magic = 0;
    SPRESULT res = SP05_LoadProdInfo(&u32Magic, sizeof(u32Magic), MISCDATA_PHASECHECK_OFFET, u32TimeOut);
    if (SP_OK != res)
    {
        return res;
    }

    uint32 dwExpSize = 0;
    switch (u32Magic)
    {
    case SP09_SPPH_MAGIC_NUMBER:
    {
        SetProperty(SP_ATTR_MAGIC_NUMBER, 0, (LPCVOID)SP09_SPPH_MAGIC_NUMBER);

        dwExpSize = SP09_MAX_PHASE_BUFF_SIZE;
        if (u32BytesToRead < dwExpSize)
        {
            LogFmtStrA(SPLOGLV_ERROR, "%s: buffer length is too small. Expected: %d bytes", __FUNCTION__, dwExpSize);
            return SP_E_PHONE_BUFFER_TOO_SMALL;
        }

        SP09_PHASE_CHECK_T sp09;
        CHKRESULT(SP09_LoadProdInfo((void*)& sp09, dwExpSize, u32TimeOut));
        CopyMemory(lpBuff, &sp09, dwExpSize);
    }
    break;

    case SP15_SPPH_MAGIC_NUMBER:
    {
        SetProperty(SP_ATTR_MAGIC_NUMBER, 0, (LPCVOID)SP15_SPPH_MAGIC_NUMBER);

        dwExpSize = SP15_MAX_PHASE_BUFF_SIZE;
        if (u32BytesToRead < dwExpSize)
        {
            LogFmtStrA(SPLOGLV_ERROR, "%s: buffer length is too small. Expected: %d bytes", __FUNCTION__, dwExpSize);
            return SP_E_PHONE_BUFFER_TOO_SMALL;
        }

        SP15_PHASE_CHECK_T sp15;
        CHKRESULT(SP09_LoadProdInfo((void*)& sp15, dwExpSize, u32TimeOut));
        CopyMemory(lpBuff, &sp15, dwExpSize);
    }
    break;

    default:
    {
        if (SP05_SPPH_MAGIC_NUMBER == u32Magic)
        {
            SetProperty(SP_ATTR_MAGIC_NUMBER, 0, (LPCVOID)SP05_SPPH_MAGIC_NUMBER);
        }

        CHKRESULT(SP05_LoadProdInfo((void*)lpBuff, u32BytesToRead, MISCDATA_PHASECHECK_OFFET, u32TimeOut));
    }
    break;
    }

    return res;
}

SPRESULT CCommCmd::SaveProductInfo(LPCVOID lpBuff, uint32 u32BytesToWrite, uint32 u32TimeOut)
{
    uint32  u32Magic = *((uint32*)lpBuff);
    switch (u32Magic)
    {
    case SP09_SPPH_MAGIC_NUMBER:
    case SP15_SPPH_MAGIC_NUMBER:
    {
        uint32 dwExpSize = (SP09_SPPH_MAGIC_NUMBER == u32Magic) ? SP09_MAX_PHASE_BUFF_SIZE : SP15_MAX_PHASE_BUFF_SIZE;
        if (u32BytesToWrite < dwExpSize)
        {
            LogFmtStrA(SPLOGLV_ERROR, "%s: buffer length is too small. Expected: %d bytes", __FUNCTION__, dwExpSize);
            return SP_E_PHONE_BUFFER_TOO_SMALL;
        }

        CHKRESULT(SP09_SaveProdInfo(lpBuff, dwExpSize, u32TimeOut));
    }
    break;

    default:
        CHKRESULT(SP05_SaveProdInfo(lpBuff, u32BytesToWrite, MISCDATA_PHASECHECK_OFFET, u32TimeOut));
        break;
    }

    return SP_OK;
}

SPRESULT CCommCmd::LoadSN(int nID, LPSTR lpSN, uint32 u32SNLength)
{
    if (NULL == lpSN || IsBadWritePtr(lpSN, u32SNLength))
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid parameters!", __FUNCTION__);
        return SP_E_PHONE_INVALID_PARAMETER;
    }
    else
    {
        ZeroMemory((void*)lpSN, u32SNLength);
    }

    LogFmtStrA(SPLOGLV_INFO, "Load SN%d:", nID);

    uint32 u32Magic = 0;
    SPRESULT res = SP05_LoadProdInfo(&u32Magic, sizeof(u32Magic), MISCDATA_PHASECHECK_OFFET, m_dwTimeOut);
    if (SP_OK == res)
    {
        if (SP09_SPPH_MAGIC_NUMBER == u32Magic)
        {
            SetProperty(SP_ATTR_MAGIC_NUMBER, 0, (LPCVOID)SP09_SPPH_MAGIC_NUMBER);

            SP09_PHASE_CHECK_T sp09;
            res = SP09_LoadProdInfo((void*)& sp09, SP09_MAX_PHASE_BUFF_SIZE, m_dwTimeOut);
            if (SP_OK == res)
            {
                uint32 u32CopyLen = u32SNLength > SP09_MAX_SN_LEN - 1 ? SP09_MAX_SN_LEN - 1 : u32SNLength;
                if (1 == nID)
                {
                    CopyMemory(lpSN, sp09.SN1, u32CopyLen);
                }
                else
                {
                    CopyMemory(lpSN, sp09.SN2, u32CopyLen);
                }
            }
        }
        else if (SP15_SPPH_MAGIC_NUMBER == u32Magic)
        {
            SetProperty(SP_ATTR_MAGIC_NUMBER, 0, (LPCVOID)SP15_SPPH_MAGIC_NUMBER);

            SP15_PHASE_CHECK_T sp15;
            res = SP09_LoadProdInfo((void*)& sp15, SP15_MAX_PHASE_BUFF_SIZE, m_dwTimeOut);
            if (SP_OK == res)
            {
                uint32 u32CopyLen = u32SNLength > SP15_MAX_SN_LEN - 1 ? SP15_MAX_SN_LEN - 1 : u32SNLength;
                if (1 == nID)
                {
                    CopyMemory(lpSN, sp15.SN1, u32CopyLen);
                }
                else
                {
                    CopyMemory(lpSN, sp15.SN2, u32CopyLen);
                }
            }
        }
        else if (SP05_SPPH_MAGIC_NUMBER == u32Magic)
        {
            SetProperty(SP_ATTR_MAGIC_NUMBER, 0, (LPCVOID)SP05_SPPH_MAGIC_NUMBER);

            SP05_PHASE_CHECK_T sp05;
            res = SP05_LoadProdInfo((void*)& sp05, SP05_MAX_PHASE_BUFF_SIZE, MISCDATA_PHASECHECK_OFFET, m_dwTimeOut);
            if (SP_OK == res)
            {
                uint32 u32CopyLen = u32SNLength > SP05_MAX_SN_LEN - 1 ? SP05_MAX_SN_LEN - 1 : u32SNLength;
                if (1 == nID)
                {
                    CopyMemory(lpSN, sp05.header.SN, u32CopyLen);
                }
                else
                {
                    CopyMemory(lpSN, sp05.header.SN2, u32CopyLen);
                }
            }
        }
        else if (((u32Magic & 0xFF000000) >> 24) == 'G' && ((u32Magic & 0x00FF0000) >> 16) == 'S')
        {
            LogFmtStrA(SPLOGLV_WARN, "This is a golden sample! %x", u32Magic);
            return SP_E_PHONE_GOLDEN_SAMPLE;
        }
        else
        {
            LogFmtStrA(SPLOGLV_ERROR, "Unknown PHASECHECK! %x", u32Magic);
            return SP_E_PHONE_INVALID_PHASECHECK_MAGIC_NUMBER;
        }
    }

    LogFmtStrA(SPLOGLV_INFO, "SN%d: %s", nID, lpSN);
    return res;
}

SPRESULT CCommCmd::SaveSN(int nID, LPCSTR lpSN, uint32 u32SNLength)
{
    if (NULL == lpSN || IsBadReadPtr(lpSN, u32SNLength))
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid parameters!", __FUNCTION__);
        return SP_E_PHONE_INVALID_PARAMETER;
    }

    LogFmtStrA(SPLOGLV_INFO, "Save SN%d: %s", nID, lpSN);
    u32SNLength = strlen(lpSN);

    uint32 u32Magic = 0;
    SPRESULT res = SP05_LoadProdInfo(&u32Magic, sizeof(u32Magic), MISCDATA_PHASECHECK_OFFET, m_dwTimeOut);
    if (SP_OK == res)
    {
        if (SP05_SPPH_MAGIC_NUMBER == u32Magic)
        {
            SetProperty(SP_ATTR_MAGIC_NUMBER, 0, (LPCVOID)SP05_SPPH_MAGIC_NUMBER);

            if (u32SNLength >= SP05_MAX_SN_LEN)
            {
                LogFmtStrA(SPLOGLV_ERROR, "Input SN length is too long %d than %d", u32SNLength, SP05_MAX_SN_LEN);
                return SP_E_PHONE_INVALID_SN_LENGTH;
            }

            SP05_PHASE_CHECK_T sp05;
            res = SP05_LoadProdInfo((void*)& sp05, SP05_MAX_PHASE_BUFF_SIZE, MISCDATA_PHASECHECK_OFFET, m_dwTimeOut);
            if (SP_OK == res)
            {
                CHAR* pDest = (SN1 == nID) ? &(sp05.header.SN[0]) : &(sp05.header.SN2[0]);
                ZeroMemory(pDest, SP05_MAX_SN_LEN);  // Bug 1181412 
                CopyMemory(pDest, lpSN, u32SNLength);

                res = SP05_SaveProdInfo((const void*)& sp05, SP05_MAX_PHASE_BUFF_SIZE, MISCDATA_PHASECHECK_OFFET, m_dwTimeOut);
            }
        }
        else if (SP09_SPPH_MAGIC_NUMBER == u32Magic)
        {
            SetProperty(SP_ATTR_MAGIC_NUMBER, 0, (LPCVOID)SP09_SPPH_MAGIC_NUMBER);

            if (u32SNLength >= SP09_MAX_SN_LEN)
            {
                LogFmtStrA(SPLOGLV_ERROR, "Input SN length is too long %d than %d", u32SNLength, SP09_MAX_SN_LEN);
                return SP_E_PHONE_INVALID_SN_LENGTH;
            }

            SP09_PHASE_CHECK_T sp09;
            res = SP09_LoadProdInfo((void*)& sp09, SP09_MAX_PHASE_BUFF_SIZE, m_dwTimeOut);
            if (SP_OK == res)
            {
                CHAR* pDest = (SN1 == nID) ? &(sp09.SN1[0]) : &(sp09.SN2[0]);
                ZeroMemory(pDest, SP09_MAX_SN_LEN);  // Bug 1181412 
                CopyMemory(pDest, lpSN, u32SNLength);

                res = SP09_SaveProdInfo((const void*)& sp09, SP09_MAX_PHASE_BUFF_SIZE, m_dwTimeOut);
            }
        }
        else if (SP15_SPPH_MAGIC_NUMBER == u32Magic)
        {
            SetProperty(SP_ATTR_MAGIC_NUMBER, 0, (LPCVOID)SP15_SPPH_MAGIC_NUMBER);

            SP15_PHASE_CHECK_T sp15;
            res = SP09_LoadProdInfo((void*)& sp15, SP15_MAX_PHASE_BUFF_SIZE, m_dwTimeOut);
            if (SP_OK == res)
            {
                if (u32SNLength >= SP15_MAX_SN_LEN)
                {
                    LogFmtStrA(SPLOGLV_ERROR, "Input SN length is too long %d than %d", u32SNLength, SP15_MAX_SN_LEN);
                    return SP_E_PHONE_INVALID_SN_LENGTH;
                }

                CHAR* pDest = (SN1 == nID) ? &(sp15.SN1[0]) : &(sp15.SN2[0]);
                ZeroMemory(pDest, SP15_MAX_SN_LEN);  // Bug 1181412 
                CopyMemory(pDest, lpSN, u32SNLength);

                res = SP09_SaveProdInfo((const void*)& sp15, SP15_MAX_PHASE_BUFF_SIZE, m_dwTimeOut);
            }
        }
        /*
        // Allow to refresh golden sample
        else if (((u32Magic & 0xFF000000) >> 24) == 'G' && ((u32Magic & 0x00FF0000) >> 16) == 'S')
        {
            LogFmtStrA(SPLOGLV_WARN, "This is a golden sample! %x", u32Magic);
            return SP_E_PHONE_GOLDEN_SAMPLE;
        }
        */
        else
        {
            res = InitPhaseCheck(((SN1 == nID) ? lpSN : NULL), ((SN2 == nID) ? lpSN : NULL));
        }
    }

    return res;
}

SPRESULT CCommCmd::BBAutoTest(
    int     nCmd,
    LPCVOID lpData,
    uint32  u32BytesToWrite,
    LPVOID  lpBuff,
    uint32  u32BytesToRead,
    uint32  u32TimeOut
)
{
    LogFmtStrA(SPLOGLV_INFO, "BBAT: Cmd = 0x%X, TimeOut = %d", nCmd, u32TimeOut);

    DeclareDiagHeader(wh, DIAG_DEVICE_AUTOTEST_F, (uint8)nCmd);
    DeclareDiagHeader(rh, DIAG_DEVICE_AUTOTEST_F, 0x00);

    SPRESULT res = SendAndRecv(wh, (const void*)lpData, u32BytesToWrite, rh, lpBuff, u32BytesToRead, NULL, u32TimeOut);
    if (SP_OK == res && 0x00 != rh.subtype)
    {
        LogFmtStrA(SPLOGLV_ERROR, "BBAT: Cmd = 0x%X execute fail,, Ack code = 0x%X", nCmd, rh.subtype);
        res = SP_E_PHONE_BBAT_COMMAND_FAIL;
    }

    return res;
}

#pragma region FM_API

SPRESULT CCommCmd::fmOpen(BOOL bOpen)
{
    LogFmtStrA(SPLOGLV_INFO, "%s:", bOpen ? "Open FM:" : "Close FM:");

    ///
    DeclareDiagHeader(hd, DIAG_FM_TEST_F, FM_CMD_STATE);
    uint8    nCmd = (uint8)(bOpen ? 0x01 : 0x00);
    uint8 u8State = 0xFF;
    SPRESULT  res = SendAndRecv(hd, (const void*)& nCmd, sizeof(nCmd), hd, (void*)& u8State, sizeof(u8State), NULL, m_dwTimeOut);
    if (SP_OK == res)
    {
        if (FM_REG_SUCCESS != u8State)
        {
            LogFmtStrA(SPLOGLV_ERROR, "%s FM failed, state = %d!", bOpen ? "Open" : "Close", u8State);
            return SP_E_PHONE_INVALID_STATE;
        }
    }

    return res;
}

SPRESULT CCommCmd::fmSetVolume(int nVolume)
{
    SPRESULT  res = 0;
    uint8 u8State = 0xFF;

    if (nVolume <= 0)
    {
        /// MUTE
        LogFmtStrA(SPLOGLV_INFO, "Set FM volume to mute: ");

        DeclareDiagHeader(hd, DIAG_FM_TEST_F, FM_CMD_MUTE);
        res = SendAndRecv(hd, NULL, 0, hd, (void*)& u8State, 1, NULL, m_dwTimeOut);
    }
    else
    {
        LogFmtStrA(SPLOGLV_INFO, "Set FM volume to level %d", nVolume);

        DeclareDiagHeader(hd, DIAG_FM_TEST_F, FM_CMD_VOLUME);
        uint8 u8Vol = (uint8)nVolume;
        res = SendAndRecv(hd, (const void*)& u8Vol, sizeof(u8Vol), hd, (void*)& u8State, 1, NULL, m_dwTimeOut);
    }

    if (SP_OK == res)
    {
        if (FM_REG_SUCCESS != u8State)
        {
            LogFmtStrA(SPLOGLV_ERROR, "FM %s failed, state = %d", (nVolume <= 0) ? "Set mute" : "Set volume", u8State);
            return SP_E_PHONE_INVALID_STATE;
        }
    }

    return res;
}

SPRESULT CCommCmd::fmGetTuneInfo(uint32 u32freq, LPFM_SIGNAL_PARAM_T lpInfo)
{
    CheckValidPointer(lpInfo);

    LogFmtStrA(SPLOGLV_INFO, "Retrieve the signal information of specified frequency: %d KHz", u32freq);

    DeclareDiagHeader(hd, DIAG_FM_TEST_F, FM_CMD_TUNE);
    uint32 u32KHz = Convert32((uint32)u32freq);
    uint32 u32revSize = 0;
    SPRESULT  res = SendAndRecv(hd, (const void*)& u32KHz, sizeof(u32KHz), hd, (void*)m_diagBuff, sizeof(m_diagBuff), &u32revSize, m_dwTimeOut);
    if (SP_OK == res)
    {
        uint32 u32ExpSize = sizeof(DIAG_FM_SIGNAL_PARAM_T);
        CheckResponseLength(u32revSize, u32ExpSize);

        DIAG_FM_SIGNAL_PARAM_T* pRLT = (DIAG_FM_SIGNAL_PARAM_T*)m_diagBuff;
        lpInfo->result = (unsigned char)pRLT->nOperInd;
        lpInfo->stereo = (unsigned char)pRLT->nStereoInd;
        lpInfo->rssi = Convert16(pRLT->nRssi);
        lpInfo->freq = Convert32(pRLT->nFrequency);
        lpInfo->powerind = Convert32(pRLT->nPowerIndicator);
        lpInfo->freqoffset = Convert32(pRLT->nFreqOffset);
        lpInfo->pilotDet = Convert32(pRLT->nPilotDet);
        lpInfo->nodaclpf = Convert32(pRLT->nNoDacLpf);

        LogFmtStrA(SPLOGLV_INFO, "FM Tuning Info: freq = %d kHz, stereo = %d, rssi = %d, power = %d, freq.offset = %d", \
            lpInfo->freq, lpInfo->stereo, lpInfo->rssi, lpInfo->powerind, lpInfo->freqoffset);
    }

    return res;
}

SPRESULT CCommCmd::fmSeekChannel(uint32 u32freq, BOOL bDirection, LPFM_SIGNAL_PARAM_T lpInfo)
{
    CheckValidPointer(lpInfo);

    LogFmtStrA(SPLOGLV_INFO, "Seek the next valid channel: %d KHz, %s", u32freq, bDirection ? "Backward" : "Forward");

    DeclareDiagHeader(hd, DIAG_FM_TEST_F, FM_CMD_SEEK);
    uint32	u32KHz = Convert32((uint32)u32freq);
    uint8	nDrt = (uint8)(bDirection ? 0x01 : 0x00);
    uint8	buff[5] = { 0 };
    memcpy((void*)& buff[0], (const void*)& u32KHz, sizeof(u32KHz));
    buff[4] = nDrt;

    uint32 u32revSize = 0;
    SPRESULT  res = SendAndRecv(hd, (const void*)& buff[0], sizeof(buff), hd, (void*)m_diagBuff, sizeof(m_diagBuff), &u32revSize, m_dwTimeOut);
    if (SP_OK == res)
    {
        uint32 u32ExpSize = sizeof(DIAG_FM_SIGNAL_PARAM_T);
        CheckResponseLength(u32revSize, u32ExpSize);

        DIAG_FM_SIGNAL_PARAM_T* pRLT = (DIAG_FM_SIGNAL_PARAM_T*)m_diagBuff;
        lpInfo->result = (unsigned char)pRLT->nOperInd;
        lpInfo->stereo = (unsigned char)pRLT->nStereoInd;
        lpInfo->rssi = Convert16(pRLT->nRssi);
        lpInfo->freq = Convert32(pRLT->nFrequency);
        lpInfo->powerind = Convert32(pRLT->nPowerIndicator);
        lpInfo->freqoffset = Convert32(pRLT->nFreqOffset);
        lpInfo->pilotDet = Convert32(pRLT->nPilotDet);
        lpInfo->nodaclpf = Convert32(pRLT->nNoDacLpf);

        LogFmtStrA(SPLOGLV_INFO, "FM Seek: freq = %d KHz, stereo = %d, rssi = %d, power = %d, freq.offset = %d", \
            lpInfo->freq, lpInfo->stereo, lpInfo->rssi, lpInfo->powerind, lpInfo->freqoffset);
    }

    return res;
}

SPRESULT CCommCmd::fmLoadRegister(uint32 u32Addr, uint32 u32Count, uint32* lpRegData)
{
    CheckValidPointer(lpRegData);

    LogFmtStrA(SPLOGLV_INFO, "Load FM register: Addr = 0x%04X, Count = %d", u32Addr, u32Count);

    DIAG_FM_RW_REG_T    req;
    ZeroMemory((void*)& req, sizeof(req));
    DeclareDiagHeader(hd, DIAG_FM_TEST_F, FM_CMD_READ_REG);
    req.nStartAddr = Convert32((uint32)u32Addr);
    req.nUnitCount = Convert32((uint32)u32Count);

    uint32 u32revSize = 0;
    SPRESULT  res = SendAndRecv(hd, (const void*)& req, sizeof(req), hd, (void*)m_diagBuff, sizeof(m_diagBuff), &u32revSize, m_dwTimeOut);
    if (SP_OK == res)
    {
        /// DIAG_FM_RW_REG_T + uint32*N
        uint32 u32ExpSize = sizeof(req) + sizeof(uint32) * u32Count;
        CheckResponseLength(u32revSize, u32ExpSize);

        DIAG_FM_RW_REG_T* pRLT = (DIAG_FM_RW_REG_T*)& m_diagBuff[0];
        uint32 u32State = Convert32(pRLT->nErrorCode);
        if (FM_REG_SUCCESS != u32State)
        {
            LogFmtStrA(SPLOGLV_ERROR, "Load FM register failed, state = %d", u32State);
            return SP_E_PHONE_INVALID_STATE;
        }

        uint32* pData = (uint32*)(m_diagBuff + sizeof(DIAG_FM_RW_REG_T));
        for (uint32 i = 0; i < u32Count; i++)
        {
            lpRegData[i] = Convert32(pData[i]);
            LogFmtStrA(SPLOGLV_INFO, "FM register [%4d] = 0x%04X", i, lpRegData[i]);
        }
    }

    return res;
}

SPRESULT CCommCmd::fmSaveRegister(uint32 u32Addr, uint32 u32Count, const uint32* lpRegData)
{
    CheckValidPointer(lpRegData);

    LogFmtStrA(SPLOGLV_INFO, "Save FM register: Addr = 0x%04X, Count = %d", u32Addr, u32Count);

    DIAG_FM_RW_REG_T    req;
    ZeroMemory((void*)& req, sizeof(req));
    DeclareDiagHeader(hd, DIAG_FM_TEST_F, FM_CMD_WRITE_REG);
    req.nStartAddr = Convert32((uint32)u32Addr);
    req.nUnitCount = Convert32((uint32)u32Count);

    /// DIAG_FM_RW_REG_T + uint32*N
    const uint32 u32SendSize = sizeof(req) + sizeof(uint32) * u32Count;
    uint8* lpData = NULL;

    try
    {
        lpData = new uint8[u32SendSize];
    }
    catch (const std::bad_alloc& /*e*/)
    {
        lpData = NULL;
    }

    if (NULL == lpData)
    {
        LogFmtStrA(SPLOGLV_ERROR, "Memory alloc failed! Size = %d", u32SendSize);
        return SP_E_PHONE_ALLOC_MEMORY;
    }

    memcpy((void*)lpData, (const void*)& req, sizeof(req));
    void* p = (void*)(lpData + sizeof(req));
    memcpy((void*)p, (const void*)lpRegData, sizeof(uint32) * u32Count);
    Convert32((uint8*)p, sizeof(uint32) * u32Count);

    uint32 u32revSize = 0;
    SPRESULT  res = SendAndRecv(hd, (const void*)lpData, u32SendSize, hd, (void*)m_diagBuff, sizeof(m_diagBuff), &u32revSize, m_dwTimeOut);
    delete[]lpData;
    lpData = NULL;

    if (SP_OK == res)
    {
        uint32 u32ExpSize = sizeof(DIAG_FM_RW_REG_T);
        CheckResponseLength(u32revSize, u32ExpSize);

        DIAG_FM_RW_REG_T* pRLT = (DIAG_FM_RW_REG_T*)& m_diagBuff[0];
        uint32 u32State = Convert32(pRLT->nErrorCode);
        if (FM_REG_SUCCESS != u32State)
        {
            LogFmtStrA(SPLOGLV_ERROR, "Save FM register failed, state = %d", u32State);
            return SP_E_PHONE_INVALID_STATE;
        }
    }

    return res;
}
#pragma endregion

SPRESULT CCommCmd::DeepSleep(void)
{
    LogRawStrA(SPLOGLV_INFO, "Enter DeepSleep mode:");

    /*
        Android P&Q: Send 7E 7E to PC and create a thread to start deep sleep
        http://*********:8081/xref/sprdroidq_trunk/vendor/sprd/proprietories-source/engpc/modules/libapdeepsleep/app_deep_sleep_cmd.c#98
    */
    DeclareDiagHeader(hd, DIAG_CURRENT_TEST_F, CURRENT_TEST_DEEP_SLEEP);
    return SendCmd(hd, NULL, 0);
}

SPRESULT CCommCmd::PowerOff(void)
{
    LogRawStrA(SPLOGLV_INFO, "Power off:");

    DeclareDiagHeader(hd, DIAG_CURRENT_TEST_F, CURRENT_TEST_POWER_OFF);
    hd.sn = 0x00;
    SPRESULT res = SendCmd(hd, NULL, 0);
    /*
        According to SW guys.
        - feature phone(SC6531E), DUT will continue to power off with no ack to pc when CURRENT_TEST_POWER_OFF command is received.
        - smart phone:
          1. Android P: Invoke reboot(LINUX_REBOOT_CMD_POWER_OFF) directly
          2. Android Q: Send ack to PC and start to power off until usb is plugged out
             http://*********:8081/xref/sprdroidq_trunk/vendor/sprd/proprietories-source/engpc/modules/libreboot_cmd/reboot_cmd.cpp#eng_diag_poweroff
    */
    //  Safe remove device: 
    //  Make sure port is closed before device is removed
    //  Otherwise PC USB host perhaps hang dead and restore unless reboot PC.
    if (SP_OK == res)
    {
        Close();
    }

    return res;
}

SPRESULT CCommCmd::AssertUE(void)
{
    LogRawStrA(SPLOGLV_INFO, "Assert UE!");
    if (!Opend())
    {
        CHKRESULT(Open(&m_ca));
    }

    DeclareDiagHeader(hd, DIAG_SYSTEM_F, 0x04/*DIAG_ASSERT_MS*/);
    return SendCmd(hd, NULL, 0);
}

SPRESULT CCommCmd::GetADC(LPPC_ADC_T lpADC)
{
    if (NULL == lpADC)
    {
        LogFmtStrA(SPLOGLV_INFO, "%s: Invalid parameters!", __FUNCTION__);
        return SP_E_PHONE_INVALID_PARAMETER;
    }

    DeclareDiagHeader(hd, DIAG_ADC_F, lpADC->nChannel);
    DeclareDiagHeader(rd, DIAG_ADC_F, ZERO_SUBTYPE);

    uint32 recvLen = 0;
    SPRESULT res = SendAndRecv(hd, NULL, 0, rd, (void*)m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);
    if (SP_OK == res)
    {
        if (recvLen < 4)
        {
            LogFmtStrA(SPLOGLV_ERROR, "Invalid response data length %d.", recvLen);
            return SP_E_PHONE_INVALID_LENGTH;
        }

        uint16 adc = *((uint16*)& m_diagBuff[0]);
        lpADC->adc = Convert16(adc);
        lpADC->flag = m_diagBuff[2];
        lpADC->nvoffset = m_diagBuff[3];

        LogFmtStrA(SPLOGLV_INFO, "ADC [%d] = %d", lpADC->nChannel, lpADC->adc);
    }

    return res;
}
// simlock
SPRESULT CCommCmd::simlockHashSign(LPBYTE lpSendBuffer, UINT32 nSendLen)
{
    if (NULL == lpSendBuffer || 0 == nSendLen)
    {
        LogFmtStrA(SPLOGLV_ERROR, "simlockHashSign: Invalid parameters!");
        return SP_E_PHONE_INVALID_PARAMETER;
    }

    LogFmtStrA(SPLOGLV_INFO, "simlockHashSign: ");

    DeclareDiagHeader(hd, 9, 7);
    ZeroMemory((void*)& m_diagBuff, sizeof(m_diagBuff));
    uint32 recvLen = 0;
    SPRESULT res = SendAndRecv(hd, (const void*)lpSendBuffer, nSendLen, hd, (void*)m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);

    if (SP_OK == res)
    {
        if (1 == m_diagBuff[0])
        {
            LogFmtStrA(SPLOGLV_INFO, "Write has sign succeed.");
            res = SP_OK;
        }
        else
        {
            LogFmtStrA(SPLOGLV_ERROR, "Write hash sign failed,return code is %d.", m_diagBuff[0]);
            res = SP_E_PHONE_SEND_DATA;
        }
    }
    return res;
}

SPRESULT CCommCmd::simlockGetHash(LPBYTE lpRecvBuffer, UINT32* nRecvLen)
{
    LogFmtStrA(SPLOGLV_INFO, "simlock get hash value: ");

    DeclareDiagHeader(hd, 9, 6);
    ZeroMemory((void*)& m_diagBuff, sizeof(m_diagBuff));
    uint32 recvLen = 0;
    SPRESULT res = SendAndRecv(hd, NULL, 0, hd, (void*)m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);

    if (SP_OK == res)
    {
        if (20 == recvLen || 32 == recvLen)
        {
            *nRecvLen = recvLen;
            memcpy(lpRecvBuffer, m_diagBuff, recvLen);
            LogFmtStrA(SPLOGLV_INFO, "Simlock Get Hash succeed.");
            res = SP_OK;
        }
        else
        {
            LogFmtStrA(SPLOGLV_ERROR, "Simlock Get hash is not invalid Hash.", m_diagBuff[0]);
            res = SP_E_PHONE_INVALID_DATA;
        }
    }

    return res;
}

SPRESULT CCommCmd::ReadNV(uint16 u16NvID, LPVOID lpData, uint32 u32BytesToRead, uint32* lpu32RecvLen)
{
    if (NULL == lpData || 0 == u32BytesToRead || IsBadWritePtr(lpData, u32BytesToRead))
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid parameters!", __FUNCTION__);
        return SP_E_PHONE_INVALID_PARAMETER;
    }

    LogFmtStrA(SPLOGLV_INFO, "%s: ID = %d", __FUNCTION__, u16NvID);
    if (NULL != lpu32RecvLen)
    {
        *lpu32RecvLen = 0;
    }

    DeclareDiagHeader(whd, DIAG_NVITEM_F, NVITEM_READ);
    DeclareDiagHeader(rhd, DIAG_NVITEM_F, ZERO_SUBTYPE);

    uint16 uID = Convert16((uint16)u16NvID);

    uint32 recvLen = 0;
    SPRESULT res = SendAndRecv(whd, (const void*)& uID, sizeof(uID), rhd, (void*)& m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);
    if (SP_OK == res)
    {
        // Response Data: ID(2bytes) + data(n bytes)
        if (recvLen <= 2 /* sizeof(ID)*/)
        {
            LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid response length %d!", __FUNCTION__, recvLen);
            return SP_E_PHONE_INVALID_LENGTH;
        }

        uint32 dataLen = recvLen - 2 /* sizeof(ID)*/;
        if (NULL != lpu32RecvLen)
        {
            // Actual nv data size
            *lpu32RecvLen = dataLen;
        }

        if (dataLen > u32BytesToRead)
        {
            LogFmtStrA(SPLOGLV_WARN, "nv %d total size is %d bytes, only %d bytes are copied", u16NvID, dataLen, u32BytesToRead);
            CopyMemory(lpData, (const void*)& m_diagBuff[2/*ID*/], u32BytesToRead);
        }
        else
        {
            CopyMemory(lpData, (const void*)& m_diagBuff[2/*ID*/], dataLen);
        }
    }

    return res;
}

SPRESULT CCommCmd::WriteNV(uint16 u16NvID, LPCVOID lpData, uint32 u32BytesToWrite)
{
    LogFmtStrA(SPLOGLV_INFO, "WriteNV: ID = %d", u16NvID);

    if (NULL == lpData || 0 == u32BytesToWrite || IsBadReadPtr(lpData, u32BytesToWrite))
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid parameters!", __FUNCTION__);
        return SP_E_PHONE_INVALID_PARAMETER;
    }

    DeclareDiagHeader(whd, DIAG_NVITEM_F, NVITEM_WRITE);
    DeclareDiagHeader(rhd, DIAG_NVITEM_F, ZERO_SUBTYPE);

    uint16 uID = Convert16((uint16)u16NvID);

    // Write Data Format: ID(2bytes) + data(n bytes)
    ZeroMemory((void*)& m_diagBuff, sizeof(m_diagBuff));
    CopyMemory((void*)& m_diagBuff, (const void*)& uID, sizeof(uID));
    CopyMemory((void*)(m_diagBuff + 2/*ID*/), lpData, u32BytesToWrite);

    return SendAndRecv(whd, (const void*)& m_diagBuff, (u32BytesToWrite + sizeof(uID)), rhd, NULL, 0, NULL, m_dwTimeOut);
}

SPRESULT CCommCmd::SyncNV(uint32 u32TimeOut)
{
    LogFmtStrA(SPLOGLV_INFO, "%s: %d", __FUNCTION__, u32TimeOut);
    DeclareDiagHeader(wh, DIAG_NVITEM_F, NVITEM_SYNC_FLASH);
    DeclareDiagHeader(rh, DIAG_NVITEM_F, 0x00);

    return SendAndRecv(wh, NULL, 0, rh, NULL, 0, NULL, u32TimeOut);
}

SPRESULT CCommCmd::LoadMiscData(uint32 u32Offset, LPVOID lpBuff, uint32 u32BytesToRead, uint32 u32Timeout)
{
    if (NULL == lpBuff || IsBadWritePtr(lpBuff, u32BytesToRead))
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid parameters!", __FUNCTION__);
        return SP_E_PHONE_INVALID_PARAMETER;
    }

    return SP05_LoadProdInfo(lpBuff, u32BytesToRead, u32Offset, u32Timeout);
}

SPRESULT CCommCmd::SaveMiscData(uint32 u32Offset, LPCVOID lpBuff, uint32 u32BytesToWrite, uint32 u32TimeOut)
{
    if (NULL == lpBuff || IsBadReadPtr(lpBuff, u32BytesToWrite))
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid parameters!", __FUNCTION__);
        return SP_E_PHONE_INVALID_PARAMETER;
    }

    return SP05_SaveProdInfo(lpBuff, u32BytesToWrite, u32Offset, u32TimeOut);
}

//////////////////////////////////////////////////////////////////////////
//				LTE Modem V3 Interface
//  NV Program
//////////////////////////////////////////////////////////////////////////
SPRESULT CCommCmd::ModemV3_Nv_Read(const PC_MODEM_RF_V3_DATA_REQ_CMD_T* nv, PC_MODEM_RF_V3_DATA_PARAM_T* pNvRlst)
{
    CheckValidPointer(nv);
    CheckValidPointer(pNvRlst);

    LogFmtStrA(SPLOGLV_INFO, "Load Modem V3 NV: nv = %d", nv->eNvType);

    L1_MODEM_RF_V3_NV_READ_REQ_T L1;
    ZeroMemory((void*)& L1, sizeof(L1));

    L1.head.SubCmdCode = Convert16(NVM_RF_READ_REQ);
    L1.head.SubCmdSize = Convert16(sizeof(L1_MODEM_RF_V3_NV_READ_REQ_T));

    L1.NvmConf.NvType = Convert16((uint16)nv->eNvType);
    L1.NvmConf.BandId = (uint8)(nv->BandId);
    L1.NvmConf.BwId = nv->BwId;
    L1.NvmConf.HwChanId = nv->HwChanID;
    L1.NvmConf.AntId = nv->AntId;
    L1.NvmConf.DataOffset = nv->DataOffset;
    L1.NvmConf.DataSize = nv->DataSize;

    DeclareDiagHeader(hd, DIAG_BOCA_F, ZERO_SUBTYPE);

    uint32 recvLen = 0;
    SPRESULT res = SendAndRecv(hd, (const void*)& L1, sizeof(L1), hd, (void*)m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);
    if (SP_OK == res)
    {

        L1_MODEM_V3_NV_READ_T* pRLT = (L1_MODEM_V3_NV_READ_T*)(m_diagBuff);
        int OpCode = Convert32(pRLT->op_code);
        uint32 nDataSize = Convert32(pRLT->size);
        uint32 nTotalBufSize = nDataSize + sizeof(OpCode) + sizeof(pRLT->size) + sizeof(L1_SUBCMD_HEAD_T);
        if (OpCode != SP_OK)
        {
            LogFmtStrA(SPLOGLV_ERROR, "ModV3LoadNV failed opCode = %d", OpCode);
            return SP_E_PHONE_INVALID_STATE;
        }

        if (nDataSize > MAX_MODEM_V3_L1_NV_LEN)
        {
            LogFmtStrA(SPLOGLV_ERROR, "ModV3LoadNV Invalid Data size: %d > Max Size: %d", pNvRlst->DataSize, MAX_LTE_L1_NV_LEN_V3);
            return SP_E_PHONE_INVALID_LENGTH;
        }
        else if (recvLen != nTotalBufSize)
        {
            LogFmtStrA(SPLOGLV_ERROR, "ModV3LoadNV Invalid response size: %d !=  %d", recvLen, nTotalBufSize);
            return SP_E_PHONE_INVALID_LENGTH;
        }
        pNvRlst->DataSize = nDataSize;

        memcpy(&pNvRlst->nData[0], &pRLT->data, pNvRlst->DataSize);
    }

    return res;
}

SPRESULT CCommCmd::Security_Read_SimlockNVData(uint8& pstrBuff, uint32& nBuffSize)
{

    unsigned int res = SP_E_SECURITY_COMM_PRI_QUERY_VERSION_FAILED;
    nBuffSize = 0;
    int nStatus = 0;
    while (nStatus == 0)
    {
        DeclareDiagHeader(whd, DIAG_EXTEND_CMD, DIAG_SECURITY_SIMLOCKNVDATA);
        DeclareDiagHeader(rhd, DIAG_EXTEND_CMD, DIAG_SECURITY_SIMLOCKNVDATA);
        uint32 recvLen = 0;
        DIAG_FILE_DUMP_REQ_T req;
        req.cmd = Convert32(2);
        req.reserved = 0;
        uint8  byDiagBuff[MAX_DIAG_BUFF_SIZE] = { 0 };
        LogFmtStrA(SPLOGLV_VERBOSE, "Security_Read_SimlockNVData");
        SPRESULT res = SendAndRecv(whd, (const void*)&req, sizeof(req), rhd, (void*)&byDiagBuff, sizeof(byDiagBuff), &recvLen, m_dwTimeOut);
        if (SP_OK == res)
        {
            LogFmtStrA(SPLOGLV_VERBOSE, "Security_Read_SimlockNVData ok");
            if (recvLen <= 2 /* sizeof(ID)*/)
            {
                LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid response length %d!,length need Greater than 2", __FUNCTION__, recvLen);
                return SP_E_PHONE_INVALID_LENGTH;
            }

            read_partition_act_t TreadPartitionAct;
            vector<int8> arrCmd(10, 0);
            memcpy(arrCmd.data(), byDiagBuff, sizeof(int));
            vector<int8> arrDataStatus(10, 0);
            memcpy(arrDataStatus.data(), byDiagBuff + 4, 4);
            vector<int8> arrDataLength(10, 0);
            memcpy(arrDataLength.data(), byDiagBuff + 8, 4);
            TreadPartitionAct.cmd = strtol(arrCmd.data(),0,16);
            TreadPartitionAct.status = strtol(arrDataStatus.data(), 0, 16);
            TreadPartitionAct.data_len = strtol(arrDataLength.data(), 0, 16);
            nStatus = TreadPartitionAct.status;
            vector<uint8> arrDataList(TreadPartitionAct.data_len, 0);
            
            if (TreadPartitionAct.data_len > 0)
            {
                memcpy(arrDataList.data(), byDiagBuff + 12, TreadPartitionAct.data_len);
                memcpy(&pstrBuff + nBuffSize, arrDataList.data(), TreadPartitionAct.data_len);
            }
            else
            {
                LogFmtStrA(SPLOGLV_ERROR, "Read_SimlockNVData length error， data_len need Greater than 0");
            }

            nBuffSize += TreadPartitionAct.data_len;

            LogFmtStrA(SPLOGLV_VERBOSE, "Read_SimlockNVData status = %d", nStatus);
            if (nStatus == 1)
            {
                return SP_OK;
            }
        }
    }

    return res;
}


SPRESULT CCommCmd::ModemV3_Nv_Write(const PC_MODEM_RF_V3_DATA_REQ_CMD_T* pNvReq, PC_MODEM_RF_V3_DATA_PARAM_T* pNvData)
{
    CheckValidPointer(pNvReq);
    CheckValidPointer(pNvData);

    LogFmtStrA(SPLOGLV_INFO, "Modem V3 Save NV: nv = %d", pNvReq->eNvType);

    L1_MODEM_RF_V3_NV_WRITE_REQ_T L1;
    ZeroMemory((void*)& L1, sizeof(L1));

    L1.head.SubCmdCode = Convert16(NVM_RF_WRITE_REQ);
    L1.head.SubCmdSize = Convert16(sizeof(L1_MODEM_RF_V3_NV_WRITE_REQ_T) + (uint16)pNvData->DataSize);

    L1.NvmConf.NvType = (uint16)(pNvReq->eNvType);
    L1.NvmConf.BandId = (uint8)(pNvReq->BandId);
    L1.NvmConf.HwChanId = (uint8)(pNvReq->HwChanID);
    L1.NvmConf.BwId = pNvReq->BwId;
    L1.NvmConf.DataSize = (uint16)(pNvData->DataSize);
    L1.NvmConf.DataOffset = (uint16)(pNvReq->DataOffset);

    DeclareDiagHeader(hd, DIAG_BOCA_F, ZERO_SUBTYPE);

    if (pNvData->DataSize > MAX_MODEM_V3_L1_NV_LEN)
    {
        LogFmtStrA(SPLOGLV_ERROR, "SP_lteModV3SaveNV Invalid Data size: %d > Max Size: %d", pNvData->DataSize, MAX_LTE_L1_NV_LEN_V3);
        return SP_E_PHONE_INVALID_LENGTH;
    }

    uint8* pBuff = NULL;
    try
    {
        pBuff = new uint8[L1.head.SubCmdSize];
    }
    catch (const std::bad_alloc& /*e*/)
    {
        pBuff = NULL;
    }
    if (NULL == pBuff)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Memory allocation failed.", __FUNCTION__);
        return SP_E_PHONE_ALLOC_MEMORY;
    }

    uint8* pPos = pBuff;

    uint16 usLen = sizeof(L1);
    memcpy(pPos, &L1, usLen);
    //usLen = sizeof(L1_MODEM_RF_V3_NV_WRITE_REQ_T);
    pPos += usLen;
    usLen = (uint16)pNvData->DataSize;
    memcpy(pPos, &pNvData->nData[0], usLen);
    uint32 usLength = L1.head.SubCmdSize;
    uint32 recvLen = 0;

    SPRESULT ret = SendAndRecv(hd, (const void*)pBuff, usLength, hd, (void*)m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);
    delete[] pBuff;
    if (SP_OK == ret)
    {
        L1_COMMON_RTL_T* pRLT = (L1_COMMON_RTL_T*)m_diagBuff;

        int OpCode = Convert32(pRLT->status);

        if (OpCode != SP_OK)
        {
            LogFmtStrA(SPLOGLV_ERROR, "ModemV3_Nv_Write failed opCode = %d", OpCode);
            return SP_E_PHONE_INVALID_STATE;
        }
    }

    return ret;
}
SPRESULT CCommCmd::ModemV3_Nv_Save2Flash(const PC_MODEM_RF_V3_NV_SAVE_REQ_CMD_T* nv, PC_MODEM_RF_V3_NV_SAVE_RSP_RLST_T* pNvRlst)
{
    CheckValidPointer(nv);
    CheckValidPointer(pNvRlst);
    LogFmtStrA(SPLOGLV_INFO, "ModemV3_Nv_Save2Flash: nv = %d", nv->eNvType);
    L1_MODEM_RF_V3_NV_SAVE2FLASH_REQ_T L1;
    ZeroMemory((void*)& L1, sizeof(L1));
    L1.head.SubCmdCode = Convert16(NVM_RF_SAVE_REQ);
    L1.head.SubCmdSize = Convert16(sizeof(L1_MODEM_RF_V3_NV_SAVE2FLASH_REQ_T));
    L1.NvmConf.eNvType = Convert16((uint16)nv->eNvType);
    L1.NvmConf.eType = Convert16((uint16)nv->eType);
    L1.NvmConf.id_cnt = Convert16((uint16)nv->id_cnt);
    CopyMemory(L1.NvmConf.id_list, nv->id_list, sizeof(nv->id_list));
    DeclareDiagHeader(hd, DIAG_BOCA_F, ZERO_SUBTYPE);
    uint32 recvLen = 0;
    SPRESULT res = SendAndRecv(hd, (const void*)& L1, sizeof(L1), hd, (void*)m_diagBuff, sizeof(m_diagBuff), &recvLen, 90000);
    if (SP_OK == res)
    {
        if (recvLen < sizeof(L1_MODEM_RF_V3_NV_SAVE2FLASH_RSP_T))
        {
            LogFmtStrA(SPLOGLV_ERROR, "%s: Response packet length %d is less than expected %d", __FUNCTION__, recvLen, sizeof(L1_MODEM_RF_V3_NV_SAVE2FLASH_RSP_T));
            return SP_E_PHONE_INVALID_DATA;
        }
        L1_MODEM_RF_V3_NV_SAVE2FLASH_RSP_T* pRLT = (L1_MODEM_RF_V3_NV_SAVE2FLASH_RSP_T*)(m_diagBuff);
        int OpCode = Convert32(pRLT->status);
        if (OpCode != SP_OK)
        {
            LogFmtStrA(SPLOGLV_ERROR, "ModV3LoadNV failed opCode = %d", OpCode);
            return SP_E_PHONE_INVALID_STATE;
        }
        pNvRlst->result = pRLT->result;
        pNvRlst->status = pRLT->status;
    }
    return res;
}

////////////////////////////////////////////////////////////////////////////
//SPRESULT CCommCmd::adbWriteAndRead(const char* pszCmd, char* pszBuffer, int nBufferLen)
//{
//    if (NULL == pszBuffer || NULL == pszCmd || nBufferLen == 0)
//    {
//        LogFmtStrA(SPLOGLV_ERROR, "%s: invalid parameter.", __FUNCTION__);
//        return SP_E_PHONE_INVALID_PARAMETER;
//    }
//
//    if (m_adbBase.GetSN().empty())
//    {
//        char szSN[64] = {0};
//        CHKRESULT(LoadSN(1, szSN, sizeof(szSN)));
//        m_adbBase.SetSN(szSN);
//    }
//
//    if (!m_adbBase.WriteAndReadCmd(pszCmd, pszBuffer, nBufferLen))
//    {
//        LogFmtStrA(SPLOGLV_ERROR, "%s: write and read adb cmd failed.", __FUNCTION__);
//        return SP_E_FAIL;
//    }
//
//    return SP_OK;
//}
//
//SPRESULT CCommCmd::adbWriteCmd(const char* pszCmd)
//{
//    if (NULL == pszCmd)
//    {
//        LogFmtStrA(SPLOGLV_ERROR, "%s: invalid parameter.", __FUNCTION__);
//        return SP_E_PHONE_INVALID_PARAMETER;
//    }
//
//    if (m_adbBase.GetSN().empty())
//    {
//        char szSN[64] = {0};
//        CHKRESULT(LoadSN(1, szSN, sizeof(szSN)));
//        m_adbBase.SetSN(szSN);
//    }
//
//    m_adbBase.WriteCmd(pszCmd);
//
//    return SP_OK;
//}


SPRESULT ReverseAsciiStr(uint8* pData, uint32 uLength, uint8* pBuff, uint32 uBuffSize, uint32* pSize)
{
    uint32 uSize = uLength;
    int nOffset = 0;
    if (uLength % 2)
    {
        uSize++;
        nOffset = 1;
    }

    if (uBuffSize > uSize)
    {
        return SP_ERROR;
    }

    std::vector<uint8> arrData(uSize, 0);
    //  arrData.resize(uSize);

    for (uint32 i = 0; i < uSize / 2; i++)
    {
        arrData[2 * i] = pData[2 * i + 1 - nOffset];
        arrData[2 * i + 1] = pData[2 * i - nOffset];
    }

    if (uLength % 2)
    {
        arrData[1] = 'A';
        arrData[0] = pData[0];
        uBuffSize++;
    }

    *pSize = uSize;
    memcpy_s(pBuff, uBuffSize, arrData.data(), uSize);
    return SP_OK;
}

int Security_String2Numeral(const int8* inputdata, uint8* outputdata, int32 inputdatasize)
{
    int i, j, tmp_len;
    unsigned char tmpData;
    std::string strTmp = inputdata;
    for (i = 0; i < inputdatasize; i++)
    {
        if ((strTmp[i] >= '0') && (strTmp[i] <= '9'))
        {
            tmpData = strTmp[i] - '0';
        }
        else if ((strTmp[i] >= 'A') && (strTmp[i] <= 'F'))
        { //A....F
            tmpData = strTmp[i] - 'A' + 10;
        }
        else if ((strTmp[i] >= 'a') && (strTmp[i] <= 'f'))
        { //a....f
            tmpData = strTmp[i] - 'a' + 10;
        }
        else {
            break;
        }
        strTmp[i] = tmpData;
    }

    for (tmp_len = 0, j = 0; j < i; j += 2)
    {
        outputdata[tmp_len++] = (strTmp[j] << 4) | strTmp[j + 1];
    }

    return tmp_len;
}

int32_t Security_Numeral2String(uint8_t* input,
    int8_t* output,
    int32_t length)
{
    int32_t i = 0;
    char tmp[3];

    for (i = 0; i < length; i++)
    {
        memset(tmp, 0, sizeof(tmp));
        sprintf(tmp, "%02x", input[i]);
        strcat((char*)output, tmp);
    }

    return strlen((char*)output);
}
//////////////////////////////////////////////////////////////////////////
/// GEID
SPRESULT CCommCmd::LoadGEID(PC_GEID_T* lpGEID, uint32 u32TimeOut)
{
    CheckValidPointer(lpGEID);
    LogFmtStrA(SPLOGLV_INFO, "Load GEID (IMEI&MEID): TimeOut = %d ms", u32TimeOut);

    diag_geid_load_req_t req;
    req.cmd = GEID_R; // R
    req.mask = lpGEID->u32Mask;
    DeclareDiagHeader(hd, DIAG_MODEM_SYSTEM_F, GEID_RW_CMD);
    uint32 revSize = 0;
    SPRESULT   res = SendAndRecv(hd, (const void*)& req, sizeof(req), hd, (void*)m_diagBuff, sizeof(m_diagBuff), &revSize, u32TimeOut);
    if (SP_OK == res)
    {
        diag_geid_load_cnf_t* pAck = (diag_geid_load_cnf_t*)m_diagBuff;
        CheckResponseLength(revSize, sizeof(diag_geid_load_cnf_t));
        if (req.cmd != pAck->cmd)
        {
            LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid ACK cmd %d", __FUNCTION__, pAck->cmd);
            return SP_E_PHONE_INVALID_DATA;
        }
        if (0 != pAck->status)
        {
            LogFmtStrA(SPLOGLV_ERROR, "Invalid state %d while loading GEID", pAck->status);
            return SP_E_PHONE_INVALID_STATE;
        }
        uint16 crc = crc16(0, (unsigned char const*)& pAck->geid, sizeof(pAck->geid));
        if (crc != pAck->crc16)
        {
            LogFmtStrA(SPLOGLV_ERROR, "CRC check failed! (calculate)0x%X != (received)0x%X", crc, pAck->crc16);
            return SP_E_PHONE_INVALID_CRC;
        }

        /// IMEI1 - IMEI4
        if (IS_BIT_SET(lpGEID->u32Mask, GEIDMASK_RW_IMEI1))
        {
            res = IMEI_NV2Str(pAck->geid.imei1, (BYTE*)lpGEID->IMEI1);
            LogFmtStrA(SPLOGLV_INFO, "IMEI1 = %s", lpGEID->IMEI1);
            CHKRESULT(res);
        }
        if (IS_BIT_SET(lpGEID->u32Mask, GEIDMASK_RW_IMEI2))
        {
            res = IMEI_NV2Str(pAck->geid.imei2, (BYTE*)lpGEID->IMEI2);
            LogFmtStrA(SPLOGLV_INFO, "IMEI2 = %s", lpGEID->IMEI2);
            CHKRESULT(res);
        }
        if (IS_BIT_SET(lpGEID->u32Mask, GEIDMASK_RW_IMEI3))
        {
            res = IMEI_NV2Str(pAck->geid.imei3, (BYTE*)lpGEID->IMEI3);
            LogFmtStrA(SPLOGLV_INFO, "IMEI3 = %s", lpGEID->IMEI3);
            CHKRESULT(res);
        }
        if (IS_BIT_SET(lpGEID->u32Mask, GEIDMASK_RW_IMEI4))
        {
            res = IMEI_NV2Str(pAck->geid.imei4, (BYTE*)lpGEID->IMEI4);
            LogFmtStrA(SPLOGLV_INFO, "IMEI4 = %s", lpGEID->IMEI4);
            CHKRESULT(res);
        }

        // MEID1 & MEID2
        if (IS_BIT_SET(lpGEID->u32Mask, GEIDMASK_RW_MEID1))
        {
           if (pAck->geid.meid1[7] == 0)
           {
               MEID_NV2STR14digits(pAck->geid.meid1, lpGEID->MEID1);
			   LogFmtStrA(SPLOGLV_INFO, "MEID_NV2STR2 MEID1 = %s", lpGEID->MEID1);
           }
           else
           {
               MEID_NV2STR(pAck->geid.meid1, lpGEID->MEID1);
		       LogFmtStrA(SPLOGLV_INFO, "MEID_NV2STR MEID1 = %s", lpGEID->MEID1);
           }
        }
        if (IS_BIT_SET(lpGEID->u32Mask, GEIDMASK_RW_MEID2))
        {
            MEID_NV2STR(pAck->geid.meid2, lpGEID->MEID2);
            LogFmtStrA(SPLOGLV_INFO, "MEID2 = %s", lpGEID->MEID2);
        }
    }

    return res;
}

SPRESULT CCommCmd::SaveGEID(const PC_GEID_T* lpGEID, uint32 u32TimeOut)
{
    CheckValidPointer(lpGEID);
    LogFmtStrA(SPLOGLV_INFO, "Save GEID (IMEI&MEID): TimeOut = %d ms", u32TimeOut);

    diag_geid_save_req_t req;
    ZeroMemory((void*)& req, sizeof(req));
    req.cmd = GEID_W;

    /// IMEI1 - IMEI4
    if (IS_BIT_SET(lpGEID->u32Mask, GEIDMASK_RW_IMEI1))
    {
        req.geid.mask |= GEIDMASK_RW_IMEI1;
        IMEI_Str2NV((const BYTE*)lpGEID->IMEI1, req.geid.imei1);
    }
    if (IS_BIT_SET(lpGEID->u32Mask, GEIDMASK_RW_IMEI2))
    {
        req.geid.mask |= GEIDMASK_RW_IMEI2;
        IMEI_Str2NV((const BYTE*)lpGEID->IMEI2, req.geid.imei2);
    }
    if (IS_BIT_SET(lpGEID->u32Mask, GEIDMASK_RW_IMEI3))
    {
        req.geid.mask |= GEIDMASK_RW_IMEI3;
        IMEI_Str2NV((const BYTE*)lpGEID->IMEI3, req.geid.imei3);
    }
    if (IS_BIT_SET(lpGEID->u32Mask, GEIDMASK_RW_IMEI4))
    {
        req.geid.mask |= GEIDMASK_RW_IMEI4;
        IMEI_Str2NV((const BYTE*)lpGEID->IMEI4, req.geid.imei4);
    }

    // MEID1 - MEID2
    if (IS_BIT_SET(lpGEID->u32Mask, GEIDMASK_RW_MEID1))
    {
        req.geid.mask |= GEIDMASK_RW_MEID1;
        if (lpGEID->MEID1[14] == 0)
        {
            uint8  MEID1[MAX_GEID_STR_LENGTH] = { 0 };
            uint32 pSize = 0;
            ReverseAsciiStr((uint8*)lpGEID->MEID1, 14, MEID1, 14, &pSize);
            vector<int8> arrMeid(16, 0);
            memcpy(arrMeid.data(), &MEID1, 14);
            Security_String2Numeral(arrMeid.data(), req.geid.meid1, 14);
        }
        else
        {
            MEID_STR2NV(lpGEID->MEID1, req.geid.meid1);
        }
    }
    if (IS_BIT_SET(lpGEID->u32Mask, GEIDMASK_RW_MEID2))
    {
        req.geid.mask |= GEIDMASK_RW_MEID2;
        MEID_STR2NV(lpGEID->MEID2, req.geid.meid2);
    }

    req.crc16 = Convert16(crc16(0, (unsigned char const*)& req.geid, sizeof(req.geid)));
    DeclareDiagHeader(hd, DIAG_MODEM_SYSTEM_F, GEID_RW_CMD);
    uint32 recvSize = 0;
    SPRESULT res = SendAndRecv(hd, (const void*)& req, sizeof(req), hd, m_diagBuff, sizeof(m_diagBuff), &recvSize, u32TimeOut);
    if (SP_OK == res)
    {
        diag_geid_save_cnf_t* pAck = (diag_geid_save_cnf_t*)m_diagBuff;
        CheckResponseLength(recvSize, sizeof(diag_geid_save_cnf_t));
        if (req.cmd != pAck->cmd)
        {
            LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid ACK cmd %d", __FUNCTION__, pAck->cmd);
            return SP_E_PHONE_INVALID_DATA;
        }
        if (0 != pAck->status)
        {
            LogFmtStrA(SPLOGLV_ERROR, "Invalid state %d while saving GEID", pAck->status);
            return SP_E_PHONE_INVALID_STATE;
        }
    }

    return res;
}

void CCommCmd::MEID_NV2STR(const BYTE nvValue[MAX_GEID_NV_LENGTH], CHAR szValue[MAX_GEID_STR_LENGTH])
{
    szValue[MAX_GEID_STR_LENGTH - 1] = '\0';

    CHAR tmp[MAX_GEID_STR_LENGTH] = { 0 };
    for (int i = 0; i < (MAX_GEID_NV_LENGTH - 1); i++)
    {
        tmp[i * 2 + 0] = HI4BITS(nvValue[i + 0]);
        tmp[i * 2 + 1] = LOW4BITS(nvValue[i + 1]);
    }

    tmp[14] = HI4BITS(nvValue[7]);

    sprintf_s((CHAR*)szValue, MAX_GEID_STR_LENGTH, "%X%X%X%X%X%X%X%X%X%X%X%X%X%X%X", \
        tmp[0], tmp[1], tmp[2], tmp[3], tmp[4], \
        tmp[5], tmp[6], tmp[7], tmp[8], tmp[9], \
        tmp[10], tmp[11], tmp[12], tmp[13], tmp[14]);
}


void CCommCmd::MEID_NV2STR14digits(const BYTE nvValue[MAX_GEID_NV_LENGTH], CHAR szValue[MAX_GEID_STR_LENGTH])
{
    szValue[MAX_GEID_STR_LENGTH - 1] = '\0';

    CHAR tmp[MAX_GEID_STR_LENGTH] = { 0 };
    for (int i = 0; i < (MAX_GEID_NV_LENGTH - 1); i++)
    {
        tmp[i * 2 + 0] = LOW4BITS(nvValue[i + 0]);
        tmp[i * 2 + 1] = HI4BITS(nvValue[i + 0]);
    }

    tmp[14] = HI4BITS(nvValue[7]);

    sprintf_s((CHAR*)szValue, MAX_GEID_STR_LENGTH, "%X%X%X%X%X%X%X%X%X%X%X%X%X%X%X", \
        tmp[0], tmp[1], tmp[2], tmp[3], tmp[4], \
        tmp[5], tmp[6], tmp[7], tmp[8], tmp[9], \
        tmp[10], tmp[11], tmp[12], tmp[13], tmp[14]);
    *(szValue + 14) = '\0';
}

void CCommCmd::MEID_STR2NV(const CHAR szValue[MAX_GEID_STR_LENGTH], BYTE nvValue[MAX_GEID_NV_LENGTH])
{
    ZeroMemory((void*)nvValue, MAX_GEID_NV_LENGTH);

#define __Conv2Hex(x, y)      \
    if ((x) >= '0' && (x) <= '9') { \
    (y) = (x) - '0' + 0x0; \
    } else if ((x) >= 'a' && (x) <= 'f') { \
    (y) = (x) - 'a' + 0xA; \
    } else { \
    (y) = (x) - 'A' + 0xA; \
    }
    BYTE temp1 = 0;
    BYTE temp2 = 0;

    // lower 4 bits of 1st byte must be 0xA 
    __Conv2Hex(szValue[0], temp1);
    nvValue[0] = (BYTE)MAKE1BYTEBY2BYTES(temp1, (BYTE)0xA);

    for (int i = 1; i < MAX_GEID_NV_LENGTH; i++)
    {
        __Conv2Hex(szValue[2 * i - 0], temp1);
        __Conv2Hex(szValue[2 * i - 1], temp2);
        nvValue[i] = (BYTE)MAKE1BYTEBY2BYTES(temp1, temp2);
    }
#undef __Conv2Hex
}

SPRESULT CCommCmd::InitPhaseCheck(LPCSTR lpszSN1, LPCSTR lpszSN2)
{
    SPRESULT res = SP_OK;
    CLoadPhaseCheckConfig cfg;
    extern HMODULE g_hModule;
    cfg.Load(g_hModule);
    size_t nCount = cfg.m_vecStations.size();
    if (0 == nCount)
    {
        LogRawStrA(SPLOGLV_ERROR, "PhaseCheck station count is 0!");
        return SP_E_PHONE_INVALID_PARAMETER;
    }

    LogRawStrA(SPLOGLV_INFO, "Initializing PhaseCheck ...");
    uint32 u32MagicNumber = SP05_SPPH_MAGIC_NUMBER;
    switch (cfg.m_eMagic)
    {
    case SP05:
    {
        u32MagicNumber = SP05_SPPH_MAGIC_NUMBER;

        SP05_PHASE_CHECK_T  sp05;
        ZeroMemory((void*)& sp05, sizeof(sp05));
        sp05.header.Magic = u32MagicNumber;
        sp05.header.StationNum = (uint8)(nCount > SP05_MAX_SUPPORT_STATION ? SP05_MAX_SUPPORT_STATION : nCount);
        if (NULL != lpszSN1)
        {
            strncpy_s(sp05.header.SN, lpszSN1, SP05_MAX_SN_LEN - 1);
        }
        if (NULL != lpszSN2)
        {
            strncpy_s(sp05.header.SN2, lpszSN2, SP05_MAX_SN_LEN - 1);
        }

        for (uint8 i = 0; i < sp05.header.StationNum; i++)
        {
            strncpy_s(sp05.items[i].TestState, "N/A", SP05_TS_LEN - 1);
            strncpy_s(sp05.items[i].TestStationName, cfg.m_vecStations[i].c_str(), SP05_TSN_LEN - 1);
        }

        res = SaveProductInfo((LPCVOID)& sp05, sizeof(sp05), TIMEOUT_3S);
    }
    break;

    case SP15:
    {
        u32MagicNumber = SP15_SPPH_MAGIC_NUMBER;

        SP15_PHASE_CHECK_T  sp15;
        ZeroMemory((void*)& sp15, sizeof(sp15));
        sp15.Magic = u32MagicNumber;
        if (NULL != lpszSN1)
        {
            strncpy_s(sp15.SN1, lpszSN1, SP15_MAX_SN_LEN - 1);
        }
        if (NULL != lpszSN2)
        {
            strncpy_s(sp15.SN2, lpszSN2, SP15_MAX_SN_LEN - 1);
        }

        sp15.SignFlag = (uint8)cfg.m_eSignFlag;
        sp15.iTestSign = (PASS_0_FAIL_1 == sp15.SignFlag) ? 0xFFFFFFFF : 0x0;
        sp15.iItem = (PASS_0_FAIL_1 == sp15.SignFlag) ? 0xFFFFFFFF : 0x0;
        sp15.StationNum = (int32)((nCount > SP15_MAX_STATION_NUM) ? SP15_MAX_STATION_NUM : nCount);

        for (int32 i = 0; i < sp15.StationNum; i++)
        {
            strncpy_s(sp15.StationName[i], cfg.m_vecStations[i].c_str(), SP15_MAX_STATION_NAME_LEN - 1);
        }

        res = SaveProductInfo((LPCVOID)& sp15, sizeof(sp15), TIMEOUT_3S);
    }
    break;

    default:
    {
        u32MagicNumber = SP09_SPPH_MAGIC_NUMBER;

        SP09_PHASE_CHECK_T  sp09;
        ZeroMemory((void*)& sp09, sizeof(sp09));
        sp09.Magic = u32MagicNumber;
        if (NULL != lpszSN1)
        {
            strncpy_s(sp09.SN1, lpszSN1, SP09_MAX_SN_LEN - 1);
        }
        if (NULL != lpszSN2)
        {
            strncpy_s(sp09.SN2, lpszSN2, SP09_MAX_SN_LEN - 1);
        }

        sp09.SignFlag = (uint8)cfg.m_eSignFlag;
        sp09.iTestSign = (PASS_0_FAIL_1 == sp09.SignFlag) ? 0xFFFF : 0x0;
        sp09.iItem = (PASS_0_FAIL_1 == sp09.SignFlag) ? 0xFFFF : 0x0;
        sp09.StationNum = (int32)((nCount > SP09_MAX_STATION_NUM) ? SP09_MAX_STATION_NUM : nCount);

        for (int32 i = 0; i < sp09.StationNum; i++)
        {
            strncpy_s(sp09.StationName[i], cfg.m_vecStations[i].c_str(), SP09_MAX_STATION_NAME_LEN - 1);
        }

        res = SaveProductInfo((LPCVOID)& sp09, sizeof(sp09), TIMEOUT_3S);
    }
    break;
    }

    if (SP_OK == res)
    {
        SetProperty(SP_ATTR_MAGIC_NUMBER, 0, (LPCVOID)u32MagicNumber);
    }

    return res;
}

SPRESULT CCommCmd::ModemV3_Common_Save2Flash()
{
    PC_MODEM_RF_V3_NV_SAVE_REQ_CMD_T req;
    req.eNvType = NV_RF_SAVE_BY_MODE;
    req.eType = RF_MODE_NONE;
    PC_MODEM_RF_V3_NV_SAVE_RSP_RLST_T rlt;
    return ModemV3_Nv_Save2Flash(&req, &rlt);
}

SPRESULT CCommCmd::ModemV3_lte_Save2Flash()
{
    PC_MODEM_RF_V3_NV_SAVE_REQ_CMD_T req;
    req.eNvType = NV_RF_SAVE_BY_MODE;
    req.eType = RF_MODE_LTE;
    PC_MODEM_RF_V3_NV_SAVE_RSP_RLST_T rlt;
    return ModemV3_Nv_Save2Flash(&req, &rlt);
}

SPRESULT CCommCmd::ModemV4_Save_AU_CompRSRP(NST_LTE_AU_RSRP_REQ_T* req)
{
    CheckValidPointer(req);
    LogRawStrA(SPLOGLV_INFO, "ModemV4_Save_AU_CompRSRP Cal:");

    L1_MODEM_RF_V4_LTE_AU_RSRP_REQ_T L1;
    ZeroMemory((void*)&L1, sizeof(L1));
    //////////////////////////////////////////////////////////////////////////
    L1.CmdHeader.SubCmdCode = NST_LTE_SAVE_RSRP_COMP_REQ;
    L1.CmdHeader.SubCmdSize = sizeof(L1);
    L1.au_rsrp0_comp = req->au_rsrp0_comp;
    L1.au_rsrp1_comp = req->au_rsrp1_comp;
    L1.au_rsrp2_comp = req->au_rsrp2_comp;
    L1.au_rsrp3_comp = req->au_rsrp3_comp;


    DeclareDiagHeader(hd, DIAG_MODEM_RF_E, ZERO_SUBTYPE);

    CRecvPkgsList recvList;
    recvList.AddCondition(hd);
    SPRESULT res = SendAndRecv(hd, (const void*)&L1, sizeof(L1), recvList, m_dwTimeOut);
    if (SP_OK != res)
    {
        return res;
    }

    uint32 recvLen = 0;
    //2nd packet
    if (!UnpackPRT(recvList.GetPackage(0), NULL, (void*)&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: 2nd packet UnpackPRT return False", __FUNCTION__);
        return SP_E_PHONE_INVALID_DATA;
    }


    if (recvLen >= sizeof(L1_SUBCMD_HEAD_T) + sizeof(uint16))
    {
        L1_MODEM_RF_V4_LTE_AU_RSRP_RLT_T* pRLT = (L1_MODEM_RF_V4_LTE_AU_RSRP_RLT_T*)m_diagBuff;
        uint16 uType = pRLT->CmdHeader.SubCmdCode;
        if ((uType != NST_LTE_SAVE_RSRP_COMP_REQ) || (pRLT->status != RF_APP_OP_SUCCESS))
        {
            LogFmtStrA(SPLOGLV_ERROR, "Invalid response command ID: 0x%X <> 0x%X", uType, NST_LTE_SAVE_RSRP_COMP_REQ);
            return SP_E_PHONE_INVALID_DATA;
        }


        return SP_OK;
    }
    return SP_E_PHONE_INVALID_DATA;
}


SPRESULT CCommCmd::ModemV4_Set_RSRPCalStatus(uint16 status)
{
  
    LogRawStrA(SPLOGLV_INFO, "ModemV4_Set_RSRPCalStatus Cal:");

    L1_RSRP_CAL_REQ_T L1;
    ZeroMemory((void*)&L1, sizeof(L1));
    L1.CmdHeader.SubCmdCode = NST_LTE_SET_RSRP_REQ;
    L1.CmdHeader.SubCmdSize = (uint16)sizeof(L1);
    L1.status = status;

    DeclareDiagHeader(hd, DIAG_MODEM_RF_E, ZERO_SUBTYPE);

    CRecvPkgsList recvList;
    recvList.AddCondition(hd);
    SPRESULT res = SendAndRecv(hd, (const void*)&L1, sizeof(L1), recvList, m_dwTimeOut);
    if (SP_OK != res)
    {
        return res;
    }

    uint32 recvLen = 0;
    //2nd packet
    if (!UnpackPRT(recvList.GetPackage(0), NULL, (void*)&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: 2nd packet UnpackPRT return False", __FUNCTION__);
        return SP_E_PHONE_INVALID_DATA;
    }


    if (recvLen >= sizeof(L1_SUBCMD_HEAD_T) + sizeof(uint16))
    {
        L1_MODEM_RF_V4_LTE_AU_RSRP_RLT_T* pRLT = (L1_MODEM_RF_V4_LTE_AU_RSRP_RLT_T*)m_diagBuff;
        uint16 uType = pRLT->CmdHeader.SubCmdCode;
        if ((uType != NST_LTE_SET_RSRP_REQ)|| (pRLT->status != RF_APP_OP_SUCCESS))
        {
            LogFmtStrA(SPLOGLV_ERROR, "Invalid response command ID: 0x%X <> 0x%X", uType, NST_LTE_SAVE_RSRP_COMP_REQ);
            return SP_E_PHONE_INVALID_DATA;
        }


        return SP_OK;
    }
    return SP_E_PHONE_INVALID_DATA;
}




SPRESULT CCommCmd::Active_LDO_VDDWIFIPA(BOOL bActive)
{
    uint32 recvSize = 0;

    LogFmtStrA(SPLOGLV_INFO, "%s", __FUNCTION__);

    LDO_VDDWIFIPA_T ldo;
    ZeroMemory((void*)& ldo, sizeof(ldo));


    ldo.head.cmd = DIAG_AP_CMD_LDO_VDDWIFIPA;
    ldo.head.length = sizeof(ldo);

    if (bActive)
    {
        ldo.action = LDO_VDDWIFIPA_ON;
    }
    else
    {
        ldo.action = LDO_VDDWIFIPA_OFF;
    }

    DeclareDiagHeader(hd, DIAG_AP_F, ZERO_SUBTYPE);

    CHKRESULT(SendAndRecv(hd, (const void*)& ldo, sizeof(ldo), hd, (void*)m_diagBuff, sizeof(m_diagBuff), &recvSize, m_dwTimeOut));

    if (recvSize < sizeof(TOOLS_DIAG_AP_CNF_T))
    {
        LogFmtStrA(SPLOGLV_INFO, "%s: Invalid Length = %d", __FUNCTION__, recvSize);

        return SP_E_PHONE_INVALID_LENGTH;
    }

    TOOLS_DIAG_AP_CNF_T* pCnf = (TOOLS_DIAG_AP_CNF_T*)m_diagBuff;

    if (pCnf->status != 0)
    {
        LogFmtStrA(SPLOGLV_INFO, "%s: Invalid Status = %d", __FUNCTION__, pCnf->status);
        return SP_E_PHONE_INVALID_PARAMETER;
    }

    LogFmtStrA(SPLOGLV_INFO, "%s OK", __FUNCTION__);

    return SP_OK;
}