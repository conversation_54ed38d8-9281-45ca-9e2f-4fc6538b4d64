#pragma once

#include "DiagChan.h"
#include <vector>
typedef const DIAG_HEADER* LPCDIAG_HEADER;
typedef const PRT_BUFF*    LPCPRT_BUFF;

//////////////////////////////////////////////////////////////////////////
class CRecvPkgsList
{
public:
    CRecvPkgsList(BOOL bRecvMultiPkgs = FALSE);
    virtual ~CRecvPkgsList(void);

    CRecvPkgsList(const CRecvPkgsList& rhs);
    const CRecvPkgsList& operator=(const CRecvPkgsList& rhs);

    void Clear(void);

    int  GetCondCount(void)const;
    int  GetPkgsCount(void)const;
    void AddCondition(const DIAG_HEADER& hd);
    void AddPackage(PRT_BUFF* lpBuff);

    LPCDIAG_HEADER GetCondition(unsigned int i)const;
    LPCPRT_BUFF GetPackage(unsigned int i)const;

    BOOL IsRecvMultiPkgs(void)const { return m_bRecvMultiPkgs; };

public:
    std::vector<DIAG_HEADER> m_CondList;
    std::vector<PRT_BUFF*  > m_PkgsList;
    BOOL m_bRecvMultiPkgs;
};

inline int CRecvPkgsList::GetCondCount(void)const
{
    return m_CondList.size(); 
}

inline int CRecvPkgsList::GetPkgsCount(void)const 
{ 
    return m_PkgsList.size(); 
}

inline void CRecvPkgsList::AddCondition(const DIAG_HEADER& hd)
{
    m_CondList.push_back(hd); 
}

inline LPCDIAG_HEADER CRecvPkgsList::GetCondition(unsigned int i)const
{
    return (/*i >= 0 &&*/i < m_CondList.size()) ? &m_CondList[i] : NULL;
}

inline void CRecvPkgsList::AddPackage(PRT_BUFF* lpBuff)
{
    if (NULL != lpBuff)
    {
        InterlockedIncrement((long* )&lpBuff->ref_count);
        m_PkgsList.push_back(lpBuff);
    }
}

inline LPCPRT_BUFF CRecvPkgsList::GetPackage(unsigned int i)const
{
    return (/*i >= 0 && */i < m_PkgsList.size()) ? m_PkgsList[i] : NULL;
}