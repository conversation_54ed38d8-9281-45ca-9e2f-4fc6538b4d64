#include "StdAfx.h"
#include "CaliCmd.h"
#include <vector>
#include "CdmaUtility.h"

SPRESULT CCaliCmd::C2K_CAL_Active(BOOL bActive)
{
	LogFmtStrA(SPLOGLV_INFO, "%s", "C2K_CAL_Active");

	L1_C2K_CAL_EMPTY_REQ_T L1;
	ZeroMemory((void* )&L1, sizeof(L1));
	if (bActive)
	{
		L1.CmdHeader.SubCmdCode  = Convert16(CAL_C2K_ACTIVE_REQ);
	}
	else
	{
		L1.CmdHeader.SubCmdCode  = Convert16(CAL_C2K_DEACTIVE_REQ);
	}
	L1.CmdHeader.SubCmdSize  = Convert16((uint16)sizeof(L1));
	DeclareDiagHeader(hd, DIAG_MODEM_RF_E, ZERO_SUBTYPE); 

	CRecvPkgsList recvList;
	recvList.AddCondition(hd);

	return SendAndRecv(hd, (const void* )&L1, sizeof(L1), recvList, m_dwTimeOut); 
}

SPRESULT CCaliCmd::C2K_CAL_AfcCal(const PC_C2K_CAL_AFC_REQ_CMD_T *pAfcReq, PC_C2K_CAL_AFC_RSP_T* pAfcRlst)
{
	CheckValidPointer(pAfcReq);
	CheckValidPointer(pAfcRlst);
	LogFmtStrA(SPLOGLV_INFO, "[C2K_CAL_AfcCal]: Afc tuning!");
	L1_C2K_CAL_AFC_REQ_T req;
	ZeroMemory((void *)&req,   sizeof(req));
	req.CmdHeader.SubCmdCode  = Convert16((uint16)CAL_C2K_AFC_REQ);
	req.CmdHeader.SubCmdSize  = Convert16((uint16)sizeof(req));
	req.CADC = Convert32(pAfcReq->CDAC);
	req.CAFC = Convert32(pAfcReq->CAFC);
	DeclareDiagHeader(hd, DIAG_MODEM_RF_E, ZERO_SUBTYPE); 
	CRecvPkgsList recvList;
	recvList.AddCondition(hd);
	SPRESULT res = SendAndRecv(hd, (const void*)&req, sizeof(req), recvList, m_dwTimeOut); 
	if (SP_OK != res)
	{
		return res;
	}
	uint32 recvLen = 0;
	if (!UnpackPRT(recvList.GetPackage(0), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
	{
		LogFmtStrA(SPLOGLV_ERROR, "[C2K_CAL AFC] Unpacket data failed!");
		return SP_E_PHONE_INVALID_DATA;  
	}
	if (recvLen >= sizeof(L1_C2K_CAL_AFC_RSP_T))
	{
		L1_C2K_CAL_AFC_RSP_T *pCnf = (L1_C2K_CAL_AFC_RSP_T *)m_diagBuff;
		uint16 uType = Convert16((uint16)pCnf->CmdHeader.SubCmdCode);
		if (req.CmdHeader.SubCmdCode != uType)
		{
			LogFmtStrA(SPLOGLV_ERROR, "[C2K_CAL AFC] Invalid response command id 0x%X!", uType);
			return SP_E_PHONE_INVALID_DATA;       
		}
		pAfcRlst->status = Convert32(pCnf->status);  
		return SP_OK;
	}
	LogRawStrA(SPLOGLV_ERROR, "Invalid response received!");
	return SP_E_PHONE_INVALID_DATA;
}
SPRESULT CCaliCmd::C2K_CAL_TxTune(const PC_C2K_CAL_TX_TUNE_REQ_T *pTxReq, PC_C2K_CAL_TX_TUNE_RSP_T *pTxRlst)
{
	CheckValidPointer(pTxReq);
	CheckValidPointer(pTxRlst);
	LogRawStrA(SPLOGLV_INFO, "C2K_CAL_TxTune Cal:");

	L1_C2K_CAL_TX_TUNE_REQ_T L1;
	ZeroMemory((void* )&L1, sizeof(L1));
	//////////////////////////////////////////////////////////////////////////
	L1.CmdHeader.SubCmdCode    = Convert16(CAL_C2K_TX_REQ);
	L1.CmdHeader.SubCmdSize    = sizeof(L1_C2K_CAL_TX_TUNE_REQ_T);
	//////////////////////////////////////////////////////////////////////////
	memcpy(&L1.TxTuneData, pTxReq, sizeof(PC_C2K_CAL_TX_TUNE_REQ_T));

	DeclareDiagHeader(hd, DIAG_MODEM_RF_E, ZERO_SUBTYPE);

	CRecvPkgsList recvList;
	recvList.AddCondition(hd);
	//recvList.AddCondition(hd);
	SPRESULT res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), recvList, m_dwTimeOut); 
	if (SP_OK != res)
	{
		return res;
	}

	uint32 recvLen = 0;
	////1st packet
	if (!UnpackPRT(recvList.GetPackage(0), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: 2nd packet UnpackPRT return False", __FUNCTION__);
		return SP_E_PHONE_INVALID_DATA;
	}
	if (recvLen < sizeof(PC_C2K_CAL_TX_TUNE_RSP_T) + sizeof(L1_SUBCMD_HEAD_T))
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Dut response data length %d is less than expected %d", __FUNCTION__, recvLen, sizeof(PC_C2K_CAL_TX_TUNE_RSP_T) + sizeof(L1_SUBCMD_HEAD_T));
		return SP_E_PHONE_INVALID_DATA;
	}

	PC_C2K_CAL_TX_TUNE_RSP_T *pTmpTxRlst = (PC_C2K_CAL_TX_TUNE_RSP_T*)(m_diagBuff+sizeof(L1_SUBCMD_HEAD_T));

	if (SP_OK != pTmpTxRlst->status)
	{
		LogFmtStrA(SPLOGLV_ERROR, "C2K_CAL_TxTune Cal DUT response OpCode is %d", pTxRlst->status);
		return SP_E_PHONE_INVALID_DATA; 
	}
	memcpy(pTxRlst, m_diagBuff+sizeof(L1_SUBCMD_HEAD_T), sizeof(PC_C2K_CAL_TX_TUNE_RSP_T));
	return SP_OK;
}

SPRESULT CCaliCmd::C2K_CAL_RxTune(const PC_C2K_CAL_RX_TUNE_REQ_T *pRxReq, PC_C2K_CAL_RX_TUNE_RSP_T* pRxRlst)
{
	CheckValidPointer(pRxReq);
	LogRawStrA(SPLOGLV_INFO, "C2K_CAL_RxTune Cal:");

	L1_C2K_CAL_RX_TUNE_REQ_T L1;
	ZeroMemory((void* )&L1, sizeof(L1));
	//////////////////////////////////////////////////////////////////////////
	L1.CmdHeader.SubCmdCode    = Convert16(CAL_C2K_RX_REQ);
	L1.CmdHeader.SubCmdSize    = sizeof(L1_C2K_CAL_RX_TUNE_REQ_T);
	//////////////////////////////////////////////////////////////////////////
	memcpy(&L1.RxTuneData, pRxReq, sizeof(PC_C2K_CAL_RX_TUNE_REQ_T));

	DeclareDiagHeader(hd, DIAG_MODEM_RF_E, ZERO_SUBTYPE);

	CRecvPkgsList recvList;
	recvList.AddCondition(hd);
	SPRESULT res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), recvList, m_dwTimeOut); 
	if (SP_OK != res)
	{
		return res;
	}

	uint32 recvLen = 0;
	////1st packet
	if (!UnpackPRT(recvList.GetPackage(0), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: 2nd packet UnpackPRT return False", __FUNCTION__);
		return SP_E_PHONE_INVALID_DATA;
	}
	if (recvLen < sizeof(PC_C2K_CAL_RX_TUNE_RSP_T) + sizeof(L1_SUBCMD_HEAD_T))
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Dut response data length %d is less than expected %d", __FUNCTION__, recvLen, sizeof(PC_C2K_CAL_RX_TUNE_RSP_T) + sizeof(L1_SUBCMD_HEAD_T));
		return SP_E_PHONE_INVALID_DATA;
	}

	int nStatus = *(m_diagBuff + sizeof(L1_SUBCMD_HEAD_T));

	if (SP_OK != nStatus)
	{
		LogFmtStrA(SPLOGLV_ERROR, "C2K_CAL_RxTune Cal DUT response OpCode is %d", nStatus);
		return SP_E_PHONE_INVALID_DATA; 
	}
	CopyMemory(pRxRlst, m_diagBuff+sizeof(L1_SUBCMD_HEAD_T), sizeof(PC_C2K_CAL_RX_TUNE_RSP_T));
	return SP_OK;
}

SPRESULT CCaliCmd::C2K_CAL_TRX_FDT(const PC_C2K_CAL_TRX_FDT_REQ_T *pTRXReq, PC_C2K_CAL_TRX_FDT_RSP_T *pTRXRlst)
{
	CheckValidPointer(pTRXReq);
	CheckValidPointer(pTRXRlst);
	LogRawStrA(SPLOGLV_INFO, "C2K_CAL_TRX_FDT Cal:");

	L1_SUBCMD_HEAD_T L1;
	ZeroMemory((void* )&L1, sizeof(L1));
	//////////////////////////////////////////////////////////////////////////
	uint16 usLength = sizeof(L1_SUBCMD_HEAD_T) + sizeof(PC_C2K_CAL_TRX_FDT_HEADER_T) + pTRXReq->Header.FreqCnt*sizeof(PC_C2K_CAL_ARFCN_CONF) + pTRXReq->Header.AgcGainCnt*sizeof(PC_C2K_CAL_RX_GAIN_CONF) + pTRXReq->Header.AptCnt*sizeof(PC_C2K_CAL_TX_APT_CONF);
	L1.SubCmdCode    = Convert16(CAL_C2K_TXRX_SEQ_REQ);
	L1.SubCmdSize    = usLength;

	uint8* pBuff = NULL;
	try
	{
		pBuff = new uint8[usLength];
	}
	catch (const std::bad_alloc& /*e*/)
	{
		pBuff = NULL;
	}
	if (NULL == pBuff)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Memory allocation failed.", __FUNCTION__);
		return SP_E_PHONE_ALLOC_MEMORY;
	}

	uint8* pPos = pBuff;
	uint16 usLen = sizeof(L1);
	memcpy(pPos, &L1, usLen);
	//header
	pPos += usLen;
	usLen = sizeof(PC_C2K_CAL_TRX_FDT_HEADER_T);
	memcpy(pPos, &pTRXReq->Header, usLen);
	//channel configuration
	pPos += usLen;
	usLen = sizeof(PC_C2K_CAL_ARFCN_CONF)*pTRXReq->Header.FreqCnt;
	memcpy(pPos, pTRXReq->pFreqConf, usLen);
	//Rx gain config
	pPos += usLen;
	usLen = sizeof(PC_C2K_CAL_RX_GAIN_CONF)*pTRXReq->Header.AgcGainCnt;
	memcpy(pPos, pTRXReq->pRxConf, usLen);
	//TXx APT config
	pPos += usLen;
	usLen = sizeof(PC_C2K_CAL_TX_APT_CONF)*pTRXReq->Header.AptCnt;
	memcpy(pPos, pTRXReq->pTxConf, usLen);

	DeclareDiagHeader(hd, DIAG_MODEM_RF_E, ZERO_SUBTYPE);

	CRecvPkgsList recvList;
	recvList.AddCondition(hd);
	SPRESULT res = SendAndRecv(hd, (const void* )pBuff, usLength, recvList, m_dwTimeOut); 
	delete[] pBuff;
	if (SP_OK != res)
	{
		return res;
	}

	uint32 recvLen = 0;

	if (!UnpackPRT(recvList.GetPackage(0), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: 2nd packet UnpackPRT return False", __FUNCTION__);
		return SP_E_PHONE_INVALID_DATA;
	}
	if (recvLen < sizeof(PC_C2K_CAL_TRX_FDT_RSP_T) + sizeof(L1_SUBCMD_HEAD_T))
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Dut response data length %d is less than expected %d", __FUNCTION__, recvLen, sizeof(PC_C2K_CAL_TRX_FDT_RSP_T) + sizeof(L1_SUBCMD_HEAD_T));
		return SP_E_PHONE_INVALID_DATA;
	}

	PC_C2K_CAL_TRX_FDT_RSP_T *pTmpTRXRlst = (PC_C2K_CAL_TRX_FDT_RSP_T*)(m_diagBuff+sizeof(L1_SUBCMD_HEAD_T));

	if (SP_OK != pTmpTRXRlst->status)
	{
		LogFmtStrA(SPLOGLV_ERROR, "C2K_CAL_TRX_FDT Cal DUT response OpCode is %d", pTRXRlst->status);
		return SP_E_PHONE_INVALID_DATA; 
	}
	CopyMemory(pTRXRlst, m_diagBuff+sizeof(L1_SUBCMD_HEAD_T), sizeof(PC_C2K_CAL_TRX_FDT_RSP_T));
	return SP_OK;
}

SPRESULT CCaliCmd::C2K_CAL_TRX_FDT_Rlst_Query(const PC_C2K_CAL_TRX_FDT_RSLT_REQ_T *pTRXRlstReq, void *pTRXRlst)
{
	CheckValidPointer(pTRXRlstReq);
	CheckValidPointer(pTRXRlst);
	LogRawStrA(SPLOGLV_INFO, "C2K_CAL_TRX_FDT_RESULT_QUERY Cal:");

	L1_C2K_CAL_TRX_RLST_REQ_T L1;
	ZeroMemory((void* )&L1, sizeof(L1));
	L1.CmdHeader.SubCmdCode    = Convert16(CAL_C2K_TXRX_SEQ_RLT_REQ);
	L1.CmdHeader.SubCmdSize    = sizeof(L1_C2K_CAL_TRX_RLST_REQ_T);

	L1.FuncType		= Convert32(pTRXRlstReq->FuncType);
	L1.Reserved[0]	= Convert32(pTRXRlstReq->Reserved[0]);
	L1.Reserved[1]	= Convert32(pTRXRlstReq->Reserved[1]);

	DeclareDiagHeader(hd, DIAG_MODEM_RF_E, ZERO_SUBTYPE);

	CRecvPkgsList recvList;
	recvList.AddCondition(hd);
	SPRESULT res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), recvList, m_dwTimeOut); 
	if (SP_OK != res)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s", __FUNCTION__);
		return SP_E_PHONE_INVALID_DATA;
	}

	uint32 recvLen = 0;
	////1st packet
	if (!UnpackPRT(recvList.GetPackage(0), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: 2nd packet UnpackPRT return False", __FUNCTION__);
		return SP_E_PHONE_INVALID_DATA;
	}
	if (recvLen < sizeof(PC_C2K_CAL_TRX_FDT_RSLT_RSP_T) + sizeof(L1_SUBCMD_HEAD_T))
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Dut response data length %d is less than expected %d", __FUNCTION__, recvLen, sizeof(PC_C2K_CAL_RX_TUNE_RSP_T) + sizeof(L1_SUBCMD_HEAD_T));
		return SP_E_PHONE_INVALID_DATA;
	}

	uint32 *puRspStatus =((uint32*)(m_diagBuff+sizeof(L1_SUBCMD_HEAD_T)));

	uint32 DataCount = 0;
	CopyMemory(&DataCount, m_diagBuff+sizeof(L1_SUBCMD_HEAD_T) + sizeof(uint32), sizeof(uint32));

	if (SP_OK != *puRspStatus)
	{
		LogFmtStrA(SPLOGLV_ERROR, "C2K_CAL_TRX_FDT Cal DUT response OpCode is %d", *puRspStatus);
		return SP_E_PHONE_INVALID_DATA; 
	}
	switch(pTRXRlstReq->FuncType)
	{
	case TXRX_SEQ_RLT_RX_RSSI:
	{
			if (pTRXRlstReq->Reserved[0] != DataCount)
			{
				LogFmtStrA(SPLOGLV_ERROR, "Required Rssi count %d doesn't match the response Rssi count %d", pTRXRlstReq->Reserved[0],DataCount);
				return SP_E_PHONE_INVALID_DATA; 
			}
			CopyMemory(pTRXRlst, m_diagBuff+sizeof(L1_SUBCMD_HEAD_T) + sizeof(uint32)*2, sizeof(PC_C2K_CAL_RX_T)*(DataCount));
	}
		break;
	case TXRX_SEQ_RLT_PDET:
	{
			if (pTRXRlstReq->Reserved[0] != DataCount*3)
			{
				LogFmtStrA(SPLOGLV_ERROR, "Required Pdet count %d doesn't match the response Pdet count %d", pTRXRlstReq->Reserved[0],DataCount*3);
				return SP_E_PHONE_INVALID_DATA; 
			}
			CopyMemory(pTRXRlst, m_diagBuff+sizeof(L1_SUBCMD_HEAD_T) + sizeof(uint32)*2, sizeof(PC_C2K_CAL_PDET_T)*(DataCount));
		}
		break;
	default:
		LogFmtStrA(SPLOGLV_ERROR, "Request result type is invalid!");
		return SP_E_PHONE_INVALID_DATA; 
	}
	return SP_OK;
}

SPRESULT CCaliCmd::C2K_CAL_IQ_Imbalance(const PC_C2K_IQ_IMBALANCE_REQ_T* pReq, LPPC_C2K_IQ_IMBALANCE_RLT_T pRlt)
{
	CheckValidPointer(pReq);
	CheckValidPointer(pRlt);
	LogFmtStrA(SPLOGLV_INFO, "cdma set irr parameter, algo_version:%d, param:%d,%d",
		pReq->algo_ver, pReq->param[0],pReq->param[1]);

	SPRESULT    res = SP_OK;
	uint32 recvSize = 0;

	L1_C2K_CAL_IQ_IMB_REQ_T L1;
	ZeroMemory((void* )&L1,    sizeof(L1));

	L1.subcmd_head.SubCmdCode  = Convert16(CAL_C2K_IQ_IMBALANCE_REQ);
	L1.subcmd_head.SubCmdSize  = Convert16((uint16)sizeof(L1));
	L1.algo_ver = pReq->algo_ver;
	CopyMemory(L1.param, pReq->param, sizeof(L1.param));

	DeclareDiagHeader(hd, DIAG_MODEM_RF_E, ZERO_SUBTYPE); 
	res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvSize, m_dwTimeOut);

	if (SP_OK == res)
	{
		L1_C2K_CAL_IQ_IMB_RLT_T* pTempRlt = (L1_C2K_CAL_IQ_IMB_RLT_T* )m_diagBuff; 
		LogFmtStrA(SPLOGLV_INFO, "status:%d, result:%d,%d,%d", 
			pTempRlt->status, pTempRlt->result[0], pTempRlt->result[1], pTempRlt->result[2]);       
		pRlt->status = pTempRlt->status;
		CopyMemory(&pRlt->status, &pTempRlt->status, sizeof(PC_C2K_IQ_IMBALANCE_RLT_T));
		res = pRlt->status;        
	}

	return res;
}
SPRESULT CCaliCmd::C2K_CAL_Save2Flash(void)
{
	PC_MODEM_RF_V3_NV_SAVE_REQ_CMD_T NvReq; 
	PC_MODEM_RF_V3_NV_SAVE_RSP_RLST_T NvRlst;
	ZeroMemory(&NvReq, sizeof(PC_MODEM_RF_V3_NV_SAVE_REQ_CMD_T));
	ZeroMemory(&NvRlst, sizeof(PC_MODEM_RF_V3_NV_SAVE_RSP_RLST_T));
	NvReq.eNvType = NV_RF_SAVE_BY_MODE;
	NvReq.eType = RF_MODE_C2K;
	return ModemV3_Nv_Save2Flash(&NvReq, &NvRlst);
}

SPRESULT CCaliCmd::C2K_CAL_BandList_Query( PC_C2K_CAL_BANDLIST_RSP_T *pBandListRlst )
{
	CheckValidPointer(pBandListRlst);
	LogRawStrA(SPLOGLV_INFO, "C2K_CAL_BandList_Query Cal:");

	L1_C2K_CAL_BANDLIST_REQ_T L1;
	ZeroMemory((void* )&L1, sizeof(L1));
	L1.CmdHeader.SubCmdCode    = Convert16(CAL_C2K_BAND_QUERY_REQ);
	L1.CmdHeader.SubCmdSize    = sizeof(L1_C2K_CAL_BANDLIST_REQ_T);
	
	DeclareDiagHeader(hd, DIAG_MODEM_RF_E, ZERO_SUBTYPE);

	CRecvPkgsList recvList;
	recvList.AddCondition(hd);
	SPRESULT res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), recvList, m_dwTimeOut); 
	if (SP_OK != res)
	{
		return res;
	}

	uint32 recvLen = 0;
	//1st packet
	if (!UnpackPRT(recvList.GetPackage(0), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: 2nd packet UnpackPRT return False", __FUNCTION__);
		return SP_E_PHONE_INVALID_DATA;
	}

	if (recvLen < sizeof(PC_C2K_CAL_BANDLIST_RSP_T) + sizeof(L1_SUBCMD_HEAD_T))
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Dut response data length %d is less than expected %d", __FUNCTION__, recvLen, sizeof(PC_C2K_CAL_BANDLIST_RSP_T) + sizeof(L1_SUBCMD_HEAD_T));
		return SP_E_PHONE_INVALID_DATA;
	}
	uint32 status = SP_OK;
	memcpy(&status, m_diagBuff+sizeof(L1_SUBCMD_HEAD_T), sizeof(uint32));
	if (SP_OK != status)
	{
		LogFmtStrA(SPLOGLV_ERROR, "C2K_CAL_BandList_Query Cal DUT response OpCode is %d",status);
		return SP_E_PHONE_INVALID_DATA; 
	}
	memcpy(pBandListRlst, m_diagBuff+sizeof(L1_SUBCMD_HEAD_T) + sizeof(uint32), sizeof(PC_C2K_CAL_BANDLIST_RSP_T));
	return SP_OK;
}
SPRESULT CCaliCmd::C2K_NST_Active()
{
	LogFmtStrA(SPLOGLV_INFO, "C2K NST Active"); 

	DeclareDiagHeader(hd, DIAG_BOCA_F, 0x00);

	L1_C2K_NST_ACTIVE_REQ_T L1;
	ZeroMemory(&L1, sizeof(L1));
	L1.head.SubCmdCode = NST_C2K_ACTIVE_REQ;
	L1.head.SubCmdSize = sizeof(L1_C2K_NST_ACTIVE_REQ_T);

	uint8  recvBuf[256] = {0};
	uint32 u32revSize = 0;
	SPRESULT  res = SendAndRecv(hd, (const void* )&L1, sizeof(L1_C2K_NST_ACTIVE_REQ_T), hd, (void* )recvBuf, sizeof(recvBuf), &u32revSize, m_dwTimeOut);
	if (SP_OK == res)
	{
		/// <-- : DIAG_TOOL_CNF_T + data
		uint32 u32ExpSize = sizeof(L1_C2K_NST_ACTIVE_RLT_T);
		if (u32revSize < u32ExpSize)
		{
			LogFmtStrA(SPLOGLV_ERROR, "Invalid response length! %d < %d", u32revSize, u32ExpSize);
			return SP_E_PHONE_INVALID_LENGTH;
		}
		L1_C2K_NST_ACTIVE_RLT_T* pCNF = reinterpret_cast<L1_C2K_NST_ACTIVE_RLT_T* >(recvBuf);
		uint32 nState = Convert32(pCNF->status);
		if (0 != nState)
		{
			LogFmtStrA(SPLOGLV_ERROR, "gLayer1 load failed, state = %d", nState);
			return SP_E_PHONE_INVALID_STATE;
		}
	}

	return res;
}

SPRESULT CCaliCmd::C2K_NST_Start( const PC_C2K_NST_START_TEST_T* req)
{
	CheckValidPointer(req);
	LogFmtStrA(SPLOGLV_INFO, "C2K NST start: DL_UARFCN = %d, Band = %d, Rx_Chnl = %d, Tx_Chnl = %d, ant = %d, enableAFC = %d, RadioConfig = %d, WalshCode = %d, TPC_Algo = %d, TPC_Step_Size = %d, Power_Type = %d, Power_Value = %.2f dBm, FER_type = %d, total_frame_num = %d", 
		req->DL_UARFCN,
		req->band,
		req->Rx_Chnl,
		req->Tx_Chnl,
		req->ant,
		req->enableAFC,
		req->RadioConfig,
		req->WalshCode,
		req->TPC_Algo,
		req->TPC_Step_Size,
		req->Power_Type,
		(int32)req->Power_Value/64.0,
		req->FER_type,
		req->total_frame_num
		);

	DeclareDiagHeader(hd, DIAG_BOCA_F, 0x00);

	L1_C2K_NST_START_REQ_T L1;
	ZeroMemory(&L1, sizeof(L1));
	L1.head.SubCmdCode = NST_C2K_START_REQ;
	L1.head.SubCmdSize = sizeof(L1_C2K_NST_START_REQ_T);
	
	L1.param.DL_UARFCN	= req->DL_UARFCN;
	L1.param.band		= req->band;
	L1.param.Rx_Chnl	= req->Rx_Chnl;
	L1.param.Tx_Chnl	= req->Tx_Chnl;
	L1.param.ant		= req->ant;
	L1.param.enableAFC	= req->enableAFC;
	L1.param.RadioConfig= req->RadioConfig;
	L1.param.WalshCode	= req->WalshCode;
	L1.param.TPC_Algo	= req->TPC_Algo;
	L1.param.TPC_Step_Size		= req->TPC_Step_Size;
	L1.param.Power_Type	= req->Power_Type;
	L1.param.Power_Value	= req->Power_Value;
	L1.param.FER_type	= req->FER_type;
	L1.param.total_frame_num	= req->total_frame_num;
	for (int i = 0; i < 20; i ++)
	{
		L1.param.reserved[i] = req->reserved[i];
	}

	uint8  recvBuf[256] = {0};
	uint32 u32revSize = 0;
	SPRESULT  res = SendAndRecv(hd, (const void* )&L1, sizeof(L1_C2K_NST_START_REQ_T), hd, (void* )recvBuf, sizeof(recvBuf), &u32revSize, m_dwTimeOut);
	if (SP_OK == res)
	{
		/// <-- : DIAG_TOOL_CNF_T + data
		uint32 u32ExpSize = sizeof(L1_C2K_NST_START_RLT_T);
		if (u32revSize < u32ExpSize)
		{
			LogFmtStrA(SPLOGLV_ERROR, "Invalid response length! %d < %d", u32revSize, u32ExpSize);
			return SP_E_PHONE_INVALID_LENGTH;
		}
		L1_C2K_NST_START_RLT_T* pCNF = reinterpret_cast<L1_C2K_NST_START_RLT_T* >(recvBuf);
		uint32 nState = Convert32(pCNF->status);
		if (0 != nState)
		{
			LogFmtStrA(SPLOGLV_ERROR, "gLayer1 load failed, state = %d", nState);
			return SP_E_PHONE_INVALID_STATE;
		}
	}
	return res;
}

SPRESULT CCaliCmd::C2K_NST_ReConfig( const PC_C2K_NST_RECONFIG_T* req )
{
	CheckValidPointer(req);
	LogFmtStrA(SPLOGLV_INFO, "C2K NST ReConfig: ant = %d, enableAFC = %d, RadioConfig = %d, WalshCode = %d, TPC_Algo = %d, TPC_Step_Size = %d, Power_Type = %d, Power_Value = %.2f dBm, FER_type = %d, total_frame_num = %d", 
		req->ant,
		req->enableAFC,
		req->RadioConfig,
		req->WalshCode,
		req->TPC_Algo,
		req->TPC_Step_Size,
		req->Power_Type,
		(int32)req->Power_Value/64.0,
		req->FER_type,
		req->total_frame_num
		);

	DeclareDiagHeader(hd, DIAG_BOCA_F, 0x00);

	L1_C2K_NST_RECONFIG_REQ_T L1;
	ZeroMemory(&L1, sizeof(L1));
	L1.head.SubCmdCode = NST_C2K_RECONFIG_REQ;
	L1.head.SubCmdSize = sizeof(L1_C2K_NST_RECONFIG_REQ_T);

	L1.param.ant				= req->ant;	
	L1.param.enableAFC          = req->enableAFC;
	L1.param.RadioConfig        = req->RadioConfig;
	L1.param.WalshCode          = req->WalshCode;
	L1.param.TPC_Algo           = req->TPC_Algo;
	L1.param.TPC_Step_Size      = req->TPC_Step_Size;
	L1.param.Power_Type         = req->Power_Type;
	L1.param.Power_Value        = req->Power_Value;
	L1.param.FER_type           = req->FER_type; 
	L1.param.total_frame_num    = req->total_frame_num;
	for (int i = 0; i < 20; i++)
	{
		L1.param.reserved[i]       = req->reserved[i];
	}

	uint8  recvBuf[256] = {0};
	uint32 u32revSize = 0;
	SPRESULT  res = SendAndRecv(hd, (const void* )&L1, sizeof(L1_C2K_NST_RECONFIG_REQ_T), hd, (void* )recvBuf, sizeof(recvBuf), &u32revSize, m_dwTimeOut);
	if (SP_OK == res)
	{
		/// <-- : DIAG_TOOL_CNF_T + data
		uint32 u32ExpSize = sizeof(L1_C2K_NST_RECONFIG_RLT_T);
		if (u32revSize < u32ExpSize)
		{
			LogFmtStrA(SPLOGLV_ERROR, "Invalid response length! %d < %d", u32revSize, u32ExpSize);
			return SP_E_PHONE_INVALID_LENGTH;
		}
		L1_C2K_NST_RECONFIG_RLT_T* pCNF = reinterpret_cast<L1_C2K_NST_RECONFIG_RLT_T* >(recvBuf);
		uint32 nState = Convert32(pCNF->status);
		if (0 != nState)
		{
			LogFmtStrA(SPLOGLV_ERROR, "gLayer1 load failed, state = %d", nState);
			return SP_E_PHONE_INVALID_STATE;
		}
	}

	return res;
}

SPRESULT CCaliCmd::C2K_NST_StartFER( const PC_C2K_NST_START_FER_T* req )
{
	CheckValidPointer(req);
	LogFmtStrA(SPLOGLV_INFO, "C2K NST StartFER: ant = %d, padding = %d, FER_type = %d, total_frame_num = %d, reserved[0] = %d, reserved[1] = %d", 
		req->ant,
		req->padding,
		req->FER_type, 
		req->total_frame_num,
		req->reserved[0],
		req->reserved[1]
		);

		DeclareDiagHeader(hd, DIAG_BOCA_F, 0x00);

		L1_C2K_NST_START_FER_REQ_T L1;
		ZeroMemory(&L1, sizeof(L1));
		L1.head.SubCmdCode = NST_C2K_START_FER_REQ;
		L1.head.SubCmdSize = sizeof(L1_C2K_NST_START_FER_REQ_T);
		L1.param.ant		= req->ant;
		L1.param.padding	= req->padding;
		L1.param.FER_type	= req->FER_type;
		L1.param.total_frame_num	= req->total_frame_num;
		L1.param.reserved[0]= req->reserved[0];

		uint8  recvBuf[256] = {0};
		uint32 u32revSize = 0;
		SPRESULT  res = SendAndRecv(hd, (const void* )&L1, sizeof(L1_C2K_NST_START_FER_REQ_T), hd, (void* )recvBuf, sizeof(recvBuf), &u32revSize, m_dwTimeOut);
		if (SP_OK == res)
		{
			/// <-- : DIAG_TOOL_CNF_T + data
			uint32 u32ExpSize = sizeof(L1_C2K_NST_START_FER_RLT_T);
			if (u32revSize < u32ExpSize)
			{
				LogFmtStrA(SPLOGLV_ERROR, "Invalid response length! %d < %d", u32revSize, u32ExpSize);
				return SP_E_PHONE_INVALID_LENGTH;
			}
			L1_C2K_NST_START_FER_RLT_T* pCNF = reinterpret_cast<L1_C2K_NST_START_FER_RLT_T* >(recvBuf);
			uint32 nState = Convert32(pCNF->status);
			if (0 != nState)
			{
				LogFmtStrA(SPLOGLV_ERROR, "gLayer1 load failed, state = %d", nState);
				return SP_E_PHONE_INVALID_STATE;
			}
		}
	return res;
}

SPRESULT CCaliCmd::C2K_NST_GetRlt(const PC_C2K_NST_GET_RLT_T *req, void* rlt )
{
	CheckValidPointer(rlt);

	LogFmtStrA(SPLOGLV_INFO, "C2K NST GetRlt: type = %d, ant = %d", req->type, req->ant);
	DeclareDiagHeader(hd, DIAG_BOCA_F, 0x00);

	L1_C2K_NST_GET_RLT_REQ_T L1;
	ZeroMemory(&L1, sizeof(L1));
	L1.head.SubCmdCode = NST_C2K_GET_RLT_REQ;
	L1.head.SubCmdSize = sizeof(L1_C2K_NST_GET_RLT_REQ_T);
	L1.param.type	= req->type;
	L1.param.ant	= req->ant;
	L1.param.reserved[0] = req->reserved[0];
	L1.param.reserved[1] = req->reserved[1];
 
	uint8  recvBuf[256] = {0};
	uint32 u32revSize = 0;
	SPRESULT  res = SendAndRecv(hd, (const void* )&L1, sizeof(L1_C2K_NST_GET_RLT_REQ_T), hd, (void* )recvBuf, sizeof(recvBuf), &u32revSize, m_dwTimeOut);
	if (SP_OK == res)
	{
		uint32 u32ExpSize = sizeof(L1_SUBCMD_HEAD_T);
		if (u32revSize < u32ExpSize)
		{
			LogFmtStrA(SPLOGLV_ERROR, "Invalid response length! %d < %d", u32revSize, u32ExpSize);
			return SP_E_PHONE_INVALID_LENGTH;
		}
		L1_C2K_NST_GET_RLT_RLT_T* pCNF = reinterpret_cast<L1_C2K_NST_GET_RLT_RLT_T* >(recvBuf);
		uint32 nState = Convert32(pCNF->status);
		if (0 != nState)
		{
			LogFmtStrA(SPLOGLV_ERROR, "state = %d", nState);
			return SP_E_PHONE_INVALID_STATE;
		}

		switch (req->type)
		{
		case NST_C2K_RLT_RX_LEVLE:
			{
				PC_C2K_NST_GET_RLT_RSCP_T *pRscpRlt = reinterpret_cast<PC_C2K_NST_GET_RLT_RSCP_T* >(rlt);
				PC_C2K_NST_GET_RLT_RSCP_T *pRscpCnf = reinterpret_cast<PC_C2K_NST_GET_RLT_RSCP_T* >(pCNF->data);
				pRscpRlt->pri_rscp = pRscpCnf->pri_rscp;
				pRscpRlt->pri_rssi = pRscpCnf->pri_rssi;
				pRscpRlt->div_rscp = pRscpCnf->div_rscp;
				pRscpRlt->div_rssi = pRscpCnf->div_rssi;
			}
			break;
		case NST_C2K_RLT_RX_FER:
			{
				PC_C2K_NST_GET_RLT_FER_T *pRscpRlt = reinterpret_cast<PC_C2K_NST_GET_RLT_FER_T* >(rlt);
				PC_C2K_NST_GET_RLT_FER_T *pRscpCnf = reinterpret_cast<PC_C2K_NST_GET_RLT_FER_T* >(pCNF->data);
				pRscpRlt->bad_frames_num	= pRscpCnf->bad_frames_num;
				pRscpRlt->total_frames_num	= pRscpCnf->total_frames_num;
				pRscpRlt->status			= pRscpCnf->status;
			}
			break;
		default:
			break;
		}
	}

	return res;
}

SPRESULT CCaliCmd::C2K_NST_Stop( void )
{
	LogFmtStrA(SPLOGLV_INFO, "C2K NST Stop"); 
	DeclareDiagHeader(hd, DIAG_BOCA_F, 0x00);

	L1_C2K_NST_STOP_REQ_T L1;
	ZeroMemory(&L1, sizeof(L1));
	L1.head.SubCmdCode = NST_C2K_STOP_REQ;
	L1.head.SubCmdSize = sizeof(L1_C2K_NST_STOP_REQ_T);

	uint8  recvBuf[256] = {0};
	uint32 u32revSize = 0;
	SPRESULT  res = SendAndRecv(hd, (const void* )&L1, sizeof(L1_C2K_NST_STOP_REQ_T), hd, (void* )recvBuf, sizeof(recvBuf), &u32revSize, m_dwTimeOut);
	if (SP_OK == res)
	{
		/// <-- : DIAG_TOOL_CNF_T + data
		uint32 u32ExpSize = sizeof(L1_C2K_NST_STOP_RLT_T);
		if (u32revSize < u32ExpSize)
		{
			LogFmtStrA(SPLOGLV_ERROR, "Invalid response length! %d < %d", u32revSize, u32ExpSize);
			return SP_E_PHONE_INVALID_LENGTH;
		}
		L1_C2K_NST_STOP_RLT_T* pCNF = reinterpret_cast<L1_C2K_NST_STOP_RLT_T* >(recvBuf);
		uint32 nState = Convert32(pCNF->status);
		if (0 != nState)
		{
			LogFmtStrA(SPLOGLV_ERROR, "state = %d", nState);
			return SP_E_PHONE_INVALID_STATE;
		}
	}

	return res;
}

SPRESULT CCaliCmd::C2K_NST_DeActive()
{
	LogFmtStrA(SPLOGLV_INFO, "C2K NST DeActive"); 

	DeclareDiagHeader(hd, DIAG_BOCA_F, 0x00);

	L1_C2K_NST_DEACTIVE_REQ_T L1;
	ZeroMemory(&L1, sizeof(L1));
	L1.head.SubCmdCode = NST_C2K_DEACTIVE_REQ;
	L1.head.SubCmdSize = sizeof(L1_C2K_NST_DEACTIVE_REQ_T);

	uint8  recvBuf[256] = {0};
	uint32 u32revSize = 0;
	SPRESULT  res = SendAndRecv(hd, (const void* )&L1, sizeof(L1_C2K_NST_DEACTIVE_REQ_T), hd, (void* )recvBuf, sizeof(recvBuf), &u32revSize, m_dwTimeOut);
	if (SP_OK == res)
	{
		/// <-- : DIAG_TOOL_CNF_T + data
		uint32 u32ExpSize = sizeof(L1_C2K_NST_DEACTIVE_RLT_T);
		if (u32revSize < u32ExpSize)
		{
			LogFmtStrA(SPLOGLV_ERROR, "Invalid response length! %d < %d", u32revSize, u32ExpSize);
			return SP_E_PHONE_INVALID_LENGTH;
		}
		L1_C2K_NST_DEACTIVE_RLT_T* pCNF = reinterpret_cast<L1_C2K_NST_DEACTIVE_RLT_T* >(recvBuf);
		uint32 nState = Convert32(pCNF->status);
		if (0 != nState)
		{
			LogFmtStrA(SPLOGLV_ERROR, "state = %d", nState);
			return SP_E_PHONE_INVALID_STATE;
		}
	}

	return res;
}
SPRESULT CCaliCmd::C2K_Nv_Read( const PC_C2K_RF_NV_DATA_REQ_CMD_T* pNvReq, PC_C2K_RF_NV_DATA_PARAM_T* pNvRlst )
{
	CheckValidPointer(pNvReq);
	CheckValidPointer(pNvRlst);
	LogFmtStrA(SPLOGLV_INFO, "Load Modem V3 NV: nv = %d", pNvReq->eNvType);
	L1_MODEM_C2K_NV_READ_REQ_T L1;
	ZeroMemory((void* )&L1, sizeof(L1));
	L1.head.SubCmdCode = Convert16(NVM_RF_READ_REQ);
	L1.head.SubCmdSize = Convert16(sizeof(L1_MODEM_C2K_NV_READ_REQ_T));
	L1.NvmConf.NvType  = Convert16((uint16)pNvReq->eNvType);
	L1.NvmConf.BandId = (uint8)(pNvReq->BandId);	
	L1.NvmConf.BwId  = pNvReq->BwId;
	L1.NvmConf.HwChanId = pNvReq->HwChanID;
	L1.NvmConf.AntId = pNvReq->AntId;

	DeclareDiagHeader(hd, DIAG_BOCA_F, ZERO_SUBTYPE); 
	uint32 recvLen = 0;
	SPRESULT res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);
	if (SP_OK == res)
	{
		L1_MODEM_C2K_NV_READ_T* pRLT = (L1_MODEM_C2K_NV_READ_T* )(m_diagBuff);
		int OpCode = Convert32(pRLT->op_code);
		uint32 nDataSize = Convert32(pRLT->size);
		uint32 nTotalBufSize = nDataSize + sizeof(OpCode) + sizeof(pRLT->size) + sizeof(L1_SUBCMD_HEAD_T);
		if (OpCode != SP_OK)
		{
			LogFmtStrA(SPLOGLV_ERROR, "ModV3LoadNV failed opCode = %d", OpCode);
			return SP_E_PHONE_INVALID_STATE;
		}
		if (nDataSize > MAX_MODEM_V3_L1_NV_LEN)
		{
			LogFmtStrA(SPLOGLV_ERROR, "ModV3LoadNV Invalid Data size: %d > Max Size: %d", pNvRlst->DataSize, MAX_LTE_L1_NV_LEN_V3);
			return SP_E_PHONE_INVALID_LENGTH;
		}
		else if (recvLen != nTotalBufSize)
		{
			LogFmtStrA(SPLOGLV_ERROR, "ModV3LoadNV Invalid response size: %d !=  %d", recvLen, nTotalBufSize);
			return SP_E_PHONE_INVALID_LENGTH;
		}
		pNvRlst->DataSize = nDataSize;
		memcpy(&pNvRlst->nData[0], &pRLT->data, pNvRlst->DataSize);
	}
	return res;
}
SPRESULT CCaliCmd::C2K_Nv_Write( const PC_C2K_RF_NV_DATA_REQ_CMD_T* pNvReq, PC_C2K_RF_NV_DATA_PARAM_T* pNvData )
{
	CheckValidPointer(pNvReq);
	CheckValidPointer(pNvData);
	LogFmtStrA(SPLOGLV_INFO, "Modem V3 Save NV: nv = %d", pNvReq->eNvType);
	L1_MODEM_C2K_NV_WRITE_REQ_T L1;
	ZeroMemory((void* )&L1, sizeof(L1));
	L1.head.SubCmdCode = Convert16(NVM_RF_WRITE_REQ);
	L1.head.SubCmdSize = Convert16(sizeof(L1_MODEM_C2K_NV_WRITE_REQ_T) + (uint16)pNvData->DataSize);
	L1.NvmConf.NvType  = (uint16)(pNvReq->eNvType);
	L1.NvmConf.BandId = (uint8)(pNvReq->BandId);	
	L1.NvmConf.HwChanId = (uint8)(pNvReq->HwChanID);
	L1.NvmConf.BwId = pNvReq->BwId;
	L1.NvmConf.DataSize = pNvData->DataSize;
	DeclareDiagHeader(hd, DIAG_BOCA_F, ZERO_SUBTYPE); 
	if (pNvData->DataSize > MAX_MODEM_V3_L1_NV_LEN)
	{
		LogFmtStrA(SPLOGLV_ERROR, "SP_lteModV3SaveNV Invalid Data size: %d > Max Size: %d", pNvData->DataSize, MAX_LTE_L1_NV_LEN_V3);
		return SP_E_PHONE_INVALID_LENGTH;
	}
	uint8* pBuff = NULL;
	try
	{
		pBuff = new uint8[L1.head.SubCmdSize];
	}
	catch (const std::bad_alloc& /*e*/)
	{
		pBuff = NULL;
	}
	if (NULL == pBuff)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Memory allocation failed.", __FUNCTION__);
		return SP_E_PHONE_ALLOC_MEMORY;
	}
	uint8* pPos = pBuff;
	uint16 usLen = sizeof(L1);
	memcpy(pPos, &L1, usLen);
	pPos += usLen;
	usLen = (uint16)pNvData->DataSize;
	memcpy(pPos, &pNvData->nData[0], usLen);
	uint32 usLength = L1.head.SubCmdSize;
	uint32 recvLen = 0;
	SPRESULT ret = SendAndRecv(hd, (const void* )pBuff, usLength, hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);
	delete[] pBuff;
	if (SP_OK == ret)
	{
		L1_COMMON_RTL_T* pRLT = (L1_COMMON_RTL_T* )m_diagBuff;
		int OpCode = Convert32(pRLT->status);
		if (OpCode != SP_OK)
		{
			LogFmtStrA(SPLOGLV_ERROR, "ModemV3_Nv_Write failed opCode = %d", OpCode);
			return SP_E_PHONE_INVALID_STATE;
		}
	}
	return ret;
}
