#pragma once
#include "..\CommCmd.h"
#include "gsmUtility.h"
#include "Phonecommand_NR.h"
/// 
class CCaliCmd : public CCommCmd
{
public:
    CCaliCmd(void)
    {
        m_gsmTxParam.puncture_type     = 0;
        m_gsmTxParam.data_type         = DSP_TX_TYPE_RANDOM;
        m_gsmTxParam.coding_scheme     = TOOL_TX_CODE_MCS1;
        m_gsmTxParam.training_sequence = 0;
    }

    virtual ~CCaliCmd(void)
    {

    }

    //////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    ///                                                          GSM
    /// 
    SPRESULT gsmSaveParam(
        uint16 is_nv, 
        L1_CALIBRATION_PARAM_E eType, 
        SP_BAND_INFO eBand, 
        uint16 nIndex, 
        uint16 nLength, 
        LPCVOID lpData, 
        uint32 u32TimeOut
        );
    SPRESULT gsmLoadParam(
        uint16 is_nv, 
        L1_CALIBRATION_PARAM_E eType, 
        SP_BAND_INFO eBand, 
        uint16 nIndex, 
        uint16 nLength, 
        LPVOID lpData, 
        uint32 u32TimeOut
        );

    ///
    /// Calibration
    ///
    void     gsmSetTxParam(const PC_TX_PARAM_T& tx);
    SPRESULT gsmActive(BOOL bActive);
    SPRESULT gsmSetArfcn(SP_BAND_INFO eBand, uint16 u16Arfcn);
    SPRESULT gsmSetPCL(SP_BAND_INFO eBand, uint16 u16Pcl);
    SPRESULT gsmSetPwrFactor(const PC_PWR_FACTOR_PARAM_T* req);
    SPRESULT gsmTxOn(SP_BAND_INFO eBand, BOOL bOn);
    SPRESULT gsmRxOn(SP_BAND_INFO eBand, BOOL bOn, uint16 u16GainIndex, uint16 u16GainValue, uint16 u16SampleCount);
    SPRESULT gsmGetRxRSSI (SP_BAND_INFO eBand, uint16* lpu16rssi);
    SPRESULT gsmGetRxLevel(SP_BAND_INFO eBand, uint16* lpu16rxlv);
	SPRESULT gsmAFC(BOOL bAfcStart, const PC_AFC_REQ_T* req);
	SPRESULT gsmPmicAFC(BOOL bAfcStart, const PC_AFC_REQ_T* req);
	SPRESULT gsmPmicAFC_32Bit(BOOL bAfcStart, const PC_PMIC_AFC_32Bit_REQ_T* req);
	SPRESULT gsmAPC(BOOL bApcStart, const PC_APC_REQ_T* req);
    SPRESULT gsmAGC(BOOL bAgcStart, const PC_AGC_REQ_T* req, PC_AGC_VALUE_CNF_T* cnf);
    SPRESULT gsmSaveToFlash(uint32 u32TimeOut);
    SPRESULT gsmFDT(const GSM_FDT_REQ* req, GSM_FDT_RES* ack, uint32 u32TimeOut);

    SPRESULT gsmNST(const PC_NONSIGNAL_REQ_T* req, PC_NONSIGNAL_CNF_T* ack);
    
	SPRESULT gsmLmt_Init(const PC_NONSIGNAL_LMT_PARAM_T *pFDTReq);
	SPRESULT gsmLmt_start();
	SPRESULT gsmLmt_GetRxResult(int nDataCount, PC_NONSIGNAL_LMT_RX_CNF_T *arrRxl);
	SPRESULT gsmLmt_Stop();
	SPRESULT gsmLmt_IsSync();

	SPRESULT gsmSetCafc(int16 cafc);
	SPRESULT gsmSetCdac(int16 cdac);
	SPRESULT gsmSetAfcCenter(int16 afc_center);
	
	SPRESULT gsmAFCStart(BOOL bAfcStart);
	SPRESULT gsmSetCafc_32Bit(uint32 cafc);
	SPRESULT gsmSetCdac_32Bit(uint32 cdac);
	SPRESULT gsmSetAfcCenter_32Bit(uint32 afc_center);

	/// GSM & TD flag
    SPRESULT gsmLoadCalFlag(uint32 *lpflag);
    SPRESULT gsmSaveCalFlag(uint32 flag);

    /// 
    SPRESULT gsmCalSwitchToTDCal(void);
	SPRESULT gsmGetTransTemperature( TRANSCEIVER_TEMP_T *pTemp );
    SPRESULT lte_GetTransceiverTemp( TRANSCEIVER_TEMP_T *pTemp );
	SPRESULT gsmAntSwitch(SP_BAND_INFO eBand, uint32 nAnt);

	SPRESULT ModemV3_GSM_COMMOM_GetVersion(GSM_VERSION_T* pVersion);

	///MGB
	SPRESULT mgbGsmRFICSWitch(SP_BAND_INFO eBand,unsigned int nRFIC );
	SPRESULT mgbGsmLoCalInit();
	SPRESULT mgbGsmLoCalEnd();
	SPRESULT mgbGsmLoCal(const  CALI_LO_PARAM_REQ_T* pLoCalparam);
	SPRESULT mgbGsmReadPdValue(SP_BAND_INFO eBand, uint16 *pPdValue);
	SPRESULT mgbLTEEGSet( MGB_LTE_EG_SET_T *pParam );
	SPRESULT edgeSetFtFlag(PC_EDGE_FT_FLAG Param);
    //////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    ///                                                      TD-SCDMA
    /// 
    ///
    /// Calibration
    ///
    SPRESULT tdActive(BOOL bActive);
    SPRESULT tdSaveToFlash(uint32 u32TimeOut);
	SPRESULT tdLoadNV(PC_TD_NV_T* lpNV);
	SPRESULT tdSaveNV(const PC_TD_NV_T* lpNV);

	SPRESULT tdFDTTX(const PC_TD_FDT_TX *lpParam);
	SPRESULT tdFDTRX(const PC_TD_FDT_RX *lpParam,PC_TD_FDT_RX_CNF *lpAck);
	SPRESULT tdAgcStart(bool bAgcStart, const PC_AGC_REQ_T *pReq, PC_AGC_VALUE_CNF_T *pAck);
	SPRESULT tdTxGroupSweep(const PC_TD_TX_SWEEP_T *pParam);

    /// NST
    SPRESULT tdNST(TD_NONSIGNAL_COMMAND_E eCmd, const PC_TD_NONSIGNAL_REQ* lpReq, PC_TD_NONSIGNAL_CNF* lpAck);

 
    /// 
    SPRESULT tdCalSwitchTogsmCal(void);

	SPRESULT ModemV3_TD_COMMOM_GetVersion(TD_VERSION_T* pVersion);
    //////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    ///                                                      WCDMA
    /// 
    SPRESULT wcdmaActive(BOOL bActive);
    SPRESULT wcdmaTxOnOff(const PC_CALI_WCDMA_TX_REQ_T* req);
    SPRESULT wcdmaSetAfcValue(const PC_CALI_WCDMA_SET_AFC_VALUE_REQ_T* req);
    SPRESULT wcdmaSetDCDC(RF_WCDMA_DCDC_TYPE eType, uint32 u32Value);
    SPRESULT wcdmaSetTxgain(const PC_CALI_WCDMA_TX_SET_GAIN_REQ_T* req, LPPC_CALI_WCDMA_TX_SET_GAIN_RLT_T rlt);
    SPRESULT wcdmaSetHDTRange(int32 i32Range);
    SPRESULT wcdmaGetHDT(RF_WCDMA_PATH_E ePath, uint32* hdt);
    SPRESULT wcdmaRxOnOff(const PC_CALI_WCDMA_RX_REQ_T* req);
    SPRESULT wcdmaSetRxgain(const PC_CALI_WCDMA_RX_SET_GAIN_REQ_T* req, LPPC_CALI_WCDMA_RX_SET_GAIN_RLT_T rlt);
    SPRESULT wcdmaGetRSSI(RF_WCDMA_PATH_E ePath, uint32* rssi1, uint32* rssi2);
    SPRESULT wcdmaGetAusRSSI(RF_WCDMA_PATH_E ePath, uint32* rssi);
    SPRESULT wcdmaCalIQ(RF_WCDMA_PATH_E ePath, int32* cof_i, int32* cof_q);
    SPRESULT wcdmaDP(const PC_CALI_WCDMA_TX_DYNAMIC_PWR_REQ_V2_T* req);
    SPRESULT wcdmaDP_V3(const PC_CALI_WCDMA_TX_DYNAMIC_PWR_REQ_V2_T* req);
    SPRESULT wcdmaFDT_V2(const PC_CALI_WCDMA_TXRX_SEQ_REQ_V2_T* req);
    SPRESULT wcdmaFDT_V4(const PC_CALI_WCDMA_TXRX_SEQ_REQ_V2_T* req);
	SPRESULT wcdmaFDT_V6(const PC_CALI_WCDMA_TXRX_SEQ_REQ_V6_T* req);
    SPRESULT wcdmaGetFDTRxValues_V2(LPPC_CALI_WCDMA_RX_SEQ_RSSI_V2_RLT_T rlt);
    SPRESULT wcdmaGetFDTRxValues_V4(LPPC_CALI_WCDMA_RX_SEQ_RSSI_V2_RLT_T rlt);
	SPRESULT wcdmaGetFDTRxValues_V6(LPPC_CALI_WCDMA_RX_SEQ_RSSI_V6_RLT_T rlt);
    SPRESULT wcdmaGetHDTValues_V2(LPPC_CALI_WCDMA_TX_HDT_RESULT_RLT_V2_T rlt);
    SPRESULT wcdmaGetHDTValues_V3(LPPC_CALI_WCDMA_TX_HDT_RESULT_RLT_V3_T rlt);
    SPRESULT wcdmaGetHDTValues_V4(LPPC_CALI_WCDMA_TX_HDT_RESULT_RLT_V4_T rlt);
	SPRESULT wcdmaGetHDTValues_V6(LPPC_CALI_WCDMA_TX_HDT_RESULT_RLT_V6_T rlt);
	SPRESULT wcdmaGetFDTTxValues_V6(LPPC_CALI_WCDMA_TX_SEQ_RSSI_V6_RLT_T rlt);
	SPRESULT wcdmaGetFDTTxDebug_V6(LPPC_CALI_WCDMA_TX_RSSI_DEBUG_V6_T pValues);
	SPRESULT wcdmaInternalCal_Init();
	SPRESULT wcdmaInternalCal_Start(const PC_CALI_WCDMA_INTCAL_START_V1_REQ_T *req,PC_CALI_WCDMA_INTCAL_START_V1_RLT_T *pResult);
	SPRESULT wcdmaInternalCal_Stop(uint32 bSaveNv);
	SPRESULT wcdmaInternal_Debug(const PC_CALI_WCDMA_TX_SEQ_DEBUG_PS_REQ_T *pReq,PC_CALI_WCDMA_TX_SEQ_DEBUG_PS_RLT_T *pResult);
    SPRESULT wcdmaIQImbalance(const PC_WCDMA_IQ_IMBALANCE_REQ_T *req, LPPC_WCDMA_IQ_IMBALANCE_RLT_T rlt);
    SPRESULT wcdmaReadICICompensation(const PC_CALI_WCDMA_RX_ICI_COMP_REQ_T* req, LPPC_CALI_WCDMA_RX_ICI_COMP_RLT_T rlt);
    SPRESULT wcdmaReadCaliVer(const PC_CALI_WCDMA_DBG_CMD_REQ* req, LPPC_CALI_WCDMA_DBG_CMD_RLT rlt);
    SPRESULT wcdmaStartCaptureIQ(RF_WCDMA_PATH_E ePath, uint32* lpu32nbrofIQBytes);
    SPRESULT wcdmaCapturingIQ(uint32 u32Offset, uint32 u32nbrOfBytesToCapture, LPVOID lpData, uint32* lpu32nbrOfBytesCaptured);
    SPRESULT wcdmaDebug(uint32 input[6], uint32 ouput[6]);

    SPRESULT wcdmaLoadCalNV(PC_WCDMA_NV_PARAM_T* lpNV);
    SPRESULT wcdmaSaveCalNV(const PC_WCDMA_NV_PARAM_T* lpNV);
    SPRESULT wcdmaSaveToFlash(uint32 u32TimeOut);
    SPRESULT wcdmaLoadDLNV(PC_WCDMA_NV_PARAM_T *lpNV);
    SPRESULT wcdmaSaveDLNV(const PC_WCDMA_NV_PARAM_T* lpNV);
    SPRESULT wcdmaSaveDLNVToFlash(uint32 u32TimeOut);
    SPRESULT wcdmaLoadCalFlag(uint16* lpflag);
    SPRESULT wcdmaSaveCalFlag(uint16 flag);
    SPRESULT wcdmaQueryBand(uint32* pBandNum);
    SPRESULT wcdmaSaveAfcToFlash(uint32 u32TimeOut);
    SPRESULT wcdma_32K_FrequencyError(int16* pFrequencyError);

    
    /// WCDMA NST
    SPRESULT wcdmaNST_Init(void);
    SPRESULT wcdmaNST_Start(const PC_WNST_START_PARAM_T* req);
    SPRESULT wcdmaNST_ReConfig(const PC_WNST_RECONFIG_T* req);
    SPRESULT wcdmaNST_Stop(void);
    SPRESULT wcdmaNST_GetRSCP (LPPC_WNST_RSCP_RLT_T  rlt);
    SPRESULT wcdmaNST_GetAusRSCP(LPPC_WNST_AUS_REQ_T req, LPPC_WNST_AUS_RLT_T  rlt, uint32 u32TimeOut );
    SPRESULT wcdmaNST_GetSEBER(LPPC_WNST_SEBER_RLT_T rlt);
	SPRESULT wcdmaNST_GetDivRSCP(LPPC_WNST_RSCP_RLT_T rlt);
	SPRESULT wcdmaNST_GetDivSEBER(LPPC_WNST_SEBER_RLT_T rlt);

	// WCDMA LMT
	SPRESULT wcdmaLMT_Init(void);
	SPRESULT wcdmaLMT_Start(const PC_WCDMA_LMT_START_T* pStart);
	SPRESULT wcdmaLMT_GetResult(const PC_WCDMA_LMT_GETRLT_REQ_T* pReq, PC_WCDMA_LMT_GETRLT_RLT_T* pRlt);
    SPRESULT wcdmaLMT_Stop(void);

    //////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    ///                                                       LTE
    /// 
    SPRESULT lteActive(BOOL bActive);
    SPRESULT lteTxCW  (const PC_LTE_AFC_CW_T* req);
    SPRESULT lteAgc(const PC_LTE_FDT_RX_T* req, PC_LTE_FDT_RX_RESULT_T* rlt);
    SPRESULT lteApc(const PC_LTE_FDT_TX_T* req);
    SPRESULT lteIrrGainImbalance(const PC_LTE_IRR_GAIN_IMBALANCE_T* req, PC_LTE_IRR_GAIN_IMBALANCE_RESULT_T* rlt);
    SPRESULT lteIrrTuning(const PC_LTE_IRR_TUNING_T* req, PC_LTE_IRR_TUNING_RESULT_T* rlt);
    SPRESULT lteIrrPhase(const PC_LTE_IRR_PHASE_T* req, PC_LTE_IRR_PHASE_RESULT_T* rlt);
    SPRESULT ltePDT(const PC_LTE_PDT_REQ_T* req, PC_LTE_PDT_RESULT_T* rlt);
    SPRESULT lteSwitch(const PC_LTE_SWITCH_REQ_T* req, LPCVOID lpData);
    SPRESULT lteTxDC(const PC_LTE_DC_CAL_CONFIG_T* req);
    SPRESULT lteApc_V2(const PC_LTE_APC_V2_T* req);
	SPRESULT LteAgc_V2(const PC_LTE_AGC_V2_T* pAgcParam, unsigned int* pAgcRet);
	SPRESULT LtePdt_V2(const PC_LTE_PDT_V2_T *req,PC_LTE_PDT_RESULT_T *rlt);
	SPRESULT lteApc_V3(const PC_LTE_APC_V3_T* req);
	SPRESULT LteAgc_V3(const PC_LTE_AGC_V3_T* pAgcParam, unsigned int* pAgcRet);
	SPRESULT LtePdt_V3(const PC_LTE_PDT_V3_T *req,PC_LTE_PDT_RESULT_T *rlt);
	SPRESULT lteFdiqCal(const PC_LTE_FDIQ_M_BAND_REQ *lpFdiqReq, PC_LTE_FDIQ_RET_T *lpFdiqRsp);

    SPRESULT lteLoadNV(PC_LTE_NV_DATA_T* nv);
    SPRESULT lteSaveNV(const PC_LTE_NV_DATA_T* nv);
    SPRESULT lteSaveToFlash(uint32 u32TimeOut);
    SPRESULT lteLoadNV_V2(PC_LTE_NV_V2_DATA_T* nv);
    SPRESULT lteSaveNV_V2(const PC_LTE_NV_V2_DATA_T* nv);
    SPRESULT lteLoadNV_V3(PC_LTE_NV_V3_DATA_T* nv);
    SPRESULT lteSaveNV_V3(const PC_LTE_NV_V3_DATA_T* nv);


    SPRESULT lteNST_Init(BOOL bActive);
    SPRESULT lteNST_Sync(const PC_LTE_NST_SYNC_T* req, LTE_NST_STATUS_E* lpStatus);
    SPRESULT lteNST_Sync_V2(const PC_LTE_NST_SYNC_T* req, LTE_NST_STATUS_E* lpStatus);
    SPRESULT lteNST_Start(const PC_LTE_NST_CONFIG_T* req);
    SPRESULT lteNST_Start_V2(const PC_LTE_NST_CONFIG_T* req);
    SPRESULT lteNST_GetBLER(PC_LTE_NST_SEBLER_T* BLER);
    SPRESULT lteNST_GetRSSI(PC_LTE_NST_RSSI_T*   RSSI);
    SPRESULT LTEContinousTxRx(PC_LTE_CALI_LTE_TRX_ALWAYS_ON * pParam);
    SPRESULT LTEContinousTxRx_V2(PC_LTE_CALI_LTE_TRX_ALWAYS_ON * pParam);
    SPRESULT LTEContinousTxRx_GetRSSI(PC_LTE_TRX_RSSI_T *lpRSSI);
    SPRESULT lteLoadCalFlag(uint16* lpflag);
    SPRESULT lteSaveCalFlag(uint16 flag);
    SPRESULT lteAfc_DL(PC_LTE_AFC_DL_T *pParam, PC_LTE_AFC_DL_RESULT_T* pResult);
	SPRESULT lteAfc_DL_V2(PC_LTE_AFC_DL_V2_T *pParam, PC_LTE_AFC_DL_RESULT_T* pResult);
    SPRESULT LteUlAfc(PC_LTE_CALI_UL_AFC_T * pParam);
    SPRESULT LteSPIData(PC_LTE_SPI* pParam);
	
    // MGB
    SPRESULT mgbLTELoCalInit( ); 
    SPRESULT mgbLTELoCalEnd(); 
    SPRESULT mgbLTELoCal(const CALI_LO_PARAM_REQ_T *pLoCalparam);

    // UIS891x
    SPRESULT gsmAgcRxOn(SP_BAND_INFO eBand, BOOL bOn, uint16 u16GainIndex, uint16 u16GainValue, uint16 u16SampleCount,DSP_TX_TYPE_E uTrxType);
	SPRESULT gsmUIS8910(void* req);
	SPRESULT gsmAPCUIS8910(BOOL bApcStart, const PC_APC_REQ_T* req, BOOL bGsmApcType);
    SPRESULT gsmAGCUIS8910(BOOL bAgcStart, const PC_GSM_AGC_REQ_T* req, PC_AGC_VALUE_CNF_T* cnf);
	
    SPRESULT LteAgc_UIS8910(const PC_LTE_AGC_UIS8910_T* pAgcParam, unsigned int* pAgcRet);
	SPRESULT LteAfcStopReq_UIS8910();
    SPRESULT LteAfcStartReq_UIS8910(const PC_LTE_AFC_START_REQ_UIS8910_T* req);
	SPRESULT LteAfcReq_UIS8910(const PC_LTE_AFC_REQ_UIS8910_T* req);
    SPRESULT LteAfcReq_UIS8910_V2(const PC_LTE_AFC_REQ_UIS8910_V2_T* req);
    SPRESULT LteAfcPwrReq_UIS8910(const PC_LTE_AFC_PWR_REQ_UIS8910_T* req);
    SPRESULT LteDcGetIQReq_UIS8910(PC_LTE_DC_GET_IQ_REQ_UIS8910_T* pRet);
    SPRESULT LteDcStartReq_UIS8910(const PC_LTE_DC_START_REQ_UIS8910_T* req);
    SPRESULT LteDcStopReq_UIS8910();
    SPRESULT LteDcSetIQReq_UIS8910(const PC_LTE_DC_SET_IQ_REQ_UIS8910_T* req);
    SPRESULT LteDcBWReq_UIS8910(const PC_LTE_DC_BW_REQ_UIS8910_T* req);
    SPRESULT LteDcRfCtrlReq_UIS8910(const PC_LTE_DC_RFCTRL_REQ_UIS8910_T* req);
	SPRESULT LteXtalThermStopReq_UIS8910();
    SPRESULT LteXtalThermStartReq_UIS8910(const PC_LTE_XTAL_THERM_START_REQ_UIS8910_T* req);
	SPRESULT LteXtalThermReq_UIS8910(PC_LTE_XTAL_THERM_REQ_UIS8910_T* req);
    SPRESULT LteXtalThermReqV2_UIS8910(PC_LTE_XTAL_THERM_REQ_UIS8910_V2_T* req);
    SPRESULT lteApc_UIS8910(const PC_LTE_APC_UIS8910_T* req);

    SPRESULT LteModeConfig_UIS8910(PC_LTE_MODE_CONFIG_UIS8910_T* req);
    SPRESULT LteModeQuery_UIS8910(PC_LTE_MODE_QUERY_UIS8910_T* req);

    SPRESULT LteTxDroop_UIS8910(const PC_V3_LTE_TXDROOP_PARAM* req);
    SPRESULT LtePdt_UIS8910(const PC_LTE_PDT_V3_T* req, PC_LTE_PDT_RESULT_T* rlt);

    SPRESULT lteNST_Sync_UIS8910(const PC_LTE_NST_SYNC_T* req, LTE_NST_STATUS_E* lpStatus);
	SPRESULT lteNST_Start_UIS8910(const PC_LTE_NST_CONFIG_T* req);
    SPRESULT lteNST_GetBLER_UIS8910(PC_LTE_NST_SEBLER_UIS8910_T* BLER);
    SPRESULT lteNST_GetRSSI_UIS8910(PC_LTE_NST_RSSI_T*   RSSI);

    SPRESULT lteLoadNV_UIS8910(PC_LTE_NV_UIS8910_DATA_T* nv);
    SPRESULT lteSaveNV_UIS8910(const PC_LTE_NV_UIS8910_DATA_T* nv);
    SPRESULT WifiActive_UIS8910(BOOL bActive);
    SPRESULT WifiCalFlag_UIS8910(const PC_WIFI_FLAG_REQ_UIS8910_T* req);
    SPRESULT WifiRxReq_UIS8910(BOOL bON, const PC_WIFI_RX_REQ_UIS8910_T *req);
    SPRESULT WifiGetRSSI_UIS8910(PC_WIFI_RSSI_REQ_UIS8910_T* RSSI);
    SPRESULT WifiNstInit_UIS8910(BOOL bActive);
    SPRESULT WifiNstFlag_UIS8910(const PC_WIFI_FLAG_REQ_UIS8910_T* req);
    SPRESULT WifiAntFlag_UIS8910(const PC_WIFI_FLAG_REQ_UIS8910_T* req);
    SPRESULT WifiNstRslReq_UIS8910(BOOL bON, const PC_WIFI_RX_REQ_UIS8910_T* req);
    SPRESULT WifiNstRslRlt_UIS8910(PC_WIFI_RSL_RLT_REQ_UIS8910_T* RSL);
    SPRESULT WifiNstRxlevelReq_UIS8910(BOOL bON, const PC_WIFI_RX_REQ_UIS8910_T* req);
    SPRESULT WifiNstRxlevelRlt_UIS8910(PC_WIFI_RSSIRLT_REQ_UIS8910_T* RSSI);

	SPRESULT His8910LteGetBandInfo(PC_TOOL_LTE_DAT_BAND_REQ_T* req);
	SPRESULT His8910LteApc(PC_TOOL_LTE_DAT_TX_REQ_T* req);
	SPRESULT His8910LteAgc(PC_TOOL_LTE_DAT_RX_REQ_T* req);
	SPRESULT His8910LteGetRSSI(PC_TOOL_LTE_DAT_RSSI_REQ_T* req);
	SPRESULT His8910LteAfc(PC_TOOL_LTE_DAT_AFC_REQ_T* req);
	SPRESULT His8910LteRegWrite(PC_TOOL_LTE_DAT_REG_WRITE_REQ_T * req);
	SPRESULT His8910LteRegRead(PC_TOOL_LTE_DAT_REG_READ_REQ_T * rl);
	SPRESULT His8910GSMRegWrite(PC_TOOL_GSM_CALI_PARAM_T* calipara, PC_TOOL_GSM_DAT_REG_WRITE_REQ_T * req);
	SPRESULT His8910GSMRegRead(PC_TOOL_GSM_CALI_PARAM_T* calipara, PC_TOOL_GSM_DAT_REG_READ_REQ_T * rl);


	/// DMR
	SPRESULT dmrLoadRegister(UINT32 nAddr, UINT32& nValue);
	SPRESULT dmrSaveRegister(UINT32 nAddr, UINT32  nValue);
	SPRESULT dmrSetAfcTable(uint32 nFreq, uint16 nDac0, uint16 nRate);
	SPRESULT dmrSetAgcTable(uint32 nFreq, int16 nAgc);
	SPRESULT dmrSetApcTable(uint32 nFreq, uint16 nPwrLv, int16 nApc);
	SPRESULT dmrSetBias(uint32 nFreq, uint16 nPwrLv, int16 nBias);
	SPRESULT dmrGetRxPwrLv(uint16 &nValue);
	SPRESULT dmrSetMode(uint32 nMode);
	SPRESULT dmrSetAdMode(uint16 nMode);

	SPRESULT dmrSetAfcGain(int nHz);
	SPRESULT dmrStartTxRx(BOOL bTX, BOOL bStart, DMR_SIGNAL_TYPE eSignal, UINT nArfcn, UINT nLv);
	SPRESULT dmrGetRxBer(uint16 &nValue);

	SPRESULT dmrUploadArb(LPCWSTR lpszArbPath);
	SPRESULT dmrDownloadIQ(LPCWSTR lpszIqPath);

	SPRESULT dmrSaveToFlash();
	SPRESULT dmrLoadFlag(uint16 &nFlag);
	SPRESULT dmrSaveFlag(uint16 nFlag);
	//Modem V3 lte
	SPRESULT ModemV3_LTE_Active( BOOL bActive );
	SPRESULT ModemV3_LTE_AfcCal( const PC_MODEM_RF_V3_LTE_AFC_REQ_CMD_T *pAfcReq, PC_MODEM_RF_V3_LTE_AFC_RSP_T* pAfcRlst );
	SPRESULT ModemV3_LTE_ApcCal( const PC_MODEM_RF_V3_LTE_APC_REQ_CMD_T *pApcReq, BOOL bPdetEn = FALSE, PC_MODEM_RF_V3_LTE_APC_RSP_T* pApcRlst = NULL , uint32 RspSize = 0);
    SPRESULT ModemV4_LTE_ApcCal( const PC_MODEM_RF_V4_LTE_APC_REQ_CMD_T* pApcReq, BOOL bPdetEn = FALSE, PC_MODEM_RF_V3_LTE_APC_RSP_T* pApcRlst = NULL, uint32 RspSize = 0);
	SPRESULT ModemV3_LTE_AgcCal( const PC_MODEM_RF_V3_LTE_AGC_PARAM *pAgcReq, unsigned short* pAgcRlst );
    SPRESULT ModemV4_LTE_AgcCal( const PC_MODEM_RF_V4_LTE_AGC_PARAM* pAgcReq, unsigned short* pAgcRlst);
	SPRESULT ModemV3_LTE_PdetCal( const PC_MODEM_RF_V3_LTE_PDET_PARAM *pPdetReq, PC_MODEM_RF_V3_LTE_APC_RSP_T *pPdetRlst );
	SPRESULT ModemV3_LTE_FdiqCal( const PC_MODEM_RF_V3_LTE_FDIQ_PARAM *pFdiqReq, PC_MODEM_RF_V3_LTE_FDIQ_RSP_T *pFdiqRlst );
    SPRESULT ModemV4_LTE_FdiqCal( const PC_MODEM_RF_V4_LTE_FDIQ_PARAM* pFdiqReq, PC_MODEM_RF_V3_LTE_FDIQ_RSP_T* pFdiqRlst);
	SPRESULT ModemV3_LTE_PADroopCal( const PC_MODEM_RF_V3_LTE_PADROOP_PARAM *pPadroopReq, PC_MODEM_RF_V3_LTE_PADROOP_RSP_T *pPadroopRlst );
    SPRESULT ModemV4_LTE_PADroopCal( const PC_MODEM_RF_V4_LTE_PADROOP_PARAM* pPadroopReq, PC_MODEM_RF_V3_LTE_PADROOP_RSP_T* pPadroopRlst);
	SPRESULT ModemV3_LTE_TRX_Tune( const PC_MODEM_RF_V3_LTE_TRX_REQ_T *pTRXReq );
	SPRESULT ModemV3_LTE_TRX_Tune_RlstQuery(uint16 RssiEn, uint16 PdetEn, uint16 FreqEn, uint16 RssiRlst[MAX_PC_MODEM_V3_RF_TRX_CHAIN_CNT][4], uint16 PdetRlst[4][4], uint16 FreqRlst[4][4]);
	SPRESULT ModemV3_LTE_TRX_Tune_RssiQuery( uint16 RssiRlst[MAX_PC_MODEM_V3_RF_TRX_CHAIN_CNT][4]);
	SPRESULT ModemV3_LTE_TRX_Tune_PdetQuery( uint16 PdetRlst[MAX_PC_MODEM_V3_RF_TRX_CHAIN_CNT][4]);
	SPRESULT ModemV3_LTE_RfChain_Query(const PC_MODEM_RF_V3_LTE_RFCHAIN_REQ_T *pRfChainReq, PC_MODEM_RF_V3_LTE_RFCHAIN_RLST_T *pRfChainRlst);
    SPRESULT ModemV4_LTE_RfChain_Query(const PC_MODEM_RF_V4_LTE_CH_REQ_CMD_T* pRfChainReq, PC_MODEM_RF_V4_LTE_CH_RSP_T* pRfChainRlst);
	SPRESULT ModemV4_LTE_Load_PathInfo(const LTE_BAND_E Band, LTE_RF_PATH_INFO_T* pPathInfo);
	SPRESULT ModemV4_LTE_Load_PathInfo_ActualAnt(const LTE_BAND_E Band, LTE_RF_PATH_INFO_NEW_T* pPathInfo);
	SPRESULT ModemV3_LTE_Infor_Query( PC_MODEM_RF_V3_LTE_BANDLIST_RSP_T *pBandListRlst, uint32 nBandCntSize );
	SPRESULT ModemV3_LTE_NSTActive(BOOL bActive);
	SPRESULT ModemV3_LTE_NSTSync(const PC_MODEM_RF_V3_LTE_SYNC_REQ_T* pSyncReq, RF_OP_STATUS_E* pStatus);
	SPRESULT ModemV3_LTE_NST_Start(const PC_MODEM_RF_V3_LTE_NST_CONFIG_T* req);
	SPRESULT ModemV3_LTE_NST_GetRSSI(PC_MODEM_RF_V3_NST_RSSI_T* RSSI);
	SPRESULT ModemV3_LTE_NST_GetBLER(PC_MODEM_RF_V3_NST_SEBLER_T* BLER);
	SPRESULT ModemV3_LTE_LoadCalFlag(int* pBandCnt, LTE_BAND_FLAG* pFlagInfo);
	SPRESULT ModemV3_LTE_COMMOM_GetVersion(LTE_VERSION_T* pVersion);
    SPRESULT ModemV3_LTE_MULTION_SWITCH(const LTE_MULTION_T* pMultionSwitch);
	//NR
	SPRESULT DetermineStatus(SUBCMD_CODE_E cmd, uint8* data, int32 length);

    /// wcdma v3 begin
    SPRESULT    ModemV3_TraceLogInfor(uint32 nStatus);
    /// calibration
    SPRESULT    ModemV3_WCDMA_CalActive(BOOL bActive);
    SPRESULT    ModemV3_WCDMA_ComCmd(const PC_CAL_WCD_COM_CMD_REQ_T* pReq, PC_CAL_WCD_COM_CMD_RLT_T* pRlt);
    SPRESULT    ModemV3_WCDMA_SetAfc(const PC_CAL_WCD_AFC_REQ_T* pReq, PC_CAL_WCD_AFC_RLT_T* pRlt);
    SPRESULT    ModemV3_WCDMA_SetTx(const PC_CAL_WCD_TX_REQ_T* pReq, PC_CAL_WCD_TX_RLT_T* pRlt);
    SPRESULT    ModemV3_WCDMA_SetRx(const PC_CAL_WCD_RX_REQ_T* pReq, PC_CAL_WCD_RX_RLT_T* pRlt);
    SPRESULT    ModemV3_WCDMA_SetIrr(const PC_CAL_WCD_IRR_REQ_T* pReq, PC_CAL_WCD_IRR_RLT_T* pRlt);
    SPRESULT    ModemV3_WCDMA_SetTxRx(const PC_CAL_WCD_TXRX_SEQ_REQ_T* pReq, PC_CAL_WCD_TXRX_SEQ_RLT_T* pRlt);
    SPRESULT    ModemV3_WCDMA_GetTxRx(const PC_CAL_WCD_TXRX_RLT_REQ_T* pReq, PC_CAL_WCD_TXRX_RLT_RLT_T* pRlt);
    SPRESULT    ModemV3_WCDMA_QueryBand(const PC_CAL_WCD_BAND_QUERY_REQ_T* pReq, PC_CAL_WCD_BAND_QUERY_RLT_T* pRlt);    
    SPRESULT    ModemV3_WCDMA_COMMOM_GetVersion(WCDMA_VERSION_T* pVersion);
    /// non-signal
    SPRESULT    ModemV3_WCDMA_NstActive(BOOL bActive);
    SPRESULT    ModemV3_WCDMA_NstStart(const PC_NST_WCD_START_REQ_T* pReq, PC_NST_WCD_START_RLT_T* pRlt);
    SPRESULT    ModemV3_WCDMA_NstReconfig(const PC_NST_WCD_RECONFIG_REQ_T* pReq, PC_NST_WCD_RECONFIG_RLT_T* pRlt);
    SPRESULT    ModemV3_WCDMA_NstStop(const PC_NST_WCD_STOP_REQ_T* pReq, PC_NST_WCD_STOP_RLT_T* pRlt);
    SPRESULT    ModemV3_WCDMA_NstStartSeBer(const PC_NST_WCD_START_SEBER_REQ_T* pReq, PC_NST_WCD_START_SEBER_RLT_T* pRlt);
    SPRESULT    ModemV3_WCDMA_NstGetRscp(const PC_NST_WCD_GET_RLT_REQ_T* pReq, PC_NST_WCD_GET_RLT_RSCP_T* pRlt);
    SPRESULT    ModemV3_WCDMA_NstGetAusRSCP(const PC_NST_WCD_AUS_RSCP_REQ_T* pReq, PC_NST_WCD_AUS_RSCP_RLT_T* pRlt, uint32 u32TimeOut );
    SPRESULT    ModemV3_WCDMA_NstGetSeBer(const PC_NST_WCD_GET_RLT_REQ_T* pReq, PC_NST_WCD_GET_RLT_SEBER_T* pRlt);
    SPRESULT    ModemV3_WCDMA_NstSetMode(NST_WCD_MODE_TYPE_E eMode);
    /// lmt
    SPRESULT    ModemV3_WCDMA_NstFstStart(const PC_NST_WCD_FST_START_REQ_T* pReq, PC_NST_WCD_FST_START_RLT_T* pRlt);
    SPRESULT    ModemV3_WCDMA_NstFstGetRlt(const PC_NST_WCD_FST_GET_RLT_REQ_T* pReq, PC_NST_WCD_FST_GET_RLT_RLT_T* pRlt);
    
    SPRESULT    ModemV3_WCDMA_Save2Flash();
    SPRESULT    ModemV3_WCDMA_LoadCalFlag(uint16* pflag);
    SPRESULT    ModemV3_WCDMA_SaveCalFlag(uint16 flag);
    /// wcdma v3 end
	
	//C2K
	SPRESULT C2K_CAL_Active(BOOL bActive);
	SPRESULT C2K_CAL_TxTune(const PC_C2K_CAL_TX_TUNE_REQ_T *pTxReq, PC_C2K_CAL_TX_TUNE_RSP_T *pTxRlst);
	SPRESULT C2K_CAL_RxTune(const PC_C2K_CAL_RX_TUNE_REQ_T *pRxReq, PC_C2K_CAL_RX_TUNE_RSP_T *pRxRlst);
	SPRESULT C2K_CAL_TRX_FDT(const PC_C2K_CAL_TRX_FDT_REQ_T *pTRXReq, PC_C2K_CAL_TRX_FDT_RSP_T *pTRXRlst);
	SPRESULT C2K_CAL_TRX_FDT_Rlst_Query(const PC_C2K_CAL_TRX_FDT_RSLT_REQ_T *pTRXRlstReq, void *pTRXRlst);
	SPRESULT C2K_CAL_IQ_Imbalance(const PC_C2K_IQ_IMBALANCE_REQ_T* pReq, LPPC_C2K_IQ_IMBALANCE_RLT_T pRlt);
	SPRESULT C2K_CAL_BandList_Query( PC_C2K_CAL_BANDLIST_RSP_T *pBandListRlst);
	SPRESULT C2K_CAL_AfcCal(const PC_C2K_CAL_AFC_REQ_CMD_T *pAfcReq, PC_C2K_CAL_AFC_RSP_T* pAfcRlst);
	SPRESULT C2K_CAL_Save2Flash(void);

	SPRESULT C2K_NST_Active();
	SPRESULT C2K_NST_Start(const PC_C2K_NST_START_TEST_T* req);
	SPRESULT C2K_NST_ReConfig(const PC_C2K_NST_RECONFIG_T* req);
	SPRESULT C2K_NST_StartFER(const PC_C2K_NST_START_FER_T* req);
	SPRESULT C2K_NST_GetRlt(const PC_C2K_NST_GET_RLT_T *req, void* rlt);
	SPRESULT C2K_NST_Stop(void);
	SPRESULT C2K_NST_DeActive();
	SPRESULT C2K_Nv_Write( const PC_C2K_RF_NV_DATA_REQ_CMD_T* pNvReq, PC_C2K_RF_NV_DATA_PARAM_T* pNvData );
	SPRESULT C2K_Nv_Read( const PC_C2K_RF_NV_DATA_REQ_CMD_T* pNvReq, PC_C2K_RF_NV_DATA_PARAM_T* pNvRlst );

    /// RFB
    SPRESULT rfbDebugCmd_Common(const PC_RFB_DEBUG_CMD_REQ_T* pReq, PC_RFB_DEBUG_CMD_RLT_T* pRlt);
    SPRESULT rfbCaptureDataLen_Common(const PC_RFB_CAPTURE_DATA_REQ_T* pReq, PC_RFB_CAPTURE_DATA_RLT_T* pRlt);
    SPRESULT rfbReadData_Common(const PC_RFB_CAPTURE_DATA_READ_REQ_T* pReq, PC_RFB_CAPTURE_DATA_READ_RLT_T* pRlt);

    SPRESULT rfbDebugCmd_NR(const PC_RFB_DEBUG_CMD_REQ_T* pReq, PC_RFB_DEBUG_CMD_RLT_T* pRlt);
    SPRESULT rfbCaptureDataLen_NR(const PC_RFB_CAPTURE_DATA_REQ_T* pReq, PC_RFB_CAPTURE_DATA_RLT_T* pRlt);
    SPRESULT rfbReadData_NR(const PC_RFB_CAPTURE_DATA_READ_REQ_T* pReq, PC_RFB_CAPTURE_DATA_READ_RLT_T* pRlt);

    SPRESULT ModemV3_ReadAntMap(RF_MODE_E Mode,
        int nBand,
        READ_NV_PARAM_RLT_ANT_MAP *pMap);
    /*AIOT*/
    SPRESULT AiotRfDebugAfc(const AIOT_DIAG_AFC_REQ_T* Param);
    SPRESULT AiotRfDebugAfc_V1(const AIOT_DIAG_AFC_REQ_T_V1* Param);
    SPRESULT AiotRfDebugTx(const AIOT_DIAG_TX_REQ_T* Param);
    SPRESULT AiotRfDebugTx_V1(const AIOT_DIAG_TX_REQ_T_V1* Param);
    SPRESULT AiotRfDebugRx(const AIOT_DIAG_RX_REQ_T* Param);
    SPRESULT AiotRfDebugRx_V1(const AIOT_DIAG_RX_REQ_T_V1* Param);
    SPRESULT AiotRfDebugTxOff(const UINT8 Stub_Cmd);
    SPRESULT AiotRfDebugRxOff(const UINT8 Stub_Cmd);
    SPRESULT AiotRfStatusClear(const UINT8 Stub_Cmd);
    SPRESULT AiotRfReadNV(const UINT8 Stub_Cmd);
    SPRESULT AiotRfReadNV_V1(UINT8 Stub_Cmd, AIOT_DIAG_CALIG_BAND_INFO& band_info);
    SPRESULT AiotRfWriteNV(const UINT8 Stub_Cmd);
    SPRESULT AiotRfCheckCrc(const UINT8 Stub_Cmd, UINT32& CrcFlag);
    SPRESULT AiotRfTxStop(const UINT8 Stub_Cmd, UINT8& TxStopRsp);
    SPRESULT AiotSetCalibFlg(const UINT8 Stub_Cmd,const AIOT_DIAG_FLAG_REQ_T* Param);
    SPRESULT AiotRfCalTx(const INT UeType, const UINT8 Stub_Cmd, AIOT_DIAG_TX_REQ_V2_T* pParam);
    SPRESULT AiotRfCalTxSave(const INT UeType, const UINT8 Stub_Cmd, AIOT_DIAG_TX_CALIB_DATA_REQ_V2_T* pParam);
    SPRESULT AiotRfCalRx(const INT UeType, const UINT8 Stub_Cmd, AIOT_DIAG_RX_REQ_V2_T* pParam);
    SPRESULT AiotRfCalGetRssi(const UINT8 Stub_Cmd, const AIOT_DIAG_CALIB_GETRSSI_REQ_T* Req, AIOT_DIAG_CALIB_GETRSSI_CNF_T* Cnf);
    SPRESULT AiotRfCalRxSave(const INT UeType, const UINT8 Stub_Cmd, AIOT_DIAG_RX_CALIB_DATA_REQ_V2_T* pParam);
    SPRESULT AiotRfCalFreq(const INT UeType, const UINT8 Stub_Cmd, AIOT_DIAG_AFC_REQ_V2_T* pParam);
    SPRESULT AiotRfCalFreqSave(const UINT8 Stub_Cmd, const AIOT_DIAG_AFC_CALIB_DATA_REQ_T* Param);
    SPRESULT AiotRfCalNumber(const INT UeType, const UINT8 Stub_Cmd, AIOT_DIAG_CALIB_NUM_REQ_V2_T* pParam);
    SPRESULT AiotRfFtSetSyncParam(const INT UeType, const UINT8 Stub_Cmd, AIOT_DIAG_NSFT_FT_SETUP_REQ_V2_T* Param);
    SPRESULT AiotRfFtGetgSyncFlag(const UINT8 Stub_Cmd, AIOT_DIAG_NSFT_FLAG_REQ_T* Flag);
    SPRESULT AiotRfFtGetgBer(const UINT8 Stub_Cmd, AIOT_DIAG_NSFT_BER_REQ_T* Ber);
    SPRESULT AiotRfFtInitBer(const UINT8 Stub_Cmd, const AIOT_DIAG_INIT_BER_REQ_T* Ber);
    SPRESULT AiotRfFtTx(const INT UeType, const UINT8 Stub_Cmd, AIOT_DIAG_TX_REQ_V2_T* Param);
    SPRESULT AiotFetchCalData(const INT UeType, const UINT8 Stub_Cmd, AIOT_DIAG_FETCH_CAL_DATA_REQ_V2_T* pParam, AIOT_DIAG_FETCH_CAL_DATA_CNF_T* Fetch);
    SPRESULT AiotRfCalTx_V1(const UINT8 Stub_Cmd, const AIOT_DIAG_TX_REQ_T_V1* Param);
    SPRESULT AiotRfCalTxSave_V1(const UINT8 Stub_Cmd, const AIOT_DIAG_TX_CALIB_DATA_REQ_T_V1* Param);
    SPRESULT AiotRfCalRx_V1(const UINT8 Stub_Cmd, const AIOT_DIAG_RX_REQ_T_V1* Param);
    SPRESULT AiotRfCalGetRssi_V1(const UINT8 Stub_Cmd, const AIOT_DIAG_CALIB_GETRSSI_REQ_T_V1* Req, AIOT_DIAG_CALIB_GETRSSI_CNF_T_V1* Cnf);
    SPRESULT AiotRfCalRxSave_V1(const UINT8 Stub_Cmd, const AIOT_DIAG_RX_CALIB_DATA_REQ_T_V1* Param);
    SPRESULT AiotRfCalFreq_V1(const UINT8 Stub_Cmd, const AIOT_DIAG_AFC_REQ_T_V1* Param);
    SPRESULT AiotRfCalNumber_V1(const UINT8 Stub_Cmd, const AIOT_DIAG_CALIB_NUM_REQ_T_V1* Param);
    SPRESULT AiotRfFtSetSyncParam_V1(const UINT8 Stub_Cmd, AIOT_DIAG_NSFT_FT_SETUP_REQ_T_V1* Param);
    SPRESULT AiotRfFtTx_V1(const UINT8 Stub_Cmd, const AIOT_DIAG_TX_REQ_T_V1* Param);
    SPRESULT AiotFetchCalData_V1(const UINT8 Stub_Cmd, const AIOT_DIAG_FETCH_CAL_DATA_REQ_T_V1* Param, AIOT_DIAG_FETCH_CAL_DATA_CNF_T_V1* Fetch);
    SPRESULT AiotRfGoldenSamplSave(UINT8 Stub_Cmd, const AIOT_DIAG_GOLDEN_SAMPLE_REQ_T* Param);
    SPRESULT AiotRfCalibLoss(UINT8 Stub_Cmd, AIOT_DIAG_CALIB_LOSS_REQ_T* Cnf);
    SPRESULT AiotRfReadCalFlag(const UINT8 Stub_Cmd, const AIOT_DIAG_CALIB_GETFLAG_REQ_T* Req, AIOT_DIAG_CALIB_GETFLAG_CNF_T* Cnf);
    SPRESULT AiotRfCalibDataBackup(const UINT8 Stub_Cmd,char*Cnf);
    SPRESULT AiotRfFtNprachTx(UINT8 Stub_Cmd, const AIOT_DIAG_NPRACH_REQ* Param);
    SPRESULT AiotRfCalibGetThmTempLoss(const UINT8 Stub_Cmd, const AIOT_DIAG_CALIB_TEMP_VALUE_REQ* pReq, AIOT_DIAG_TEMP_VALUE_CNF* pCnf);
private:
    SPRESULT ModemV3_ReadNvParam(RF_MODE_E Mode,
        NV_PARAM_OP_TYPE Type,
        uint32 uBandIndex,
        int nBuffSize,
        uint8 *pBuff,
        int &nLength);

    SPRESULT rfbDebugCmd(SUBCMD_CODE_E SubCmd, const PC_RFB_DEBUG_CMD_REQ_T* pReq, PC_RFB_DEBUG_CMD_RLT_T* pRlt);
    SPRESULT rfbCaptureDataLen(SUBCMD_CODE_E SubCmd, const PC_RFB_CAPTURE_DATA_REQ_T* pReq, PC_RFB_CAPTURE_DATA_RLT_T* pRlt);
    SPRESULT rfbReadData(SUBCMD_CODE_E SubCmd, const PC_RFB_CAPTURE_DATA_READ_REQ_T* pReq, PC_RFB_CAPTURE_DATA_READ_RLT_T* pRlt);

    /// GSM
    inline uint16 gsmIoBand(SP_BAND_INFO eBand);
    inline BOOL   gsmIsValidPCL(SP_BAND_INFO eBand, uint16 pcl);
    inline BOOL   gsmIsValidArfcn(SP_BAND_INFO eBand, uint16 nArfcn);
    FREQ_LEVEL_E  gsmArfcnFreqLv(SP_BAND_INFO eBand, uint16 nArfcn);

    /// WCDMA 
    inline uint16 wcdmaIoBand(SP_BAND_INFO eBand);
    SPRESULT      wcdmaRLT(const void* lpRecvBuf, uint32 u32RecvSize);

    uint32        wcdmaGetExpCalNvSize(WCDMA_CAL_NV_TYPE_E eNvType);
    void          wcdmaCalNvEndianConv(WCDMA_CAL_NV_TYPE_E eNvType, uint8* lpData, uint32 u32Size);
    uint32        wcdmaGetExpDLNvSize(WCDMA_DL_NV_TYPE eNvType);
    void          wcdmaDLNvEndianConv(WCDMA_DL_NV_TYPE eNvType, uint8* lpData, uint32 u32Size);

	
private:
	double dmrArfcnConvMHz(uint16 nArfcn);
    PC_TX_PARAM_T m_gsmTxParam;

    static LPCSTR SCZ_GSM_BAND[BI_GSM_MAX_BAND];
};

///
#define DEFAULT_RX_SAMPLE_COUNT         (  18 )   
#define WAIT_TIME_FOR_GET_RSSI          ( 200 )

#define getIndexPCL(band, pcl)          ( (BI_EGSM == (band) || BI_GSM_850 == (band)) ? ((pcl) - 5) : (pcl) )
#define gsmIsValidBand(band)            (  BI_EGSM == (band) || BI_DCS == (band) || BI_PCS == (band) || BI_GSM_850 == (band) )








