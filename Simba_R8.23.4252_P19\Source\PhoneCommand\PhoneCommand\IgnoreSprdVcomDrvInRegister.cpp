﻿#include "StdAfx.h"
#include <sstream>
#include <vector>
#include <string>

static std::vector<std::wstring> SplitW(const std::wstring &s, WCHAR delim);
static std::wstring& trimW(std::wstring& str);

//////////////////////////////////////////////////////////////////////////
/*
[HKEY_LOCAL_MACHINE\SYSTEM\ControlSet001\Control\UsbFlags]中添加：
“GlobalDisableSerNumGen”=hex:01    // 屏蔽所有USB设备的SN
 “IgnoreHWSerNum17825D24”=hex:01    // 屏蔽指定USB设备的SN                                                                      // 其中的1782 5D24就是指设备的VID和PID

示例屏蔽了1782 5D24设备产生的SN号，即使设备上报了SN  windows 也不会采集,  
这样在同样的物理口上，即使设备SN不一致，端口可以保持一致.

！！修改这条注册表需要管理员权限！！
*/
void RegisterSprdVComKey(const std::wstring& sn)  
{
    if (sn.empty())
    {
        return ;
    }

    HKEY hKey = NULL;
    LPCWSTR SUBKEY = L"SYSTEM\\CurrentControlSet\\Control\\UsbFlags";
    LSTATUS status = RegOpenKeyExW(HKEY_LOCAL_MACHINE, SUBKEY, 0, KEY_ALL_ACCESS, &hKey);
    if (ERROR_SUCCESS == status)
    {
        std::vector<std::wstring> arr = SplitW(sn, L';');
        for (int i=0, length=arr.size(); i<length; i++)
        {
            std::wstring key = L"IgnoreHWSerNum" + arr[i];
            const BYTE value = 1;
            RegSetValueExW(hKey, key.c_str(), 0, REG_BINARY, &value, sizeof(value));
        }
    }

    if (NULL != hKey)
    {
        RegCloseKey(hKey);
    }
}

std::vector<std::wstring> SplitW(const std::wstring &s, WCHAR delim) 
{
    std::vector<std::wstring> elems;
    elems.clear();

    std::wstringstream ss;
    ss.str(s);
    std::wstring item = L"";
    while (std::getline(ss, item, delim)) 
    {
        trimW(item);
        if (item.length() > 0)
        {
            elems.push_back(item);
        }
    }

    return elems;
}

std::wstring& trimW(std::wstring& str)
{
    std::wstring::size_type pos = str.find_last_not_of(L' ');
    if (pos != std::wstring::npos) 
    {
        str.erase(pos + 1);
        pos = str.find_first_not_of(L' ');
        if(pos != std::wstring::npos) 
        {
            str.erase(0, pos);
        }
    }
    else 
    {
        str.erase(str.begin(), str.end());
    }

    return str;
}

