#include "StdAfx.h"
#include "RecvPkgsList.h"

CRecvPkgsList::CRecvPkgsList(BOOL bRecvMultiPkgs/* = FALSE*/)
    : m_bRecvMultiPkgs(bRecvMultiPkgs)
{
}

CRecvPkgsList::~CRecvPkgsList(void)
{
    Clear();
}

CRecvPkgsList::CRecvPkgsList(const CRecvPkgsList& rhs)
{
    size_t i = 0;
    Clear();

    for (i=0; i<rhs.m_CondList.size(); i++)
    {
        AddCondition(rhs.m_CondList[i]);
    }

    for (i=0; i<rhs.m_PkgsList.size(); i++)
    {
        AddPackage(rhs.m_PkgsList[i]);
    }
}

const CRecvPkgsList& CRecvPkgsList::operator=(const CRecvPkgsList& rhs)
{
    if (this == &rhs)
    {
        return *this;
    }

    size_t i = 0;
    Clear();

    for (i=0; i<rhs.m_CondList.size(); i++)
    {
        AddCondition(rhs.m_CondList[i]);
    }

    for (i=0; i<rhs.m_PkgsList.size(); i++)
    {
        AddPackage(rhs.m_PkgsList[i]);
    }
	m_bRecvMultiPkgs = rhs.m_bRecvMultiPkgs;
    return *this;
}

void CRecvPkgsList::Clear(void)
{
    m_bRecvMultiPkgs = FALSE;
    m_CondList.clear();

    int nCount = m_PkgsList.size();
    for (int i=0; i<nCount; i++)
    {
        if (NULL != m_PkgsList[i])
        {
            // Memory free 
            m_PkgsList[i]->free_prt(m_PkgsList[i]);
        }
    }
    m_PkgsList.clear();
}