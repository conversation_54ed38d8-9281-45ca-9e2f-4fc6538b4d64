﻿#include "StdAfx.h"
#include "CaliCmd.h"
#include <vector>
#include "LteUtility.h"
#include "LteDef.h"

//////////////////////////////////////////////////////////////////////////

SPRESULT CCaliCmd::gsmAgcRxOn(SP_BAND_INFO eBand, BOOL bOn, uint16 u16GainIndex, uint16 u16GainValue, uint16 u16SampleCount,DSP_TX_TYPE_E uTrxType)
{
    if (!gsmIsValidBand(eBand))
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Unknown GSM band %d!", __FUNCTION__, eBand);
        return SP_E_PHONE_INVALID_PARAMETER;
    }
    else
    {
        LogFmtStrA(SPLOGLV_INFO, "%s RX %s, gain_index = %d, gain_value = 0x%X, sample = %d", SCZ_GSM_BAND[eBand], \
            bOn ? "On" : "Off",  u16GainIndex, u16GainValue, u16SampleCount);

        L1_TX_RX_REQ_T L1;
        ZeroMemory((void* )&L1, sizeof(L1));
        L1.data_type           = Convert32((uint32)uTrxType);
        L1.gain_ind            = Convert16((uint16)u16GainIndex);
        L1.gain_val            = Convert16((uint16)u16GainValue);
        L1.sample_couter       = Convert16((uint16)u16SampleCount);
        L1.on_off              = (uint16)(bOn ? 1 : 0);
        L1.is_dcvoltage_meas   = (uint16)(0);

        return gsmSaveParam(L1_RAM, CALI_RX_ON_OFF, eBand, 0, sizeof(L1), (const void* )&L1, m_dwTimeOut);
    }
}

SPRESULT CCaliCmd::gsmUIS8910(void* req)
{
    CheckValidPointer(req);

    TOOL_EMPTY_REQ_T  *pReq = (TOOL_EMPTY_REQ_T  *)req;

    pReq->is_nv = 0;
    pReq->bandN = 0;
    pReq->index = 0;
    pReq->type = pReq->SignalCode;

    LogFmtStrA(SPLOGLV_INFO, "DIAGCMD: 0x%x %d", pReq->SignalCode, pReq->SignalSize);

    uint16 codeId;
    uint32 cmdLen;


    switch(pReq->SignalCode)
    {
        /*common*/
    case TOOL_MPH_GSM_CODE_VERSION_REQ:
        {
            codeId = TOOL_MPH_GSM_CODE_VERSION_CNF;
            cmdLen = sizeof(TOOL_MPH_GSM_CODE_VERSION_CNF_T);
        }
        break;

    case TOOL_MPH_GSM_MODE_TX_REQ:
    case TOOL_MPH_GSM_MODE_RX_REQ:
    case TOOL_MPH_GSM_SET_SW_PARAM_REQ:
    case TOOL_MPH_GSM_SET_CALIB_INFO_REQ:
    case TOOL_MPH_GSM_SET_CALIB_FLAG_REQ:
        {
            codeId = TOOL_MPH_GSM_COMMON_RSP;
            cmdLen = sizeof(TOOL_MPH_GSM_COMMON_RSP_T);
        }
        break;

    case TOOL_MPH_GSM_SEND_MODE_REQ:
        {
            codeId = TOOL_MPH_GSM_SEND_MODE_CNF;
            cmdLen = sizeof(TOOL_MPH_GSM_SEND_MODE_CNF_T);
        }
        break;

    case TOOL_MPH_GSM_SEND_COMMAND_REQ:
        {
            codeId = TOOL_MPH_GSM_SEND_COMMAND_CNF;
            cmdLen = sizeof(TOOL_MPH_GSM_SEND_COMMAND_CNF_T);
        }
        break;


    case TOOL_MPH_GSM_GET_SW_PARAM_REQ:
        {
            codeId = TOOL_MPH_GSM_GET_SW_PARAM_CNF;
            cmdLen = sizeof(TOOL_MPH_GSM_GET_SW_PARAM_CNF_T);
        }
        break;

    case TOOL_MPH_GSM_GET_CALIB_INFO_REQ:
        {
            codeId = TOOL_MPH_GSM_GET_CALIB_INFO_CNF;
            cmdLen = sizeof(TOOL_MPH_GSM_GET_CALIB_INFO_CNF_T);
        }
        break;

    case TOOL_MPH_GSM_DUMP_CALIB_INFO_REQ:
        {
            codeId = TOOL_MPH_GSM_DUMP_CALIB_INFO_CNF;
            cmdLen = sizeof(TOOL_MPH_GSM_DUMP_CALIB_INFO_CNF_T);
        }
        break;
        /*AFC xtal*/
    case TOOL_MPH_GSM_AFC_XTAL_COMPENSATE_REQ:
        {
            codeId = TOOL_MPH_GSM_AFC_XTAL_COMPENSATE_CNF;
            cmdLen = sizeof(TOOL_MPH_GSM_AFC_XTAL_COMPENSATE_CNF_T);
        }
        break;

    case TOOL_MPH_GSM_GET_XCV_PARAM_REQ:
        {
            codeId = TOOL_MPH_GSM_GET_XCV_PARAM_CNF;
            cmdLen = sizeof(TOOL_MPH_GSM_GET_XCV_PARAM_CNF_T);
        }
        break;
        /*AGC iloss*/
    case TOOL_MPH_GSM_AGC_ILOSS_COMPENSATE_REQ:
        {
            codeId = TOOL_MPH_GSM_AGC_ILOSS_COMPENSATE_CNF;
            cmdLen = sizeof(TOOL_MPH_GSM_AGC_ILOSS_COMPENSATE_CNF_T);
        }
        break;

    case TOOL_MPH_GSM_GET_RX_NB_POWER_REQ:
        {
            codeId = TOOL_MPH_GSM_GET_RX_NB_POWER_CNF;
            cmdLen = sizeof(TOOL_MPH_GSM_GET_RX_NB_POWER_CNF_T);
        }
        break;
        /*APC paprofile*/
    case TOOL_MPH_GSM_APC_PA_PROFILE_MEAS_REQ:
        {
            codeId = TOOL_MPH_GSM_APC_PA_PROFILE_MEAS_CNF;
            cmdLen = sizeof(TOOL_MPH_GSM_APC_PA_PROFILE_MEAS_CNF_T);
        }
        break;

        /*APC paprofile*/
    case TOOL_MPH_GSM_APC_PA_FAST_PROFILE_MEAS_REQ:
        {
            codeId = TOOL_MPH_GSM_APC_PA_FAST_PROFILE_MEAS_CNF;
            cmdLen = sizeof(TOOL_MPH_GSM_APC_PA_FAST_PROFILE_MEAS_CNF_T);
        }
        break;

        /*APC paoffset*/
    case TOOL_MPH_GSM_DEFAULT_PCL2DBM_CONFIG_REQ:
    case TOOL_MPH_GSM_SET_POW_PER_PCL_ARFCN_REQ:
    case TOOL_MPH_GSM_PA_PROFILE_DAC_SET_REQ:
        {
            codeId = TOOL_MPH_GSM_COMMON_RSP;
            cmdLen = sizeof(TOOL_MPH_GSM_COMMON_RSP_T);
        }
        break;
        /*nst test*/
    case TOOL_MPH_GSM_NST_SET_CONNECT_REQ:
    case TOOL_MPH_GSM_NST_CONFIG_REQ:
    case TOOL_MPH_GSM_NST_BER_LOOP_AB_ORDER_REQ:
    case TOOL_MPH_GSM_NST_BER_START_LOG_REQ:
    case TOOL_MPH_GSM_NST_SET_FLAG_REQ:
	case TOOL_MPH_GSM_ANTTEST_SET_FLAG_REQ:
        {
            codeId = TOOL_MPH_GSM_COMMON_RSP;
            cmdLen = sizeof(TOOL_MPH_GSM_COMMON_RSP_T);
        }
        break;

    case TOOL_MPH_GSM_NST_GET_STATUS_REQ:
        {
            codeId = TOOL_MPH_GSM_NST_GET_STATUS_CNF;
            cmdLen = sizeof(TOOL_MPH_GSM_NST_GET_STATUS_CNF_T);
        }
        break;

    case TOOL_MPH_GSM_NST_BER_GET_REQ:
        {
            codeId = TOOL_MPH_GSM_NST_BER_GET_CNF;
            cmdLen = sizeof(TOOL_MPH_GSM_NST_BER_GET_CNF_T);
        }
        break;
    default:
        codeId = 0;
        cmdLen = 0;
        LogFmtStrA(SPLOGLV_INFO, "DIAGCMD: err invalid codeid");
        break;

    }

    if(cmdLen == 0)
    {
        return SP_E_PHONE_NOT_SUPPORTED;
    }

    DeclareDiagHeader(hd, DIAG_CALIBRATION, ZERO_SUBTYPE); 

    uint32 recvLen = 0;
    SPRESULT   res = SendAndRecv(hd, (const void* )req, pReq->SignalSize, hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);

    if (SP_OK == res)
    {
        TOOL_EMPTY_CNF_T  *pRsp = (TOOL_EMPTY_CNF_T  *)&m_diagBuff[0];
        if((recvLen != cmdLen) || (pRsp->parameter_type != codeId))
        {
            LogFmtStrA(SPLOGLV_ERROR, "Err response (length %d != %d) of (id 0x%x != 0x%x)", cmdLen, recvLen, codeId, pRsp->parameter_type);
            return SP_E_PHONE_INVALID_LENGTH;
        }

        memcpy((void* )req, (const void* )&m_diagBuff[0], recvLen);

    }

    return res;
}

SPRESULT CCaliCmd::gsmAPCUIS8910(BOOL bApcStart, const PC_APC_REQ_T* req, BOOL bGsmApcType)
{
    CheckValidPointer(req);

    SPRESULT res = SP_OK;
    if (bApcStart)
    {
        res = gsmSetArfcn(req->pwrFactor.eBand, req->pwrFactor.uArfcn);
        if (SP_OK != res)
        {
            return res;
        }

        if( 0 == bGsmApcType)
        {
            res = gsmSetPCL(req->pwrFactor.eBand, req->pwrFactor.uPCL);
            if (SP_OK != res)
            {
                return res;
            }
        }
        else if( 1 == bGsmApcType)
        {
            res = gsmSetPwrFactor(&req->pwrFactor);
            if (SP_OK != res)
            {
                return res;
            }
            else
            {
                /// Wait parameters to setup 
                Sleep(100);
            }
        }
    }

    return gsmTxOn(req->pwrFactor.eBand, bApcStart);
}

SPRESULT CCaliCmd::gsmAGCUIS8910(BOOL bAgcStart, const PC_GSM_AGC_REQ_T* req, PC_AGC_VALUE_CNF_T* cnf)
{
    if (NULL == req || NULL == cnf)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid parameters!", __FUNCTION__);
        return SP_E_PHONE_INVALID_PARAMETER;
    }

    SPRESULT res = SP_OK;
    if (bAgcStart)
    {
        res = gsmSetArfcn(req->eBand, req->uArfcn);
        if (SP_OK != res)
        {
            return res;
        }
    }

    res = gsmAgcRxOn(req->eBand, bAgcStart, req->uGainIndex, req->uGainValue, DEFAULT_RX_SAMPLE_COUNT,(DSP_TX_TYPE_E)req->uTXRX_TYPE);
    if (SP_OK != res)
    {
        return res;
    }

    if (bAgcStart)
    {
        Sleep(WAIT_TIME_FOR_GET_RSSI);

        //
        uint16 rssi = 0;
        res = gsmGetRxRSSI(req->eBand, &rssi);
        if (SP_OK != res)
        {
            return res;
        }

        uint16 rxlv = 0;
        res = gsmGetRxLevel(req->eBand, &rxlv);
        if (SP_OK != res)
        {
            return res;
        }

        cnf->nRssi  = rssi;
        cnf->nRxlev = rxlv;
    }

    return res;
}

SPRESULT CCaliCmd::His8910GSMRegWrite(PC_TOOL_GSM_CALI_PARAM_T* calipara, PC_TOOL_GSM_DAT_REG_WRITE_REQ_T * req)
{
    TOOL_L1_DIAG_CALI_PARAM_T L1;
    L1.is_nv    = Convert16(calipara->is_nv);
    L1.band     = Convert16((uint16)gsmIoBand((SP_BAND_INFO)calipara->band));
    L1.type     = Convert16(calipara->type);
    L1.index    = Convert16(calipara->index);
    L1.length   = Convert16(calipara->length);

    DeclareDiagHeader(hd, DIAG_CALIBRATION, L1_WRITE);

    /// -->: TOOL_L1_DIAG_CALI_PARAM_T + data
    ZeroMemory((void *)&m_diagBuff, sizeof(m_diagBuff));
    CopyMemory((void* )&m_diagBuff[0], (const void* )&L1, sizeof(L1));
    uint32 u32SendSize = sizeof(L1);
    if (NULL != req )
    {
        CopyMemory((void* )&m_diagBuff[sizeof(L1)], &req->wpara, sizeof(PC_TOOL_GSM_REG_REQ_T));
        u32SendSize += sizeof(PC_TOOL_GSM_REG_REQ_T);
    }

    uint8  recvBuf[256] = {0};
    uint32 u32revSize = 0;
    SPRESULT  res = SendAndRecv(hd, (const void* )m_diagBuff, u32SendSize, hd, (void* )recvBuf, sizeof(recvBuf), &u32revSize, calipara->u32TimeOut);
    if (SP_OK == res)
    {
        uint32 u32ExpSize = sizeof(DIAG_TOOL_CNF_T);
        if (u32revSize < u32ExpSize)
        {
            LogFmtStrA(SPLOGLV_ERROR, "Invalid response length! %d < %d", u32revSize, u32ExpSize);
            return SP_E_PHONE_INVALID_LENGTH;
        }
    }
    return res;
}

SPRESULT CCaliCmd::His8910GSMRegRead(PC_TOOL_GSM_CALI_PARAM_T* calipara,PC_TOOL_GSM_DAT_REG_READ_REQ_T * rlt)
{
    if (NULL == calipara || NULL == rlt)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid pointer!", __FUNCTION__);
        return SP_E_POINTER;
    }

    TOOL_L1_DIAG_CALI_PARAM_T L1;
    L1.is_nv    = Convert16(calipara->is_nv);
    L1.band     = Convert16((uint16)gsmIoBand((SP_BAND_INFO)calipara->band));
    L1.type     = Convert16(calipara->type);
    L1.index    = Convert16(calipara->index);
    L1.length   = Convert16(calipara->length);

    DeclareDiagHeader(hd, DIAG_CALIBRATION, L1_READ);

    ZeroMemory((void *)&m_diagBuff, sizeof(m_diagBuff));
    CopyMemory((void* )&m_diagBuff[0], (const void* )&L1, sizeof(L1));
    uint32 u32SendSize = sizeof(L1);
    
    CopyMemory((void* )&m_diagBuff[sizeof(L1)], &rlt->rpara, sizeof(PC_TOOL_GSM_REG_REQ_T));
    u32SendSize += sizeof(PC_TOOL_GSM_REG_REQ_T);

    uint8  recvBuf[256] = {0};
    uint32 u32revSize = 0;
    SPRESULT res = SendAndRecv(hd, (const void* )m_diagBuff, u32SendSize, hd, (void* )recvBuf, sizeof(recvBuf), &u32revSize, calipara->u32TimeOut);
    if (SP_OK == res)
    {
        /// <-- : DIAG_TOOL_CNF_T + data
        uint32 u32ExpSize = sizeof(DIAG_TOOL_CNF_T) + sizeof(PC_TOOL_GSM_REG_REQ_T);
        if (u32revSize < u32ExpSize)
        {
            LogFmtStrA(SPLOGLV_ERROR, "Invalid response length! %d < %d", u32revSize, u32ExpSize);
            return SP_E_PHONE_INVALID_LENGTH;
        }

        CopyMemory(&rlt->rpara, ((char*)recvBuf) + sizeof(DIAG_TOOL_CNF_T) , sizeof(PC_TOOL_GSM_REG_REQ_T));
    }

    return res;
}

SPRESULT CCaliCmd::His8910LteGetBandInfo( PC_TOOL_LTE_DAT_BAND_REQ_T *pBandInfo )
{
	if (NULL == pBandInfo)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid parameters!", __FUNCTION__);
		return SP_E_PHONE_INVALID_PARAMETER;
	}

	LogRawStrA(SPLOGLV_INFO, "HIS8910 LTE Get Band Info");  

	L1_LTE_EMPTY_REQ_T L1;    
	ZeroMemory((void* )&L1, sizeof(L1));  

	unsigned short usLength = (unsigned short)(sizeof(L1))
		+ sizeof(PC_TOOL_LTE_DAT_BAND_REQ_T);

	L1.SignalSize = usLength;
	L1.SignalCode = TOOL_MPH_LTE_DAT_BAND_REQ;

	unsigned char *lpBuf = new unsigned char[usLength];
	if (NULL == lpBuf)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Memory allocation failed.", __FUNCTION__);
		return SP_E_PHONE_ALLOC_MEMORY;
	}
	else
	{
		ZeroMemory((void *)lpBuf, usLength);
	}

	unsigned char *pPos = lpBuf;
	uint16 usLen =  sizeof(L1);
	memcpy(pPos, &L1, usLen);

	DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE); 
	//////////////////////////////////////////////////////////////////////////
	CRecvPkgsList recvList;
	recvList.AddCondition(hd);
	recvList.AddCondition(hd);
	SPRESULT res = SendAndRecv(hd, (const void* )lpBuf, usLength, recvList, m_dwTimeOut); 
	delete[] lpBuf;
	if (SP_OK != res)
	{
		return res;
	}

	uint32 recvLen = 0;
	//1st packet
	if (!UnpackPRT(recvList.GetPackage(0), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: 1st packet UnpackPRT return False", __FUNCTION__);
		return SP_E_PHONE_INVALID_DATA;
	}
	else if (recvLen != 0)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: 1st packet length != 0", __FUNCTION__);
		return SP_E_PHONE_INVALID_DATA;
	}

	//2nd packet
	if (!UnpackPRT(recvList.GetPackage(1), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: 2nd packet UnpackPRT return False", __FUNCTION__);
		return SP_E_PHONE_INVALID_DATA;
	}
	uint32 unRecLen = sizeof(L1_LTE_EMPTY_REQ_T) + sizeof(PC_TOOL_LTE_DAT_BAND_REQ_T);
	if (recvLen != unRecLen)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: 2nd packet length != sizeof(PC_TOOL_LTE_DAT_BAND_REQ_T), %d != %d", __FUNCTION__, recvLen, unRecLen);
		return SP_E_PHONE_INVALID_LENGTH;
	}
	else
	{
		memcpy(pBandInfo, ((char*)m_diagBuff) + sizeof(L1_LTE_EMPTY_REQ_T), sizeof(PC_TOOL_LTE_DAT_BAND_REQ_T));	
		return SP_OK;
	}
}

SPRESULT CCaliCmd::His8910LteApc(PC_TOOL_LTE_DAT_TX_REQ_T* req)
{
	if (NULL == req)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid parameters!", __FUNCTION__);
		return SP_E_PHONE_INVALID_PARAMETER;
	}

	LogRawStrA(SPLOGLV_INFO, "HIS8910 LTE APC");  

	L1_LTE_EMPTY_REQ_T L1;    
	ZeroMemory((void* )&L1, sizeof(L1));  

	unsigned short usLength = (unsigned short)(sizeof(L1))
		+ sizeof(PC_TOOL_LTE_DAT_TX_REQ_T);

	L1.SignalSize = usLength;
	L1.SignalCode = TOOL_MPH_LTE_DAT_TX_REQ;

	unsigned char *lpBuf = new unsigned char[usLength];
	if (NULL == lpBuf)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Memory allocation failed.", __FUNCTION__);
		return SP_E_PHONE_ALLOC_MEMORY;
	}
	else
	{
		ZeroMemory((void *)lpBuf, usLength);
	}

	unsigned char *pPos = lpBuf;
	uint16 usLen =  sizeof(L1);
	memcpy(pPos, &L1, usLen);
	//only para
	pPos += usLen;
	usLen = sizeof(PC_TOOL_LTE_DAT_TX_REQ_T);
	memcpy(pPos, req, usLen);

	DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE); 
	//////////////////////////////////////////////////////////////////////////
	CRecvPkgsList recvList;
	recvList.AddCondition(hd);
	recvList.AddCondition(hd);
	SPRESULT res = SendAndRecv(hd, (const void* )lpBuf, usLength, recvList, m_dwTimeOut); 
	delete[] lpBuf;
	if (SP_OK != res)
	{
		return res;
	}
	return SP_OK;

	/*uint32 recvLen = 0;
	//1st packet
	if (!UnpackPRT(recvList.GetPackage(0), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: 1st packet UnpackPRT return False", __FUNCTION__);
		return SP_E_PHONE_INVALID_DATA;
	}
	else if (recvLen != 0)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: 1st packet length != 0", __FUNCTION__);
		return SP_E_PHONE_INVALID_DATA;
	}

	//2nd packet
	if (!UnpackPRT(recvList.GetPackage(1), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: 2nd packet UnpackPRT return False", __FUNCTION__);
		return SP_E_PHONE_INVALID_DATA;
	}
	uint32 unRecLen = sizeof(L1_LTE_EMPTY_REQ_T) + sizeof(PC_TOOL_LTE_DAT_TX_REQ_T);
	if (recvLen != unRecLen)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: 2nd packet length != sizeof(PC_TOOL_LTE_DAT_TX_REQ_T), %d != %d", __FUNCTION__, recvLen, unRecLen);
		return SP_E_PHONE_INVALID_LENGTH;
	}
	else
	{
		memcpy(req, ((char*)m_diagBuff) + sizeof(L1_LTE_EMPTY_REQ_T), sizeof(PC_TOOL_LTE_DAT_TX_REQ_T));	
		return SP_OK;
	}*/
}

SPRESULT CCaliCmd::His8910LteAgc(PC_TOOL_LTE_DAT_RX_REQ_T* req)
{
	if (NULL == req)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid parameters!", __FUNCTION__);
		return SP_E_PHONE_INVALID_PARAMETER;
	}

	LogRawStrA(SPLOGLV_INFO, "HIS8910 LTE AGC");  

	L1_LTE_EMPTY_REQ_T L1;    
	ZeroMemory((void* )&L1, sizeof(L1));  

	unsigned short usLength = (unsigned short)(sizeof(L1))
		+ sizeof(PC_TOOL_LTE_DAT_RX_REQ_T);

	L1.SignalSize = usLength;
	L1.SignalCode = TOOL_MPH_LTE_DAT_RX_REQ;

	unsigned char *lpBuf = new unsigned char[usLength];
	if (NULL == lpBuf)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Memory allocation failed.", __FUNCTION__);
		return SP_E_PHONE_ALLOC_MEMORY;
	}
	else
	{
		ZeroMemory((void *)lpBuf, usLength);
	}

	unsigned char *pPos = lpBuf;
	uint16 usLen =  sizeof(L1);
	memcpy(pPos, &L1, usLen);
	//only para
	pPos += usLen;
	usLen = sizeof(PC_TOOL_LTE_DAT_RX_REQ_T);
	memcpy(pPos, req, usLen);

	DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE); 
	//////////////////////////////////////////////////////////////////////////
	CRecvPkgsList recvList;
	recvList.AddCondition(hd);
	recvList.AddCondition(hd);
	SPRESULT res = SendAndRecv(hd, (const void* )lpBuf, usLength, recvList, m_dwTimeOut); 
	delete[] lpBuf;
	if (SP_OK != res)
	{
		return res;
	}
	return SP_OK;
	
	/*uint32 recvLen = 0;
	//1st packet
	if (!UnpackPRT(recvList.GetPackage(0), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: 1st packet UnpackPRT return False", __FUNCTION__);
		return SP_E_PHONE_INVALID_DATA;
	}
	else if (recvLen != 0)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: 1st packet length != 0", __FUNCTION__);
		return SP_E_PHONE_INVALID_DATA;
	}

	//2nd packet
	if (!UnpackPRT(recvList.GetPackage(1), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: 2nd packet UnpackPRT return False", __FUNCTION__);
		return SP_E_PHONE_INVALID_DATA;
	}
	uint32 unRecLen = sizeof(L1_LTE_EMPTY_REQ_T) + sizeof(PC_TOOL_LTE_DAT_RX_REQ_T);
	if (recvLen != unRecLen)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: 2nd packet length != sizeof(PC_TOOL_LTE_DAT_RX_REQ_T), %d != %d", __FUNCTION__, recvLen, unRecLen);
		return SP_E_PHONE_INVALID_LENGTH;
	}
	else
	{
		memcpy(req, ((char*)m_diagBuff) + sizeof(L1_LTE_EMPTY_REQ_T), sizeof(PC_TOOL_LTE_DAT_RX_REQ_T));	
		return SP_OK;
	}*/
}

SPRESULT CCaliCmd::His8910LteGetRSSI(PC_TOOL_LTE_DAT_RSSI_REQ_T* req)
{
	if (NULL == req)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid parameters!", __FUNCTION__);
		return SP_E_PHONE_INVALID_PARAMETER;
	}

	LogRawStrA(SPLOGLV_INFO, "HIS8910 LTE GET RSSI");  

	L1_LTE_EMPTY_REQ_T L1;
	ZeroMemory((void* )&L1, sizeof(L1));

	unsigned short usLength = (unsigned short)(sizeof(L1))
		+ sizeof(PC_TOOL_LTE_DAT_RSSI_REQ_T);

	L1.SignalSize = usLength;
	L1.SignalCode = TOOL_MPH_LTE_DAT_RSSI_REQ;

	unsigned char *lpBuf = new unsigned char[usLength];
	if (NULL == lpBuf)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Memory allocation failed.", __FUNCTION__);
		return SP_E_PHONE_ALLOC_MEMORY;
	}
	else
	{
		ZeroMemory((void *)lpBuf, usLength);
	}

	unsigned char *pPos = lpBuf;
	uint16 usLen =  sizeof(L1);
	memcpy(pPos, &L1, usLen);
	
	DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE); 

	CRecvPkgsList recvList;
	recvList.AddCondition(hd);
	recvList.AddCondition(hd);
	SPRESULT res = SendAndRecv(hd, (const void* )lpBuf, usLength, recvList, m_dwTimeOut); 
	delete[] lpBuf;
	if (SP_OK != res)
	{
		return res;
	}

	uint32 recvLen = 0;
	//1st packet
	if (!UnpackPRT(recvList.GetPackage(0), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: 1st packet UnpackPRT return False", __FUNCTION__);
		return SP_E_PHONE_INVALID_DATA;
	}
	else if (recvLen != 0)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: 1st packet length != 0", __FUNCTION__);
		return SP_E_PHONE_INVALID_DATA;
	}

	//2nd packet
	if (!UnpackPRT(recvList.GetPackage(1), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: 2nd packet UnpackPRT return False", __FUNCTION__);
		return SP_E_PHONE_INVALID_DATA;
	}
	uint32 unRecLen = sizeof(L1_LTE_EMPTY_REQ_T) + sizeof(PC_TOOL_LTE_DAT_RSSI_REQ_T);
	if (recvLen != unRecLen)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: 2nd packet length != sizeof(PC_TOOL_LTE_DAT_RSSI_REQ_T), %d != %d", __FUNCTION__, recvLen, unRecLen);
		return SP_E_PHONE_INVALID_LENGTH;
	}
	else
	{
		memcpy(req, ((char*)m_diagBuff) + sizeof(L1_LTE_EMPTY_REQ_T), sizeof(PC_TOOL_LTE_DAT_RSSI_REQ_T));	
		return SP_OK;
	}
}

SPRESULT CCaliCmd::His8910LteAfc(PC_TOOL_LTE_DAT_AFC_REQ_T* req)
{
	if (NULL == req)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid parameters!", __FUNCTION__);
		return SP_E_PHONE_INVALID_PARAMETER;
	}

	LogRawStrA(SPLOGLV_INFO, "HIS8910 LTE AFC");  

	L1_LTE_EMPTY_REQ_T L1;    
	ZeroMemory((void* )&L1, sizeof(L1));  

	unsigned short usLength = (unsigned short)(sizeof(L1))
		+ sizeof(PC_TOOL_LTE_DAT_AFC_REQ_T);

	L1.SignalSize = usLength;
	L1.SignalCode = TOOL_MPH_LTE_DAT_AFC_ERQ;

	unsigned char *lpBuf = new unsigned char[usLength];
	if (NULL == lpBuf)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Memory allocation failed.", __FUNCTION__);
		return SP_E_PHONE_ALLOC_MEMORY;
	}
	else
	{
		ZeroMemory((void *)lpBuf, usLength);
	}

	unsigned char *pPos = lpBuf;
	uint16 usLen =  sizeof(L1);
	memcpy(pPos, &L1, usLen);
	//only para
	pPos += usLen;
	usLen = sizeof(PC_TOOL_LTE_DAT_AFC_REQ_T);
	memcpy(pPos, &req->afc, usLen);

	DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE); 
	//////////////////////////////////////////////////////////////////////////
	CRecvPkgsList recvList;
	recvList.AddCondition(hd);
	recvList.AddCondition(hd);
	SPRESULT res = SendAndRecv(hd, (const void* )lpBuf, usLength, recvList, m_dwTimeOut); 
	delete[] lpBuf;
	if (SP_OK != res)
	{
		return res;
	}

	uint32 recvLen = 0;
	//1st packet
	if (!UnpackPRT(recvList.GetPackage(0), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: 1st packet UnpackPRT return False", __FUNCTION__);
		return SP_E_PHONE_INVALID_DATA;
	}
	else if (recvLen != 0)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: 1st packet length != 0", __FUNCTION__);
		return SP_E_PHONE_INVALID_DATA;
	}

	//2nd packet
	if (!UnpackPRT(recvList.GetPackage(1), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: 2nd packet UnpackPRT return False", __FUNCTION__);
		return SP_E_PHONE_INVALID_DATA;
	}
	uint32 unRecLen = sizeof(L1_LTE_EMPTY_REQ_T) + sizeof(PC_TOOL_LTE_DAT_AFC_REQ_T);
	if (recvLen != unRecLen)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: 2nd packet length != sizeof(PC_TOOL_LTE_DAT_AFC_REQ_T), %d != %d", __FUNCTION__, recvLen, unRecLen);
		return SP_E_PHONE_INVALID_LENGTH;
	}
	else
	{
		memcpy(req, ((char*)m_diagBuff) + sizeof(L1_LTE_EMPTY_REQ_T), sizeof(PC_TOOL_LTE_DAT_AFC_REQ_T));	
		return SP_OK;
	}
}

SPRESULT CCaliCmd::His8910LteRegWrite(PC_TOOL_LTE_DAT_REG_WRITE_REQ_T* req)
{
	if (NULL == req)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid parameters!", __FUNCTION__);
		return SP_E_PHONE_INVALID_PARAMETER;
	}

	LogRawStrA(SPLOGLV_INFO, "HIS8910 LTE SPI WRITE");  

	L1_LTE_EMPTY_REQ_T L1;    
	ZeroMemory((void* )&L1, sizeof(L1));  

	unsigned short usLength = (unsigned short)(sizeof(L1))
		+ sizeof(PC_TOOL_LTE_DAT_REG_WRITE_REQ_T);

	L1.SignalSize = usLength;
	L1.SignalCode = TOOL_MPH_LTE_DAT_REG_WRITE_REQ;

	unsigned char *lpBuf = new unsigned char[usLength];
	if (NULL == lpBuf)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Memory allocation failed.", __FUNCTION__);
		return SP_E_PHONE_ALLOC_MEMORY;
	}
	else
	{
		ZeroMemory((void *)lpBuf, usLength);
	}

	unsigned char *pPos = lpBuf;
	uint16 usLen =  sizeof(L1);
	memcpy(pPos, &L1, usLen);
	//only para
	pPos += usLen;
	usLen = sizeof(PC_TOOL_LTE_DAT_REG_WRITE_REQ_T);
	memcpy(pPos, &req->wpara, usLen);

	DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE); 
	//////////////////////////////////////////////////////////////////////////
	CRecvPkgsList recvList;
	recvList.AddCondition(hd);
	recvList.AddCondition(hd);
	SPRESULT res = SendAndRecv(hd, (const void* )lpBuf, usLength, recvList, m_dwTimeOut); 
	delete[] lpBuf;
	if (SP_OK != res)
	{
		return res;
	}

	uint32 recvLen = 0;
	//1st packet
	if (!UnpackPRT(recvList.GetPackage(0), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: 1st packet UnpackPRT return False", __FUNCTION__);
		return SP_E_PHONE_INVALID_DATA;
	}
	else if (recvLen != 0)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: 1st packet length != 0", __FUNCTION__);
		return SP_E_PHONE_INVALID_DATA;
	}

	//2nd packet
	if (!UnpackPRT(recvList.GetPackage(1), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: 2nd packet UnpackPRT return False", __FUNCTION__);
		return SP_E_PHONE_INVALID_DATA;
	}
	uint32 unRecLen = sizeof(L1_LTE_EMPTY_REQ_T) + sizeof(PC_TOOL_LTE_DAT_REG_WRITE_REQ_T);
	if (recvLen != unRecLen)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: 2nd packet length != sizeof(PC_TOOL_LTE_DAT_REG_WRITE_REQ_T), %d != %d", __FUNCTION__, recvLen, unRecLen);
		return SP_E_PHONE_INVALID_LENGTH;
	}
	else
	{
		memcpy(req, ((char*)m_diagBuff) + sizeof(L1_LTE_EMPTY_REQ_T), sizeof(PC_TOOL_LTE_DAT_REG_WRITE_REQ_T));	
		return SP_OK;
	}
}

SPRESULT CCaliCmd::His8910LteRegRead(PC_TOOL_LTE_DAT_REG_READ_REQ_T* rlt)
{
	if (NULL == rlt)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid parameters!", __FUNCTION__);
		return SP_E_PHONE_INVALID_PARAMETER;
	}

	LogRawStrA(SPLOGLV_INFO, "HIS8910 LTE SPI READ");  

	L1_LTE_EMPTY_REQ_T L1;    
	ZeroMemory((void* )&L1, sizeof(L1));  

	unsigned short usLength = (unsigned short)(sizeof(L1))
		+ sizeof(PC_TOOL_LTE_DAT_REG_READ_REQ_T);

	L1.SignalSize = usLength;
	L1.SignalCode = TOOL_MPH_LTE_DAT_REG_READ_REQ;

	unsigned char *lpBuf = new unsigned char[usLength];
	if (NULL == lpBuf)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Memory allocation failed.", __FUNCTION__);
		return SP_E_PHONE_ALLOC_MEMORY;
	}
	else
	{
		ZeroMemory((void *)lpBuf, usLength);
	}

	unsigned char *pPos = lpBuf;
	uint16 usLen =  sizeof(L1);
	memcpy(pPos, &L1, usLen);
	//only para
	pPos += usLen;
	usLen = sizeof(PC_TOOL_LTE_DAT_REG_READ_REQ_T);
	memcpy(pPos, &rlt->rpara, usLen);

	DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE); 
	//////////////////////////////////////////////////////////////////////////
	CRecvPkgsList recvList;
	recvList.AddCondition(hd);
	recvList.AddCondition(hd);
	SPRESULT res = SendAndRecv(hd, (const void* )lpBuf, usLength, recvList, m_dwTimeOut); 
	delete[] lpBuf;
	if (SP_OK != res)
	{
		return res;
	}

	uint32 recvLen = 0;
	//1st packet
	if (!UnpackPRT(recvList.GetPackage(0), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: 1st packet UnpackPRT return False", __FUNCTION__);
		return SP_E_PHONE_INVALID_DATA;
	}
	else if (recvLen != 0)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: 1st packet length != 0", __FUNCTION__);
		return SP_E_PHONE_INVALID_DATA;
	}

	//2nd packet
	if (!UnpackPRT(recvList.GetPackage(1), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: 2nd packet UnpackPRT return False", __FUNCTION__);
		return SP_E_PHONE_INVALID_DATA;
	}
	uint32 unRecLen = sizeof(L1_LTE_EMPTY_REQ_T) + sizeof(PC_TOOL_LTE_DAT_REG_READ_REQ_T);
	if (recvLen != unRecLen)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: 2nd packet length != sizeof(PC_TOOL_LTE_DAT_REG_READ_REQ_T), %d != %d", __FUNCTION__, recvLen, unRecLen);
		return SP_E_PHONE_INVALID_LENGTH;
	}
	else
	{
		memcpy(rlt, ((char*)m_diagBuff) + sizeof(L1_LTE_EMPTY_REQ_T), sizeof(PC_TOOL_LTE_DAT_REG_READ_REQ_T));	
		return SP_OK;
	}
}

SPRESULT CCaliCmd::lteApc_UIS8910( const PC_LTE_APC_UIS8910_T* req )
{
    CheckValidPointer(req);

    LogRawStrA(SPLOGLV_INFO, "LTE APC UIS8910");  

    L1_LTE_EMPTY_REQ_T    L1;    
    ZeroMemory((void* )&L1, sizeof(L1));   

    uint16 usLength =  (uint16)(sizeof(L1) + sizeof(PC_LTE_APC_UIS8910_HEADER_T) + req->Header.ChannelCount * req->Header.ChannelSize + req->Header.AptCount * req->Header.AptSize);

    L1.SignalCode   = Convert16(TOOL_MPH_LTE_APC_V3);
    L1.SignalSize   = Convert16(usLength);

    uint8* pBuff = NULL;
    try
    {
        pBuff = new uint8[usLength];
    }
    catch (const std::bad_alloc& /*e*/)
    {
        pBuff = NULL;
    }
    if (NULL == pBuff)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Memory allocation failed.", __FUNCTION__);
        return SP_E_PHONE_ALLOC_MEMORY;
    }

    uint8* pPos = pBuff;
    uint16 usLen = sizeof(L1);
    memcpy(pPos, &L1, usLen);
    //header
    pPos += usLen;
    usLen = sizeof(PC_LTE_APC_UIS8910_HEADER_T);
    memcpy(pPos, &req->Header, usLen);
    //channel
    pPos += usLen;
    usLen = req->Header.ChannelCount * req->Header.ChannelSize;
    memcpy(pPos, req->pChannel, usLen);
    //apt
    pPos += usLen;
    usLen = req->Header.AptCount * req->Header.AptSize;
    memcpy(pPos, req->pApt, usLen);

    DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE); 
    SPRESULT result = SendAndRecv(hd, (const void* )pBuff, usLength, hd, NULL, 0, NULL, m_dwTimeOut);

    delete[] pBuff;

    return result;
}

SPRESULT CCaliCmd::LteAgc_UIS8910(const PC_LTE_AGC_UIS8910_T* pAgcParam, unsigned int* pAgcRet)
{
    if (NULL == pAgcParam || NULL == pAgcRet)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid parameters!", __FUNCTION__);
        return SP_E_PHONE_INVALID_PARAMETER;
    }

    LogRawStrA(SPLOGLV_INFO, "LTE AGC UIS8910");  

    L1_LTE_EMPTY_REQ_T    L1;    
    ZeroMemory((void* )&L1, sizeof(L1));  

    unsigned short usLength = (unsigned short)(sizeof(L1)
        + sizeof(PC_LTE_AGC_UIS8910_HEADER_T)
        + sizeof(PC_LTE_AGC_UIS8910_TRIGGER_T)
        + pAgcParam->Header.ChannelNumber * pAgcParam->Header.ChannelSize
        + pAgcParam->Header.PointNumber * pAgcParam->Header.PointSize);

    L1.SignalSize = usLength;
    L1.SignalCode = TOOL_MPH_LTE_AGC_V3;

    unsigned char *lpBuf = new unsigned char[usLength];
    if (NULL == lpBuf)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Memory allocation failed.", __FUNCTION__);
        return SP_E_PHONE_ALLOC_MEMORY;
    }
    else
    {
        ZeroMemory((void *)lpBuf, usLength);
    }

    unsigned char *pPos = lpBuf;
    uint16 usLen =  sizeof(L1);
    memcpy(pPos, &L1, usLen);
    //Header
    pPos += usLen;
    usLen = sizeof(PC_LTE_AGC_UIS8910_HEADER_T);
    memcpy(pPos, &pAgcParam->Header, usLen);
    //Trigger
    pPos += usLen;
    usLen = sizeof(PC_LTE_AGC_UIS8910_TRIGGER_T);
    memcpy(pPos, &pAgcParam->Trigger, usLen);
    //channel
    pPos += usLen;
    usLen = (unsigned short)(pAgcParam->Header.ChannelNumber * pAgcParam->Header.ChannelSize);
    memcpy(pPos, pAgcParam->Channels, usLen);
    //apt
    pPos += usLen;
    usLen = (unsigned short)(pAgcParam->Header.PointNumber * pAgcParam->Header.PointSize);
    memcpy(pPos, pAgcParam->Points, usLen);
    DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE); 
    //////////////////////////////////////////////////////////////////////////
    CRecvPkgsList recvList;
    recvList.AddCondition(hd);
    recvList.AddCondition(hd);
    SPRESULT res = SendAndRecv(hd, (const void* )lpBuf, usLength, recvList, m_dwTimeOut); 
    delete[] lpBuf;
    if (SP_OK != res)
    {
        return res;
    }

    uint32 recvLen = 0;
    //1st packet
    if (!UnpackPRT(recvList.GetPackage(0), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: 1st packet UnpackPRT return False", __FUNCTION__);
        return SP_E_PHONE_INVALID_DATA;
    }
    else if (recvLen != 0)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: 1st packet length != 0", __FUNCTION__);
        return SP_E_PHONE_INVALID_DATA;
    }

    //2nd packet
    if (!UnpackPRT(recvList.GetPackage(1), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: 2nd packet UnpackPRT return False", __FUNCTION__);
        return SP_E_PHONE_INVALID_DATA;
    }
    uint32 unRecLen = sizeof(L1_LTE_EMPTY_REQ_T) + sizeof(short) + pAgcParam->Header.PointNumber * 4;
    if (recvLen != unRecLen)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: 2nd packet length err, %d != %d", __FUNCTION__, recvLen, unRecLen);
        return SP_E_PHONE_INVALID_LENGTH;
    }
    else
    {
        short RssiNumber = *(short*)(m_diagBuff + sizeof(L1_LTE_EMPTY_REQ_T));
        if (RssiNumber != pAgcParam->Header.PointNumber)
        {
            LogFmtStrA(SPLOGLV_ERROR, "[CCaliCmd::LteAgc_UIS8910] RssiNumber != Header.PointNumber, %d != %d!", RssiNumber, pAgcParam->Header.PointNumber);
            return SP_E_PHONE_INVALID_DATA;
        }
        else
        {
            memcpy(pAgcRet, ((char*)m_diagBuff) + sizeof(L1_LTE_EMPTY_REQ_T) + sizeof(short), RssiNumber * sizeof(int));	
        }
        return SP_OK;
    }
}

SPRESULT CCaliCmd::LteAfcStartReq_UIS8910(const PC_LTE_AFC_START_REQ_UIS8910_T* req)
{
    if (NULL == req)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid parameters!", __FUNCTION__);
        return SP_E_PHONE_INVALID_PARAMETER;
    }

    LogRawStrA(SPLOGLV_INFO, "LTE AFC START UIS8910");  

    L1_LTE_EMPTY_REQ_T L1;    
    ZeroMemory((void* )&L1, sizeof(L1));  

    const uint16 usLength = sizeof(L1) + sizeof(PC_LTE_AFC_START_REQ_UIS8910_T);

    L1.SignalSize = usLength;
    L1.SignalCode = TOOL_MPH_LTE_AFC_START_REQ;

    unsigned char *lpBuf = new unsigned char[usLength];
    if (NULL == lpBuf)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Memory allocation failed.", __FUNCTION__);
        return SP_E_PHONE_ALLOC_MEMORY;
    }
    else
    {
        ZeroMemory((void *)lpBuf, usLength);
    }

    unsigned char *pPos = lpBuf;
    uint16 usLen =  sizeof(L1);
    memcpy(pPos, &L1, usLen);
    //only para
    pPos += usLen;
    usLen = sizeof(PC_LTE_AFC_START_REQ_UIS8910_T);
    memcpy(pPos, req, usLen);

    DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE); 
    //////////////////////////////////////////////////////////////////////////
    CRecvPkgsList recvList;
    recvList.AddCondition(hd);
    recvList.AddCondition(hd);
    SPRESULT res = SendAndRecv(hd, (const void* )lpBuf, usLength, recvList, m_dwTimeOut); 
    delete[] lpBuf;
    return res;
}

SPRESULT CCaliCmd::LteAfcStopReq_UIS8910()
{
    LogRawStrA(SPLOGLV_INFO, "LTE AFC STOP UIS8910");  

    L1_LTE_EMPTY_REQ_T L1;    
    ZeroMemory((void* )&L1, sizeof(L1));  

    const uint16 usLength = sizeof(L1);

    L1.SignalSize = usLength;
    L1.SignalCode = TOOL_MPH_LTE_AFC_STOP_REQ;

    unsigned char *lpBuf = new unsigned char[usLength];
    if (NULL == lpBuf)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Memory allocation failed.", __FUNCTION__);
        return SP_E_PHONE_ALLOC_MEMORY;
    }
    else
    {
        ZeroMemory((void *)lpBuf, usLength);
    }

    unsigned char *pPos = lpBuf;
    uint16 usLen =  sizeof(L1);
    memcpy(pPos, &L1, usLen);

    DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE); 
    //////////////////////////////////////////////////////////////////////////
    CRecvPkgsList recvList;
    recvList.AddCondition(hd);
    recvList.AddCondition(hd);
    SPRESULT res = SendAndRecv(hd, (const void* )lpBuf, usLength, recvList, m_dwTimeOut); 
    delete[] lpBuf;
    return res;
}

SPRESULT CCaliCmd::LteAfcReq_UIS8910(const PC_LTE_AFC_REQ_UIS8910_T* req)
{
    if (NULL == req)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid parameters!", __FUNCTION__);
        return SP_E_PHONE_INVALID_PARAMETER;
    }

    LogRawStrA(SPLOGLV_INFO, "LTE AFC REQ UIS8910");  

    L1_LTE_EMPTY_REQ_T L1;    
    ZeroMemory((void* )&L1, sizeof(L1));  

    const uint16 usLength = sizeof(L1) + sizeof(PC_LTE_AFC_REQ_UIS8910_T);

    L1.SignalSize = usLength;
    L1.SignalCode = TOOL_MPH_LTE_AFC_REQ;

    unsigned char *lpBuf = new unsigned char[usLength];
    if (NULL == lpBuf)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Memory allocation failed.", __FUNCTION__);
        return SP_E_PHONE_ALLOC_MEMORY;
    }
    else
    {
        ZeroMemory((void *)lpBuf, usLength);
    }

    unsigned char *pPos = lpBuf;
    uint16 usLen =  sizeof(L1);
    memcpy(pPos, &L1, usLen);
    //only para
    pPos += usLen;
    usLen = sizeof(PC_LTE_AFC_REQ_UIS8910_T);
    memcpy(pPos, req, usLen);

    DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE); 
    //////////////////////////////////////////////////////////////////////////
    CRecvPkgsList recvList;
    recvList.AddCondition(hd);
    recvList.AddCondition(hd);
    SPRESULT res = SendAndRecv(hd, (const void* )lpBuf, usLength, recvList, m_dwTimeOut); 
    delete[] lpBuf;
    return res;
}

SPRESULT CCaliCmd::LteAfcReq_UIS8910_V2(const PC_LTE_AFC_REQ_UIS8910_V2_T* req)
{
    if (NULL == req)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid parameters!", __FUNCTION__);
        return SP_E_PHONE_INVALID_PARAMETER;
    }

    LogRawStrA(SPLOGLV_INFO, "LTE AFC REQ UIS8910 V2");

    L1_LTE_EMPTY_REQ_T L1;
    ZeroMemory((void*)&L1, sizeof(L1));

    const uint16 usLength = sizeof(L1) + sizeof(PC_LTE_AFC_REQ_UIS8910_V2_T);

    L1.SignalSize = usLength;
    L1.SignalCode = TOOL_MPH_LTE_AFC_REQ_V2;

    unsigned char* lpBuf = new unsigned char[usLength];
    if (NULL == lpBuf)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Memory allocation failed.", __FUNCTION__);
        return SP_E_PHONE_ALLOC_MEMORY;
    }
    else
    {
        ZeroMemory((void*)lpBuf, usLength);
    }

    unsigned char* pPos = lpBuf;
    uint16 usLen = sizeof(L1);
    memcpy(pPos, &L1, usLen);
    //only para
    pPos += usLen;
    usLen = sizeof(PC_LTE_AFC_REQ_UIS8910_V2_T);
    memcpy(pPos, req, usLen);

    DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE);
    //////////////////////////////////////////////////////////////////////////
    CRecvPkgsList recvList;
    recvList.AddCondition(hd);
    recvList.AddCondition(hd);
    SPRESULT res = SendAndRecv(hd, (const void*)lpBuf, usLength, recvList, m_dwTimeOut);
    delete[] lpBuf;
    return res;
}

SPRESULT CCaliCmd::LteAfcPwrReq_UIS8910(const PC_LTE_AFC_PWR_REQ_UIS8910_T* req)
{
    if (NULL == req)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid parameters!", __FUNCTION__);
        return SP_E_PHONE_INVALID_PARAMETER;
    }

    LogRawStrA(SPLOGLV_INFO, "LTE AFC PWR REQ UIS8910");

    L1_LTE_EMPTY_REQ_T L1;
    ZeroMemory((void*)&L1, sizeof(L1));

    const uint16 usLength = sizeof(L1) + sizeof(PC_LTE_AFC_PWR_REQ_UIS8910_T);

    L1.SignalSize = usLength;
    L1.SignalCode = TOOL_MPH_LTE_AFC_PWR_REQ;

    unsigned char* lpBuf = new unsigned char[usLength];
    if (NULL == lpBuf)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Memory allocation failed.", __FUNCTION__);
        return SP_E_PHONE_ALLOC_MEMORY;
    }
    else
    {
        ZeroMemory((void*)lpBuf, usLength);
    }

    unsigned char* pPos = lpBuf;
    uint16 usLen = sizeof(L1);
    memcpy(pPos, &L1, usLen);
    //only para
    pPos += usLen;
    usLen = sizeof(PC_LTE_AFC_PWR_REQ_UIS8910_T);
    memcpy(pPos, req, usLen);

    DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE);
    //////////////////////////////////////////////////////////////////////////
    CRecvPkgsList recvList;
    recvList.AddCondition(hd);
    recvList.AddCondition(hd);
    SPRESULT res = SendAndRecv(hd, (const void*)lpBuf, usLength, recvList, m_dwTimeOut);
    delete[] lpBuf;
    return res;
}

SPRESULT CCaliCmd::LteDcGetIQReq_UIS8910(PC_LTE_DC_GET_IQ_REQ_UIS8910_T* pRet)
{
    if (NULL == pRet)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid parameters!", __FUNCTION__);
        return SP_E_PHONE_INVALID_PARAMETER;
    }

    LogFmtStrA(SPLOGLV_INFO, "%s", pRet->type ? "LTE RxIRR GET IQ UIS8910" : "LTE TxDC GET IQ UIS8910");

    L1_LTE_EMPTY_REQ_T L1;
    ZeroMemory((void*)&L1, sizeof(L1));

    const uint16 usLength = sizeof(L1) + sizeof(PC_LTE_DC_GET_IQ_REQ_UIS8910_T);

    L1.SignalSize = usLength;
    L1.SignalCode = TOOL_MPH_LTE_DC_GET_IQ_REQ;

    unsigned char* lpBuf = new unsigned char[usLength];
    if (NULL == lpBuf)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Memory allocation failed.", __FUNCTION__);
        return SP_E_PHONE_ALLOC_MEMORY;
    }
    else
    {
        ZeroMemory((void*)lpBuf, usLength);
    }

    unsigned char* pPos = lpBuf;
    uint16 usLen = sizeof(L1);
    memcpy(pPos, &L1, usLen);
    //only para
    pPos += usLen;
    usLen = sizeof(PC_LTE_DC_GET_IQ_REQ_UIS8910_T);
    memcpy(pPos, pRet, usLen);

    DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE);
    //////////////////////////////////////////////////////////////////////////
    CRecvPkgsList recvList;
    recvList.AddCondition(hd);
    recvList.AddCondition(hd);
    SPRESULT res = SendAndRecv(hd, (const void*)lpBuf, usLength, recvList, m_dwTimeOut);
    delete[] lpBuf;
    if (SP_OK != res)
    {
        return res;
    }

    uint32 recvLen = 0;
    for (int i = 0; i < recvList.GetPkgsCount(); i++)
    {
        if (!UnpackPRT(recvList.GetPackage(i), NULL, (void*)&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
        {
            LogFmtStrA(SPLOGLV_ERROR, "%s: UnpackPRT return False", __FUNCTION__);
            return SP_E_PHONE_INVALID_DATA;
        }
        if (recvLen >= usLength)
        {
            break;
        }
    }
    if (recvLen < usLength)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid response length %d!", __FUNCTION__, recvLen);
        return SP_E_PHONE_INVALID_LENGTH;
    }
    memcpy(pRet, ((char*)m_diagBuff) + sizeof(L1_LTE_EMPTY_REQ_T), sizeof(PC_LTE_DC_GET_IQ_REQ_UIS8910_T));
    return SP_OK;
}

SPRESULT CCaliCmd::LteDcStartReq_UIS8910(const PC_LTE_DC_START_REQ_UIS8910_T* req)
{
    if (NULL == req)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid parameters!", __FUNCTION__);
        return SP_E_PHONE_INVALID_PARAMETER;
    }

    LogFmtStrA(SPLOGLV_INFO, "%s", req->type ? "LTE RxIRR START UIS8910" : "LTE TxDC START UIS8910");

    L1_LTE_EMPTY_REQ_T L1;
    ZeroMemory((void*)&L1, sizeof(L1));

    const uint16 usLength = sizeof(L1) + sizeof(PC_LTE_DC_START_REQ_UIS8910_T);

    L1.SignalSize = usLength;
    L1.SignalCode = TOOL_MPH_LTE_DC_START_REQ;

    unsigned char* lpBuf = new unsigned char[usLength];
    if (NULL == lpBuf)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Memory allocation failed.", __FUNCTION__);
        return SP_E_PHONE_ALLOC_MEMORY;
    }
    else
    {
        ZeroMemory((void*)lpBuf, usLength);
    }

    unsigned char* pPos = lpBuf;
    uint16 usLen = sizeof(L1);
    memcpy(pPos, &L1, usLen);
    //only para
    pPos += usLen;
    usLen = sizeof(PC_LTE_DC_START_REQ_UIS8910_T);
    memcpy(pPos, req, usLen);

    DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE);
    //////////////////////////////////////////////////////////////////////////
    CRecvPkgsList recvList;
    recvList.AddCondition(hd);
    recvList.AddCondition(hd);
    SPRESULT res = SendAndRecv(hd, (const void*)lpBuf, usLength, recvList, m_dwTimeOut);
    delete[] lpBuf;
    return res;
}

SPRESULT CCaliCmd::LteDcStopReq_UIS8910()
{
    LogRawStrA(SPLOGLV_INFO, "LTE TxDC/RxIRR STOP UIS8910");

    L1_LTE_EMPTY_REQ_T L1;
    ZeroMemory((void*)&L1, sizeof(L1));

    const uint16 usLength = sizeof(L1);

    L1.SignalSize = usLength;
    L1.SignalCode = TOOL_MPH_LTE_DC_STOP_REQ;

    unsigned char* lpBuf = new unsigned char[usLength];
    if (NULL == lpBuf)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Memory allocation failed.", __FUNCTION__);
        return SP_E_PHONE_ALLOC_MEMORY;
    }
    else
    {
        ZeroMemory((void*)lpBuf, usLength);
    }

    unsigned char* pPos = lpBuf;
    uint16 usLen = sizeof(L1);
    memcpy(pPos, &L1, usLen);

    DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE);
    //////////////////////////////////////////////////////////////////////////
    CRecvPkgsList recvList;
    recvList.AddCondition(hd);
    recvList.AddCondition(hd);
    SPRESULT res = SendAndRecv(hd, (const void*)lpBuf, usLength, recvList, m_dwTimeOut);
    delete[] lpBuf;
    return res;
}

SPRESULT CCaliCmd::LteDcSetIQReq_UIS8910(const PC_LTE_DC_SET_IQ_REQ_UIS8910_T* req)
{
    if (NULL == req)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid parameters!", __FUNCTION__);
        return SP_E_PHONE_INVALID_PARAMETER;
    }

    LogFmtStrA(SPLOGLV_INFO, "%s", req->type ? "LTE RxIRR SET IQ REQ UIS8910" : "LTE TxDC SET IQ REQ UIS8910");

    L1_LTE_EMPTY_REQ_T L1;
    ZeroMemory((void*)&L1, sizeof(L1));

    const uint16 usLength = sizeof(L1) + sizeof(PC_LTE_DC_SET_IQ_REQ_UIS8910_T);

    L1.SignalSize = usLength;
    L1.SignalCode = TOOL_MPH_LTE_DC_SET_IQ_REQ;

    unsigned char* lpBuf = new unsigned char[usLength];
    if (NULL == lpBuf)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Memory allocation failed.", __FUNCTION__);
        return SP_E_PHONE_ALLOC_MEMORY;
    }
    else
    {
        ZeroMemory((void*)lpBuf, usLength);
    }

    unsigned char* pPos = lpBuf;
    uint16 usLen = sizeof(L1);
    memcpy(pPos, &L1, usLen);
    //only para
    pPos += usLen;
    usLen = sizeof(PC_LTE_DC_SET_IQ_REQ_UIS8910_T);
    memcpy(pPos, req, usLen);

    DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE);
    //////////////////////////////////////////////////////////////////////////
    CRecvPkgsList recvList;
    recvList.AddCondition(hd);
    recvList.AddCondition(hd);
    SPRESULT res = SendAndRecv(hd, (const void*)lpBuf, usLength, recvList, m_dwTimeOut);
    delete[] lpBuf;
    return res;
}

SPRESULT CCaliCmd::LteDcBWReq_UIS8910(const PC_LTE_DC_BW_REQ_UIS8910_T* req)
{
    if (NULL == req)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid parameters!", __FUNCTION__);
        return SP_E_PHONE_INVALID_PARAMETER;
    }

    LogFmtStrA(SPLOGLV_INFO, "%s", req->type ? "LTE RxIRR BW REQ UIS8910" : "LTE TxDC BW REQ UIS8910");

    L1_LTE_EMPTY_REQ_T L1;
    ZeroMemory((void*)&L1, sizeof(L1));

    const uint16 usLength = sizeof(L1) + sizeof(PC_LTE_DC_BW_REQ_UIS8910_T);

    L1.SignalSize = usLength;
    L1.SignalCode = TOOL_MPH_LTE_DC_BW_REQ;

    unsigned char* lpBuf = new unsigned char[usLength];
    if (NULL == lpBuf)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Memory allocation failed.", __FUNCTION__);
        return SP_E_PHONE_ALLOC_MEMORY;
    }
    else
    {
        ZeroMemory((void*)lpBuf, usLength);
    }

    unsigned char* pPos = lpBuf;
    uint16 usLen = sizeof(L1);
    memcpy(pPos, &L1, usLen);
    //only para
    pPos += usLen;
    usLen = sizeof(PC_LTE_DC_BW_REQ_UIS8910_T);
    memcpy(pPos, req, usLen);

    DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE);
    //////////////////////////////////////////////////////////////////////////
    CRecvPkgsList recvList;
    recvList.AddCondition(hd);
    recvList.AddCondition(hd);
    SPRESULT res = SendAndRecv(hd, (const void*)lpBuf, usLength, recvList, m_dwTimeOut);
    delete[] lpBuf;
    return res;
}

SPRESULT CCaliCmd::LteDcRfCtrlReq_UIS8910(const PC_LTE_DC_RFCTRL_REQ_UIS8910_T* req)
{
    if (NULL == req)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid parameters!", __FUNCTION__);
        return SP_E_PHONE_INVALID_PARAMETER;
    }

    if (1 == req->On)//on
    {
        LogFmtStrA(SPLOGLV_INFO, "%s", req->type ? "LTE RxIRR RFCTRL RXON REQ UIS8910" : "LTE TxDC RFCTRL TXON REQ UIS8910");
    }
    else//off
    {
        LogFmtStrA(SPLOGLV_INFO, "%s", req->type ? "LTE RxIRR RFCTRL RXOFF REQ UIS8910" : "LTE TxDC RFCTRL TXOFF REQ UIS8910");
    }

    L1_LTE_EMPTY_REQ_T L1;
    ZeroMemory((void*)&L1, sizeof(L1));

    const uint16 usLength = sizeof(L1) + sizeof(PC_LTE_DC_RFCTRL_REQ_UIS8910_T);

    L1.SignalSize = usLength;
    L1.SignalCode = TOOL_MPH_LTE_DC_RFCTRL_REQ;

    unsigned char* lpBuf = new unsigned char[usLength];
    if (NULL == lpBuf)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Memory allocation failed.", __FUNCTION__);
        return SP_E_PHONE_ALLOC_MEMORY;
    }
    else
    {
        ZeroMemory((void*)lpBuf, usLength);
    }

    unsigned char* pPos = lpBuf;
    uint16 usLen = sizeof(L1);
    memcpy(pPos, &L1, usLen);
    //only para
    pPos += usLen;
    usLen = sizeof(PC_LTE_DC_RFCTRL_REQ_UIS8910_T);
    memcpy(pPos, req, usLen);

    DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE);
    //////////////////////////////////////////////////////////////////////////
    CRecvPkgsList recvList;
    recvList.AddCondition(hd);
    recvList.AddCondition(hd);
    SPRESULT res = SendAndRecv(hd, (const void*)lpBuf, usLength, recvList, m_dwTimeOut);
    delete[] lpBuf;
    return res;
}

SPRESULT CCaliCmd::LteXtalThermStartReq_UIS8910(const PC_LTE_XTAL_THERM_START_REQ_UIS8910_T* req)
{
    LogRawStrA(SPLOGLV_INFO, "LTE XTAL THERM START UIS8910");  

	CheckValidPointer(req);

    L1_LTE_EMPTY_REQ_T L1;    
    ZeroMemory((void* )&L1, sizeof(L1));  

    const uint16 usLength = sizeof(L1) + sizeof(PC_LTE_XTAL_THERM_START_REQ_UIS8910_T);

    L1.SignalSize = usLength;
    L1.SignalCode = TOOL_MPH_LTE_XTAL_THERM_START_REQ;

    unsigned char* lpBuf = new unsigned char[usLength];
    if (NULL == lpBuf)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Memory allocation failed.", __FUNCTION__);
        return SP_E_PHONE_ALLOC_MEMORY;
    }
    else
    {
        ZeroMemory((void*)lpBuf, usLength);
    }

    unsigned char* pPos = lpBuf;
    uint16 usLen = sizeof(L1);
    memcpy(pPos, &L1, usLen);
    //only para
    pPos += usLen;
    usLen = sizeof(PC_LTE_XTAL_THERM_START_REQ_UIS8910_T);
    memcpy(pPos, req, usLen);

    DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE); 
    //////////////////////////////////////////////////////////////////////////
    CRecvPkgsList recvList;
    recvList.AddCondition(hd);
    recvList.AddCondition(hd);
    SPRESULT res = SendAndRecv(hd, (const void* )lpBuf, usLength, recvList, m_dwTimeOut); 
    delete[] lpBuf;
    return res;
}

SPRESULT CCaliCmd::LteXtalThermStopReq_UIS8910()
{
    LogRawStrA(SPLOGLV_INFO, "LTE XTAL THERM STOP UIS8910");  

    L1_LTE_EMPTY_REQ_T L1;    
    ZeroMemory((void* )&L1, sizeof(L1));  

    const uint16 usLength = sizeof(L1);

    L1.SignalSize = usLength;
    L1.SignalCode = TOOL_MPH_LTE_XTAL_THERM_STOP_REQ;

    unsigned char* lpBuf = new unsigned char[usLength];
    if (NULL == lpBuf)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Memory allocation failed.", __FUNCTION__);
        return SP_E_PHONE_ALLOC_MEMORY;
    }
    else
    {
        ZeroMemory((void*)lpBuf, usLength);
    }

    unsigned char* pPos = lpBuf;
    uint16 usLen = sizeof(L1);
    memcpy(pPos, &L1, usLen);

    DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE); 
    //////////////////////////////////////////////////////////////////////////
    CRecvPkgsList recvList;
    recvList.AddCondition(hd);
    recvList.AddCondition(hd);
    SPRESULT res = SendAndRecv(hd, (const void* )lpBuf, usLength, recvList, m_dwTimeOut); 
    delete[] lpBuf;
    return res;
}

SPRESULT CCaliCmd::LteXtalThermReq_UIS8910(PC_LTE_XTAL_THERM_REQ_UIS8910_T* req)
{
    LogRawStrA(SPLOGLV_INFO, "LTE XTAL THERM REQ UIS8910");

	CheckValidPointer(req);

    L1_LTE_EMPTY_REQ_T L1;    
    ZeroMemory((void* )&L1, sizeof(L1));  

    const uint16 usLength = sizeof(L1) + sizeof(PC_LTE_XTAL_THERM_REQ_UIS8910_T);

    L1.SignalSize = usLength;
    L1.SignalCode = TOOL_MPH_LTE_XTAL_THERM_REQ;

    unsigned char* lpBuf = new unsigned char[usLength];
    if (NULL == lpBuf)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Memory allocation failed.", __FUNCTION__);
        return SP_E_PHONE_ALLOC_MEMORY;
    }
    else
    {
        ZeroMemory((void*)lpBuf, usLength);
    }

    unsigned char* pPos = lpBuf;
    uint16 usLen = sizeof(L1);
    memcpy(pPos, &L1, usLen);
    //only para
    pPos += usLen;
    usLen = sizeof(PC_LTE_XTAL_THERM_REQ_UIS8910_T);
    memcpy(pPos, req, usLen);

    DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE); 
    //////////////////////////////////////////////////////////////////////////
    CRecvPkgsList recvList;
    recvList.AddCondition(hd);
    recvList.AddCondition(hd);
    SPRESULT res = SendAndRecv(hd, (const void* )lpBuf, usLength, recvList, m_dwTimeOut); 
    delete[] lpBuf;
    if (SP_OK != res)
    {
        return res;
    }

    uint32 recvLen = 0;

	for(int i=0; i<recvList.GetPkgsCount(); i++)
	{
		if (!UnpackPRT(recvList.GetPackage(i), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
		{
            LogFmtStrA(SPLOGLV_ERROR, "%s: UnpackPRT return False", __FUNCTION__);
            return SP_E_PHONE_INVALID_DATA;
		}
		if (recvLen >= usLength)
		{
			break;
		}
	}
	if (recvLen < usLength)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid response length %d!", __FUNCTION__, recvLen);
		return SP_E_PHONE_INVALID_LENGTH;
	}

    memcpy(req, ((char*)m_diagBuff) + sizeof(L1_LTE_EMPTY_REQ_T), sizeof(PC_LTE_XTAL_THERM_REQ_UIS8910_T));
	return SP_OK;
}

SPRESULT CCaliCmd::LteXtalThermReqV2_UIS8910(PC_LTE_XTAL_THERM_REQ_UIS8910_V2_T* req)
{
    LogRawStrA(SPLOGLV_INFO, "LTE XTAL THERM REQ UIS8910 V2");

    CheckValidPointer(req);

    L1_LTE_EMPTY_REQ_T L1;
    ZeroMemory((void*)&L1, sizeof(L1));

    const uint16 usLength = sizeof(L1) + sizeof(PC_LTE_XTAL_THERM_REQ_UIS8910_V2_T);

    L1.SignalSize = usLength;
    L1.SignalCode = TOOL_MPH_LTE_XTAL_THERM_REQ_V2;

    unsigned char* lpBuf = new unsigned char[usLength];
    if (NULL == lpBuf)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Memory allocation failed.", __FUNCTION__);
        return SP_E_PHONE_ALLOC_MEMORY;
    }
    else
    {
        ZeroMemory((void*)lpBuf, usLength);
    }

    unsigned char* pPos = lpBuf;
    uint16 usLen = sizeof(L1);
    memcpy(pPos, &L1, usLen);
    //only para
    pPos += usLen;
    usLen = sizeof(PC_LTE_XTAL_THERM_REQ_UIS8910_V2_T);
    memcpy(pPos, req, usLen);

    DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE);
    //////////////////////////////////////////////////////////////////////////
    CRecvPkgsList recvList;
    recvList.AddCondition(hd);
    recvList.AddCondition(hd);
    SPRESULT res = SendAndRecv(hd, (const void*)lpBuf, usLength, recvList, m_dwTimeOut);
    delete[] lpBuf;
    if (SP_OK != res)
    {
        return res;
    }

    uint32 recvLen = 0;
    for (int i = 0; i < recvList.GetPkgsCount(); i++)
    {
        if (!UnpackPRT(recvList.GetPackage(i), NULL, (void*)&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
        {
            LogFmtStrA(SPLOGLV_ERROR, "%s: UnpackPRT return False", __FUNCTION__);
            return SP_E_PHONE_INVALID_DATA;
        }
        if (recvLen >= usLength)
        {
            break;
        }
    }
    if (recvLen < usLength)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid response length %d!", __FUNCTION__, recvLen);
        return SP_E_PHONE_INVALID_LENGTH;
    }
    memcpy(req, ((char*)m_diagBuff) + sizeof(L1_LTE_EMPTY_REQ_T), sizeof(PC_LTE_XTAL_THERM_REQ_UIS8910_V2_T));
    return SP_OK;
}

SPRESULT CCaliCmd::LteModeConfig_UIS8910(PC_LTE_MODE_CONFIG_UIS8910_T* req)
{
    LogRawStrA(SPLOGLV_INFO, "LTE MODE CONFIG REQ UIS8910");  

	CheckValidPointer(req);

    L1_LTE_EMPTY_REQ_T L1;    
    ZeroMemory((void* )&L1, sizeof(L1));  

    unsigned short usLength = (unsigned short)(sizeof(L1))
        + sizeof(PC_LTE_MODE_CONFIG_UIS8910_T);

    L1.SignalSize = usLength;
    L1.SignalCode = TOOL_MPH_LTE_MODE_CONFIG_REQ;

    unsigned char *lpBuf = new unsigned char[usLength];
    if (NULL == lpBuf)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Memory allocation failed.", __FUNCTION__);
        return SP_E_PHONE_ALLOC_MEMORY;
    }
    else
    {
        ZeroMemory((void *)lpBuf, usLength);
    }

    unsigned char *pPos = lpBuf;
    uint16 usLen =  sizeof(L1);
    memcpy(pPos, &L1, usLen);
    //only para
    pPos += usLen;
    usLen = sizeof(PC_LTE_MODE_CONFIG_UIS8910_T);
    memcpy(pPos, req, usLen);

    DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE); 
    //////////////////////////////////////////////////////////////////////////
    CRecvPkgsList recvList;
    recvList.AddCondition(hd);
    recvList.AddCondition(hd);
    SPRESULT res = SendAndRecv(hd, (const void* )lpBuf, usLength, recvList, m_dwTimeOut); 
    delete[] lpBuf;
    if (SP_OK != res)
    {
        return res;
    }

    uint32 recvLen = 0;
    //1st packet
    if (!UnpackPRT(recvList.GetPackage(0), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: 1st packet UnpackPRT return False", __FUNCTION__);
        return SP_E_PHONE_INVALID_DATA;
    }

    //2nd packet
    if (!UnpackPRT(recvList.GetPackage(1), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: 2nd packet UnpackPRT return False", __FUNCTION__);
        return SP_E_PHONE_INVALID_DATA;
    }

    return SP_OK;
}

SPRESULT CCaliCmd::LteModeQuery_UIS8910(PC_LTE_MODE_QUERY_UIS8910_T* req)
{
    LogRawStrA(SPLOGLV_INFO, "LTE MODE QUERY REQ UIS8910");  

	CheckValidPointer(req);

    L1_LTE_EMPTY_REQ_T L1;    
    ZeroMemory((void* )&L1, sizeof(L1));  

    uint16 usLen =  sizeof(L1);

    L1.SignalSize = usLen;
    L1.SignalCode = TOOL_MPH_LTE_MODE_QUERY_REQ;

    unsigned char *lpBuf = new unsigned char[usLen];
    if (NULL == lpBuf)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Memory allocation failed.", __FUNCTION__);
        return SP_E_PHONE_ALLOC_MEMORY;
    }
    else
    {
        ZeroMemory((void *)lpBuf, usLen);
    }

    unsigned char *pPos = lpBuf;
    memcpy(pPos, &L1, usLen);
    //only para
    //pPos += usLen;
    //usLen = sizeof(PC_LTE_MODE_QUERY_UIS8910_T);
    //memcpy(pPos, req, usLen);

    DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE); 
    //////////////////////////////////////////////////////////////////////////
    CRecvPkgsList recvList;
    recvList.AddCondition(hd);
    recvList.AddCondition(hd);
    SPRESULT res = SendAndRecv(hd, (const void* )lpBuf, usLen, recvList, m_dwTimeOut); 
    delete[] lpBuf;
    if (SP_OK != res)
    {
        return res;
    }

    uint32 recvLen = 0;
    //1st packet
    if (!UnpackPRT(recvList.GetPackage(0), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: 1st packet UnpackPRT return False", __FUNCTION__);
        return SP_E_PHONE_INVALID_DATA;
    }

    //2nd packet
    if (!UnpackPRT(recvList.GetPackage(1), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: 2nd packet UnpackPRT return False", __FUNCTION__);
        return SP_E_PHONE_INVALID_DATA;
    }

	uint32 unRecLen = sizeof(L1_LTE_EMPTY_REQ_T) + sizeof(PC_LTE_MODE_QUERY_UIS8910_T);
	if (recvLen != unRecLen)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: 2nd packet length != sizeof(PC_LTE_MODE_QUERY_UIS8910_T), %d != %d", __FUNCTION__, recvLen, unRecLen);
		return SP_E_PHONE_INVALID_LENGTH;
	}
	else
	{
		memcpy(req, ((char*)m_diagBuff) + sizeof(L1_LTE_EMPTY_REQ_T), sizeof(PC_LTE_MODE_QUERY_UIS8910_T));	
		return SP_OK;
	}
}

SPRESULT CCaliCmd::LteTxDroop_UIS8910(const PC_V3_LTE_TXDROOP_PARAM *req)
{
	CheckValidPointer(req);
	LogRawStrA(SPLOGLV_INFO, "Lte_TxDroop Cal:");

	L1_LTE_EMPTY_REQ_T L1;    
	ZeroMemory((void* )&L1, sizeof(L1));

	uint16 usLength =  (uint16)(sizeof(L1) + sizeof(PC_V3_LTE_TXDROOP_HEADER_T)
		+ req->Header.arfcn_count*req->Header.arfcn_size);

    L1.SignalSize = usLength;
    L1.SignalCode =  Convert16(TOOL_MPH_LTE_TXDROOP_REQ);
	//////////////////////////////////////////////////////////////////////////
    uint8* pBuff = NULL;
    try
    {
        pBuff = new uint8[usLength];
    }
    catch (const std::bad_alloc& /*e*/)
    {
        pBuff = NULL;
    }
    if (NULL == pBuff)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Memory allocation failed.", __FUNCTION__);
        return SP_E_PHONE_ALLOC_MEMORY;
    }
	else
    {
        ZeroMemory((void *)pBuff, usLength);
    }

    uint8 *pPos = pBuff;
	uint16 usLen =  sizeof(L1);
	memcpy((void *)pPos, (void *)(&L1), usLen);
	//Header
	pPos += usLen;
	usLen = sizeof(PC_V3_LTE_TXDROOP_HEADER_T);
	memcpy((void *)pPos, (void *)(&(req->Header)), usLen);
	//arfcn
	pPos += usLen;
	usLen = req->Header.arfcn_count*req->Header.arfcn_size;
	memcpy((void *)pPos, (void *)(req->txdroop_arfcn), usLen);

	DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE);
	SPRESULT res = SendAndRecv(hd, (const void* )pBuff, usLength, hd, NULL, 0, NULL, m_dwTimeOut); 

	delete[] pBuff;

	return res;
}

SPRESULT CCaliCmd::LtePdt_UIS8910(const PC_LTE_PDT_V3_T* req, PC_LTE_PDT_RESULT_T* rlt)
{
    CheckValidPointer(req);

    LogRawStrA(SPLOGLV_INFO, "LTE Pdt UIS8910");

    L1_LTE_EMPTY_REQ_T L1;
    ZeroMemory(&L1, sizeof(L1_LTE_EMPTY_REQ_T));

    unsigned short usLength = (unsigned short)(sizeof(L1)
        + sizeof(PC_LTE_PDT_V2_HEADER_T)
        + req->Header.ChannelNumber * req->Header.ChannelSize
        + req->Header.AptNumber * req->Header.AptSize);

    L1.SignalSize = usLength;
    L1.SignalCode = TOOL_MPH_LTE_PDT_REQ;

    uint8* pBuff = NULL;
    try
    {
        pBuff = new uint8[usLength];
    }
    catch (const std::bad_alloc& /*e*/)
    {
        pBuff = NULL;
    }
    if (NULL == pBuff)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Memory allocation failed.", __FUNCTION__);
        return SP_E_PHONE_ALLOC_MEMORY;
    }

    unsigned char* pPos = pBuff;
    uint32 usLen = sizeof(L1);
    memcpy(pPos, &L1, usLen);
    //Header
    pPos += usLen;
    usLen = sizeof(PC_LTE_PDT_V2_HEADER_T);
    memcpy(pPos, &req->Header, usLen);
    //channel
    pPos += usLen;
    usLen = (unsigned short)(req->Header.ChannelNumber * req->Header.ChannelSize);
    memcpy(pPos, req->Channels, usLen);
    //apt
    pPos += usLen;
    usLen = (unsigned short)(req->Header.AptNumber * req->Header.AptSize);
    memcpy(pPos, req->Apts, usLen);
    DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE);

    CRecvPkgsList recvList;
    recvList.AddCondition(hd);
    recvList.AddCondition(hd);
    SPRESULT res = SendAndRecv(hd, (const void*)pBuff, usLength, recvList, m_dwTimeOut);
    delete[] pBuff;
    if (SP_OK != res)
    {
        return res;
    }

    uint32 recvLen = 0;
    //1st packet
    if (!UnpackPRT(recvList.GetPackage(0), NULL, (void*)&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: 1st packet UnpackPRT return False");
        return SP_E_PHONE_INVALID_DATA;
    }
    else if (recvLen != 0)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: 1st packet length != 0");
        return SP_E_PHONE_INVALID_DATA;
    }

    //2nd packet
    if (!UnpackPRT(recvList.GetPackage(1), NULL, (void*)&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: 2nd packet UnpackPRT return False", __FUNCTION__);
        return SP_E_PHONE_INVALID_DATA;
    }
    //fetch result
    unsigned short RetNumber = *(unsigned short*)(m_diagBuff + sizeof(L1_LTE_EMPTY_REQ_T));
    L1_LTE_PDT_RLT* pRLT = (L1_LTE_PDT_RLT*)m_diagBuff;
    uint16 uType = Convert16(pRLT->SignalCode);
    if (TOOL_MPH_LTE_PDT_REQ != uType)
    {
        LogFmtStrA(SPLOGLV_ERROR, "Invalid response command ID: 0x%X != 0x%X", uType, TOOL_MPH_LTE_PDT_REQ);
        return SP_E_PHONE_INVALID_DATA;
    }
    rlt->nCount = RetNumber;

    memcpy(rlt->Value, ((char*)m_diagBuff) + sizeof(L1_LTE_EMPTY_REQ_T) + sizeof(unsigned short), RetNumber * sizeof(unsigned short));

    return SP_OK;
}


SPRESULT CCaliCmd::lteLoadNV_UIS8910(PC_LTE_NV_UIS8910_DATA_T* nv)
{
    CheckValidPointer(nv);

    LogFmtStrA(SPLOGLV_INFO, "Load LTE NV UIS8910: nv = %d", nv->header.eNvType);

    DeclareDiagHeader(hd, DIAG_LTE_NV, LTE_NV_READ); 

    uint32 recvLen = 0;
    SPRESULT res = SendAndRecv(hd, (const void* )nv, sizeof(PC_LTE_NV_UIS8910_HEADER_T), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);

    if (SP_OK == res)
    {
        PC_LTE_NV_UIS8910_DATA_T* pRLT = (PC_LTE_NV_UIS8910_DATA_T* )m_diagBuff;

        nv->header.DataSize = Convert16(pRLT->header.DataSize);
        nv->header.Position = Convert16(pRLT->header.Position);

        if (nv->header.DataSize > MAX_PC_LTE_NV_LEN_UIS8910)
        {
            LogFmtStrA(SPLOGLV_ERROR, "SP_lteLoadNV_UIS8910 Invalid Data size: %d > Max Size: %d", nv->header.DataSize, MAX_PC_LTE_NV_LEN_UIS8910);
            return SP_E_PHONE_INVALID_LENGTH;
        }
        else if (recvLen != (nv->header.DataSize + sizeof(PC_LTE_NV_UIS8910_HEADER_T)) ) 
        {
            LogFmtStrA(SPLOGLV_ERROR, "SP_lteLoadNV_UIS8910 Invalid response size: %d != (nv->DataSize + 4) * sizeof(int16): %d", \
                nv->header.DataSize, (nv->header.DataSize + sizeof(PC_LTE_NV_UIS8910_HEADER_T)));
            return SP_E_PHONE_INVALID_LENGTH;
        }

        memcpy(&nv->nData[0], &pRLT->nData[0], nv->header.DataSize);


    }

    return res;
}


SPRESULT CCaliCmd::lteSaveNV_UIS8910(const PC_LTE_NV_UIS8910_DATA_T* nv)
{

    CheckValidPointer(nv);

    LogFmtStrA(SPLOGLV_INFO, "Save LTE NV UIS8910: nv = %d", nv->header.eNvType);

    DeclareDiagHeader(hd, DIAG_LTE_NV, LTE_NV_WRITE); 

    if (nv->header.DataSize > MAX_PC_LTE_NV_LEN_UIS8910)
    {
        LogFmtStrA(SPLOGLV_ERROR, "SP_lteSaveNV_UIS8910 Invalid Data size: %d > Max Size: %d", nv->header.DataSize, MAX_PC_LTE_NV_LEN_UIS8910);
        return SP_E_PHONE_INVALID_LENGTH;
    }

    uint32 usLength = sizeof(PC_LTE_NV_UIS8910_HEADER_T) + nv->header.DataSize;

    SPRESULT ret = SendAndRecv(hd, (const void* )nv, usLength, hd, NULL, 0, NULL, m_dwTimeOut);

#if 0
    if (ret == SP_OK && m_pContainer != NULL)
    {
        std::wstring strKey = CLteUtility::GetShareKey(CLteUtility::V3, nv->eNvType, nv->Band, nv->Indicator);
        m_pContainer->SetValue(strKey.c_str(), nv->nData, nv->DataSize * sizeof(nv->nData[0]));
    }
#endif

    return ret;
}

SPRESULT CCaliCmd::lteNST_Sync_UIS8910(const PC_LTE_NST_SYNC_T* req, LTE_NST_STATUS_E* lpStatus)
{
    if (NULL == req || NULL == lpStatus)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid parameters!", __FUNCTION__);
        return SP_E_PHONE_INVALID_PARAMETER;
    }

    LogFmtStrA(SPLOGLV_INFO, "LTE NST SYNC V2: Cell = %d, arfcn = %d, RB = %d,(%d), BW = %d, MCS = %d, TDD = %d, CellPower = %.1f, RTNI = %d, version = %d, precoding = %d, Ant = %d", \
        req->Cell_id, 
        req->Arfcn, 
        req->RB_num, 
        req->RB_pos, 
        req->BW, 
        req->MCS, 
        req->TDD_frameconfig, 
        req->CellPower, 
        req->RTNI, 
        req->redundancy_version, 
        req->precoding_information, 
        req->Ant
        );

    L1_LTE_NST_SYNC_UIS8910_REQ_T L1;
    ZeroMemory((void* )&L1,  sizeof(L1));
    L1.SignalCode      = Convert16(TOOL_MPH_LTE_NST_SYNC_V2);
    L1.SignalSize      = Convert16((uint16)sizeof(L1));

    L1.Cell_id                  = Convert16(req->Cell_id);
    L1.Arfcn                    = Convert32(req->Arfcn);
    L1.RB_num                   = (uint8)req->RB_num;
    L1.RB_pos                   = (uint8)req->RB_pos;
    L1.BW                       = (uint8)req->BW;
    L1.MCS                      = (uint8)req->MCS;
    L1.TDD_frameconfig          = (uint8)req->TDD_frameconfig;
    L1.CellPower                = Convert16((uint16)(req->CellPower*10));
    L1.RTNI                     = Convert16(req->RTNI);
    L1.redundancy_version       = Convert16(req->redundancy_version);
    L1.precoding_information    = Convert16(req->precoding_information);
    L1.Ant                      = Convert16(req->Ant);

    DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE); 

    CRecvPkgsList recvList;
    recvList.AddCondition(hd);
    recvList.AddCondition(hd);
    SPRESULT res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), recvList, m_dwTimeOut); 
    if (SP_OK != res)
    {
        return res;
    }

    for (int i = 0; i < recvList.GetPkgsCount(); i++)
    {
        uint32 recvLen = 0;
        if (!UnpackPRT(recvList.GetPackage(i), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
        {
            assert(0);
            continue;
        }

        if (recvLen >= sizeof(L1_LTE_NST_SYNC_RLT_T))
        {
            L1_LTE_NST_SYNC_RLT_T* pRLT = (L1_LTE_NST_SYNC_RLT_T* )m_diagBuff;
            uint16 uType = Convert16(pRLT->SignalCode);
            if (TOOL_MPH_LTE_NST_SYNC_V2 != uType)
            {
                LogFmtStrA(SPLOGLV_ERROR, "Invalid response command ID: 0x%X <> 0x%X", uType, TOOL_MPH_LTE_NST_SYNC_V2);
                return SP_E_PHONE_INVALID_DATA;       
            }

            *lpStatus = static_cast<LTE_NST_STATUS_E>(Convert32(pRLT->status));
            LogFmtStrA(SPLOGLV_INFO, "SYNC status = %d", *lpStatus);

            return SP_OK;
        }
    }

    LogRawStrA(SPLOGLV_ERROR, "Invalid response received!");
    return SP_E_PHONE_INVALID_DATA;
}


SPRESULT CCaliCmd::lteNST_Start_UIS8910(const PC_LTE_NST_CONFIG_T* req)
{
    CheckValidPointer(req);
    uint16 freqNum = 0;

    LogFmtStrA(SPLOGLV_INFO, "LTE NST Start V2: Arfcn Count = %d, Ant = %d", req->arfcn_num, req->Ant);

    L1_LTE_NST_CONFIG_UIS8910_REQ_T L1;
    ZeroMemory((void* )&L1, sizeof(L1));

    L1.SignalCode   = Convert16(TOOL_MPH_LTE_NST_START_TEST_V2);
    L1.SignalSize   = Convert16((uint16)sizeof(L1));

    L1.arfcn_num    = Convert16(req->arfcn_num);
    L1.Ant          = Convert16(req->Ant);
    for (uint16 i = 0; i < req->arfcn_num; i++)
    {
        if(req->arfcn[i].frame_num == 0)
        {
            continue;
        }

        L1.arfcn[freqNum].arfcn      = Convert32(req->arfcn[i].arfcn);
        L1.arfcn[freqNum].frame_num  = Convert16(req->arfcn[i].frame_num);

        // rx
        if (req->arfcn[i].rx.frame_num > req->arfcn[i].frame_num)
        {
            LogFmtStrA(SPLOGLV_ERROR, "Arfcn[%2d] = %d, Invalid rx frame count, %d <= %d", i, req->arfcn[i].arfcn, req->arfcn[i].rx.frame_num, req->arfcn[i].frame_num);
            return SP_E_PHONE_INVALID_PARAMETER;
        }

        L1.arfcn[freqNum].rx.frame_num   = (uint8)req->arfcn[i].rx.frame_num;
        L1.arfcn[freqNum].rx.CellPower   = Convert16((uint16)(req->arfcn[i].rx.CellPower*10));

        // tx
        int   total_tx_frames = 0;
        L1.arfcn[freqNum].tx_count  = req->arfcn[i].tx_count;
        for (uint8 j = 0; j < req->arfcn[i].tx_count; j++)
        {
            total_tx_frames += req->arfcn[i].tx[j].frame_num;

            L1.arfcn[freqNum].tx[j].frame_num        = (uint8)req->arfcn[i].tx[j].frame_num;
            L1.arfcn[freqNum].tx[j].RB_num           = (uint8)req->arfcn[i].tx[j].RB_num;
            L1.arfcn[freqNum].tx[j].RB_pos           = (uint8)req->arfcn[i].tx[j].RB_pos;
            L1.arfcn[freqNum].tx[j].TPC              = (uint8)req->arfcn[i].tx[j].TPC;
            L1.arfcn[freqNum].tx[j].closeloop_power  = (int8 )req->arfcn[i].tx[j].closeloop_power;
            L1.arfcn[freqNum].tx[j].BW               = (uint8)req->arfcn[i].tx[j].BW;
            L1.arfcn[freqNum].tx[j].MCS              = (uint8)req->arfcn[i].tx[j].MCS;
            L1.arfcn[freqNum].tx[j].reserved         = (uint8)req->arfcn[i].tx[j].reserved;
        }

        if (total_tx_frames > req->arfcn[i].frame_num)
        {
            LogFmtStrA(SPLOGLV_ERROR, "Arfcn[%2d] = %d, Invalid total tx frame count, %d <= %d", i, req->arfcn[i].arfcn, total_tx_frames, req->arfcn[i].frame_num);
            return SP_E_PHONE_INVALID_PARAMETER;
        }

        freqNum++;
    }

    L1.arfcn_num = freqNum;

    DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE); 

    return SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, NULL, 0, NULL, m_dwTimeOut);
}

SPRESULT CCaliCmd::lteNST_GetBLER_UIS8910(PC_LTE_NST_SEBLER_UIS8910_T* BLER)
{
    CheckValidPointer(BLER);

    LogFmtStrA(SPLOGLV_INFO, "LTENST BLER:");

    L1_LTE_EMPTY_REQ_T L1;
    ZeroMemory((void* )&L1, sizeof(L1));
    L1.SignalCode   = Convert16(TOOL_MPH_LTE_NST_GET_BLER);
    L1.SignalSize   = Convert16((uint16)sizeof(L1));

    DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE); 

    CRecvPkgsList recvList;
    recvList.AddCondition(hd);
    recvList.AddCondition(hd);
    SPRESULT res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), recvList, m_dwTimeOut); 
    if (SP_OK != res)
    {
        return res;
    }

    for (int nPktIdx = 0; nPktIdx < recvList.GetPkgsCount(); nPktIdx++)
    {
        uint32 recvLen = 0;
        if (!UnpackPRT(recvList.GetPackage(nPktIdx), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
        {
            assert(0);
            continue;
        }

        if (recvLen >= sizeof(L1_LTE_NST_GET_BLER_RLT_UIS8910_T))
        {
            L1_LTE_NST_GET_BLER_RLT_UIS8910_T* pRLT = (L1_LTE_NST_GET_BLER_RLT_UIS8910_T* )m_diagBuff;
            uint16 uType = Convert16(pRLT->SignalCode);
            if (TOOL_MPH_LTE_NST_GET_BLER != uType)
            {
                LogFmtStrA(SPLOGLV_ERROR, "Invalid response command ID: 0x%X <> 0x%X", uType, TOOL_MPH_LTE_NST_GET_BLER);
                return SP_E_PHONE_INVALID_DATA;       
            }

            BLER->status = Convert16(pRLT->status);
            LogFmtStrA(SPLOGLV_INFO, "BLER status = %d!", BLER->status);

            BLER->arfcn_num = Convert16(pRLT->arfcn_num);
            if (BLER->arfcn_num > MAX_PC_LTE_NST_ARFCN_NUM)
            {
                LogFmtStrA(SPLOGLV_INFO, "Invalid BLER Arfcn count = %d!", BLER->arfcn_num);
                return SP_E_PHONE_INVALID_DATA;
            }

            for (uint16 nArfcnIdx = 0; nArfcnIdx <MAX_PC_LTE_NST_ARFCN_NUM; nArfcnIdx++)
            {
                BLER->BLER10[nArfcnIdx] = Convert16(pRLT->BLER10[nArfcnIdx]);
                BLER->disRadioFrame[nArfcnIdx] = Convert16(pRLT->disRadioFrame[nArfcnIdx]);
                //LogFmtStrA(SPLOGLV_INFO, "BLER Arfcn[%2d] = %d", i+1, BLER->BLER10[i]);
            }

            return SP_OK;
        }
    }

    LogRawStrA(SPLOGLV_ERROR, "Invalid response received!");
    return SP_E_PHONE_INVALID_DATA;
}

SPRESULT CCaliCmd::lteNST_GetRSSI_UIS8910(PC_LTE_NST_RSSI_T* RSSI)
{
    CheckValidPointer(RSSI);

    LogRawStrA(SPLOGLV_INFO, "LTE NST RSSI:");

    L1_LTE_EMPTY_REQ_T L1;
    ZeroMemory((void* )&L1, sizeof(L1));
    L1.SignalCode      = Convert16(TOOL_MPH_LTE_NST_GET_RSSI);
    L1.SignalSize      = Convert16((uint16)sizeof(L1));

    DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE); 

    CRecvPkgsList recvList;
    recvList.AddCondition(hd);
    recvList.AddCondition(hd);
    SPRESULT res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), recvList, m_dwTimeOut); 
    if (SP_OK != res)
    {
        return res;
    }

    for (int nPktIdx = 0; nPktIdx < recvList.GetPkgsCount(); nPktIdx++)
    {
        uint32 recvLen = 0;
        if (!UnpackPRT(recvList.GetPackage(nPktIdx), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
        {
            assert(0);
            continue;
        }

        if (recvLen >= sizeof(L1_LTE_NST_GET_RSSI_RLT_T))
        {
            L1_LTE_NST_GET_RSSI_RLT_T* pRLT = (L1_LTE_NST_GET_RSSI_RLT_T* )m_diagBuff;
            uint16 uType = Convert16(pRLT->SignalCode);
            if (TOOL_MPH_LTE_NST_GET_RSSI != uType)
            {
                LogFmtStrA(SPLOGLV_ERROR, "Invalid response command ID: 0x%X <> 0x%X", uType, TOOL_MPH_LTE_NST_GET_RSSI);
                return SP_E_PHONE_INVALID_DATA;       
            }

            RSSI->status = Convert16(pRLT->status);

            LogFmtStrA(SPLOGLV_INFO, "RSSI status = %d!", RSSI->status);

            RSSI->arfcn_num = Convert16(pRLT->arfcn_num);
            if (RSSI->arfcn_num > MAX_PC_LTE_NST_ARFCN_NUM)
            {
                LogFmtStrA(SPLOGLV_INFO, "Invalid RSSI Arfcn count = %d!", RSSI->arfcn_num);
                return SP_E_PHONE_INVALID_DATA;
            }

            for (uint16 nArfcnIdx = 0; nArfcnIdx <MAX_PC_LTE_NST_ARFCN_NUM; nArfcnIdx++)
                //for (uint16 i = 0; i < RSSI->arfcn_num; i++)
            {
                RSSI->RSSI[nArfcnIdx] = Convert16(pRLT->RSSI[nArfcnIdx]);
                LogFmtStrA(SPLOGLV_INFO, "RSSI Arfcn[%2d] = %d", nArfcnIdx+1, RSSI->RSSI[nArfcnIdx]);
            }

            return SP_OK;
        }
    }

    LogRawStrA(SPLOGLV_ERROR, "Invalid response received!");
    return SP_E_PHONE_INVALID_DATA;
}

SPRESULT CCaliCmd::WifiActive_UIS8910(BOOL bActive)
{
    LogFmtStrA(SPLOGLV_INFO, "%s", bActive ? "wifi Active" : "wifi DeActive");

    L1_LTE_EMPTY_REQ_T L1;
    ZeroMemory((void* )&L1, sizeof(L1));
    if (bActive)
    {
        L1.SignalCode = Convert16(TOOL_MPH_WIFI_ACTIVE);
    }
    else
    {
        L1.SignalCode = Convert16(TOOL_MPH_WIFI_DEACTIVE);
    }
    L1.SignalSize  = Convert16((uint16)sizeof(L1));

    DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE);

    CRecvPkgsList recvList;
    recvList.AddCondition(hd);
    recvList.AddCondition(hd);

    return SendAndRecv(hd, (const void* )&L1, sizeof(L1), recvList, m_dwTimeOut); 
}

SPRESULT CCaliCmd::WifiCalFlag_UIS8910(const PC_WIFI_FLAG_REQ_UIS8910_T* req)
{
    CheckValidPointer(req);

    LogFmtStrA(SPLOGLV_INFO, "Wifi Cal Flag UIS8910");

    L1_LTE_EMPTY_REQ_T L1;
    ZeroMemory((void*)&L1, sizeof(L1));

    unsigned short usLength = (unsigned short)(sizeof(L1))
        + sizeof(PC_WIFI_FLAG_REQ_UIS8910_T);

    L1.SignalSize = usLength;
    L1.SignalCode = TOOL_MPH_WIFI_CAL_FLAG_REQ;


    unsigned char* lpBuf = new unsigned char[usLength];
    if (NULL == lpBuf)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Memory allocation failed.", __FUNCTION__);
        return SP_E_PHONE_ALLOC_MEMORY;
    }
    else
    {
        ZeroMemory((void*)lpBuf, usLength);
    }

    unsigned char* pPos = lpBuf;
    uint16 usLen = sizeof(L1);
    memcpy(pPos, &L1, usLen);
    //only para
    pPos += usLen;
    usLen = sizeof(PC_WIFI_FLAG_REQ_UIS8910_T);
    memcpy(pPos, req, usLen);

    DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE);
    //////////////////////////////////////////////////////////////////////////
    CRecvPkgsList recvList;
    recvList.AddCondition(hd);
    recvList.AddCondition(hd);
    SPRESULT res = SendAndRecv(hd, (const void*)lpBuf, usLength, recvList, m_dwTimeOut);
    delete[] lpBuf;
    if (SP_OK != res)
    {
        return res;
    }

    uint32 recvLen = 0;
    //1st packet
    if (!UnpackPRT(recvList.GetPackage(0), NULL, (void*)&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: 1st packet UnpackPRT return False", __FUNCTION__);
        return SP_E_PHONE_INVALID_DATA;
    }

    //2nd packet
    if (!UnpackPRT(recvList.GetPackage(1), NULL, (void*)&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: 2nd packet UnpackPRT return False", __FUNCTION__);
        return SP_E_PHONE_INVALID_DATA;
    }

    return SP_OK;
}

SPRESULT CCaliCmd::WifiRxReq_UIS8910(BOOL bON, const PC_WIFI_RX_REQ_UIS8910_T* req)
{
    CheckValidPointer(req);

    LogFmtStrA(SPLOGLV_INFO, "%s", bON ? "Wifi RxOn UIS8910" : "Wifi RxOff UIS8910");

    L1_LTE_EMPTY_REQ_T L1;
    ZeroMemory((void*)&L1, sizeof(L1));

    unsigned short usLength = (unsigned short)(sizeof(L1))
        + sizeof(PC_WIFI_RX_REQ_UIS8910_T);

    L1.SignalSize = usLength;
    if (bON)
    {
        L1.SignalCode = TOOL_MPH_WIFI_RXON_REQ;
    }
    else
    {
        L1.SignalCode = TOOL_MPH_WIFI_RXOFF_REQ;
    }
    unsigned char* lpBuf = new unsigned char[usLength];
    if (NULL == lpBuf)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Memory allocation failed.", __FUNCTION__);
        return SP_E_PHONE_ALLOC_MEMORY;
    }
    else
    {
        ZeroMemory((void*)lpBuf, usLength);
    }

    unsigned char* pPos = lpBuf;
    uint16 usLen = sizeof(L1);
    memcpy(pPos, &L1, usLen);
    //only para
    pPos += usLen;
    usLen = sizeof(PC_WIFI_RX_REQ_UIS8910_T);
    memcpy(pPos, req, usLen);

    DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE);
    //////////////////////////////////////////////////////////////////////////
    CRecvPkgsList recvList;
    recvList.AddCondition(hd);
    recvList.AddCondition(hd);
    SPRESULT res = SendAndRecv(hd, (const void*)lpBuf, usLength, recvList, m_dwTimeOut);
    delete[] lpBuf;
    if (SP_OK != res)
    {
        return res;
    }

    uint32 recvLen = 0;
    //1st packet
    if (!UnpackPRT(recvList.GetPackage(0), NULL, (void*)&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: 1st packet UnpackPRT return False", __FUNCTION__);
        return SP_E_PHONE_INVALID_DATA;
    }

    //2nd packet
    if (!UnpackPRT(recvList.GetPackage(1), NULL, (void*)&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: 2nd packet UnpackPRT return False", __FUNCTION__);
        return SP_E_PHONE_INVALID_DATA;
    }

    return SP_OK;
}

SPRESULT CCaliCmd::WifiGetRSSI_UIS8910(PC_WIFI_RSSI_REQ_UIS8910_T* RSSI)
{
    CheckValidPointer(RSSI);

    LogRawStrA(SPLOGLV_INFO, "Wifi Get RSSI:");

    L1_LTE_EMPTY_REQ_T L1;
    ZeroMemory((void*)&L1, sizeof(L1));
    L1.SignalCode = Convert16(TOOL_MPH_WIFI_RSSI_REQ);
    L1.SignalSize = Convert16((uint16)sizeof(L1));

    unsigned short usRltLength = (unsigned short)(sizeof(L1))
        + sizeof(PC_WIFI_RSSI_REQ_UIS8910_T);

    DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE);

    CRecvPkgsList recvList;
    recvList.AddCondition(hd);
    recvList.AddCondition(hd);
    SPRESULT res = SendAndRecv(hd, (const void*)&L1, sizeof(L1), recvList, m_dwTimeOut);
    if (SP_OK != res)
    {
        return res;
    }

    for (int nPktIdx = 0; nPktIdx < recvList.GetPkgsCount(); nPktIdx++)
    {
        uint32 recvLen = 0;
        if (!UnpackPRT(recvList.GetPackage(nPktIdx), NULL, (void*)&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
        {
            assert(0);
            continue;
        }

        if (recvLen >= usRltLength)
        {
            L1_LTE_EMPTY_REQ_T* pHead = (L1_LTE_EMPTY_REQ_T*)m_diagBuff;
            uint16 uType = Convert16(pHead->SignalCode);
            if (TOOL_MPH_WIFI_RSSI_REQ != uType)
            {
                LogFmtStrA(SPLOGLV_ERROR, "Invalid response command ID: 0x%X <> 0x%X", uType, TOOL_MPH_WIFI_RSSI_REQ);
                return SP_E_PHONE_INVALID_DATA;
            }

            PC_WIFI_RSSI_REQ_UIS8910_T* pRlt = (PC_WIFI_RSSI_REQ_UIS8910_T*)(m_diagBuff + sizeof(L1));
            memcpy(RSSI, pRlt, sizeof(PC_WIFI_RSSI_REQ_UIS8910_T));
            return SP_OK;
        }
    }

    LogRawStrA(SPLOGLV_ERROR, "Invalid response received!");
    return SP_E_PHONE_INVALID_DATA;
}

SPRESULT CCaliCmd::WifiNstInit_UIS8910(BOOL bActive)
{
    LogFmtStrA(SPLOGLV_INFO, "%s", bActive ? "wifi NstInit" : "wifi NstStop");

    L1_LTE_EMPTY_REQ_T L1;
    ZeroMemory((void*)&L1, sizeof(L1));
    if (bActive)
    {
        L1.SignalCode = Convert16(TOOL_MPH_WIFI_NST_INIT);
    }
    else
    {
        L1.SignalCode = Convert16(TOOL_MPH_WIFI_NST_STOP);
    }
    L1.SignalSize = Convert16((uint16)sizeof(L1));

    DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE);

    CRecvPkgsList recvList;
    recvList.AddCondition(hd);
    recvList.AddCondition(hd);

    return SendAndRecv(hd, (const void*)&L1, sizeof(L1), recvList, m_dwTimeOut);
}

SPRESULT CCaliCmd::WifiNstFlag_UIS8910(const PC_WIFI_FLAG_REQ_UIS8910_T* req)
{
    CheckValidPointer(req);

    LogFmtStrA(SPLOGLV_INFO, "Wifi Nst Flag UIS8910");

    L1_LTE_EMPTY_REQ_T L1;
    ZeroMemory((void*)&L1, sizeof(L1));

    unsigned short usLength = (unsigned short)(sizeof(L1))
        + sizeof(PC_WIFI_FLAG_REQ_UIS8910_T);

    L1.SignalSize = usLength;
    L1.SignalCode = TOOL_MPH_WIFI_NST_FLAG_REQ;


    unsigned char* lpBuf = new unsigned char[usLength];
    if (NULL == lpBuf)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Memory allocation failed.", __FUNCTION__);
        return SP_E_PHONE_ALLOC_MEMORY;
    }
    else
    {
        ZeroMemory((void*)lpBuf, usLength);
    }

    unsigned char* pPos = lpBuf;
    uint16 usLen = sizeof(L1);
    memcpy(pPos, &L1, usLen);
    //only para
    pPos += usLen;
    usLen = sizeof(PC_WIFI_FLAG_REQ_UIS8910_T);
    memcpy(pPos, req, usLen);

    DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE);
    //////////////////////////////////////////////////////////////////////////
    CRecvPkgsList recvList;
    recvList.AddCondition(hd);
    recvList.AddCondition(hd);
    SPRESULT res = SendAndRecv(hd, (const void*)lpBuf, usLength, recvList, m_dwTimeOut);
    delete[] lpBuf;
    if (SP_OK != res)
    {
        return res;
    }

    uint32 recvLen = 0;
    //1st packet
    if (!UnpackPRT(recvList.GetPackage(0), NULL, (void*)&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: 1st packet UnpackPRT return False", __FUNCTION__);
        return SP_E_PHONE_INVALID_DATA;
    }

    //2nd packet
    if (!UnpackPRT(recvList.GetPackage(1), NULL, (void*)&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: 2nd packet UnpackPRT return False", __FUNCTION__);
        return SP_E_PHONE_INVALID_DATA;
    }

    return SP_OK;
}

SPRESULT CCaliCmd::WifiAntFlag_UIS8910(const PC_WIFI_FLAG_REQ_UIS8910_T* req)
{
    CheckValidPointer(req);

    LogFmtStrA(SPLOGLV_INFO, "Wifi Ant Flag UIS8910");

    L1_LTE_EMPTY_REQ_T L1;
    ZeroMemory((void*)&L1, sizeof(L1));

    unsigned short usLength = (unsigned short)(sizeof(L1))
        + sizeof(PC_WIFI_FLAG_REQ_UIS8910_T);

    L1.SignalSize = usLength;
    L1.SignalCode = TOOL_MPH_WIFI_ANT_FLAG_REQ;


    unsigned char* lpBuf = new unsigned char[usLength];
    if (NULL == lpBuf)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Memory allocation failed.", __FUNCTION__);
        return SP_E_PHONE_ALLOC_MEMORY;
    }
    else
    {
        ZeroMemory((void*)lpBuf, usLength);
    }

    unsigned char* pPos = lpBuf;
    uint16 usLen = sizeof(L1);
    memcpy(pPos, &L1, usLen);
    //only para
    pPos += usLen;
    usLen = sizeof(PC_WIFI_FLAG_REQ_UIS8910_T);
    memcpy(pPos, req, usLen);

    DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE);
    //////////////////////////////////////////////////////////////////////////
    CRecvPkgsList recvList;
    recvList.AddCondition(hd);
    recvList.AddCondition(hd);
    SPRESULT res = SendAndRecv(hd, (const void*)lpBuf, usLength, recvList, m_dwTimeOut);
    delete[] lpBuf;
    if (SP_OK != res)
    {
        return res;
    }

    uint32 recvLen = 0;
    //1st packet
    if (!UnpackPRT(recvList.GetPackage(0), NULL, (void*)&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: 1st packet UnpackPRT return False", __FUNCTION__);
        return SP_E_PHONE_INVALID_DATA;
    }

    //2nd packet
    if (!UnpackPRT(recvList.GetPackage(1), NULL, (void*)&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: 2nd packet UnpackPRT return False", __FUNCTION__);
        return SP_E_PHONE_INVALID_DATA;
    }

    return SP_OK;
}

SPRESULT CCaliCmd::WifiNstRslReq_UIS8910(BOOL bON, const PC_WIFI_RX_REQ_UIS8910_T* req)
{
    CheckValidPointer(req);

    LogFmtStrA(SPLOGLV_INFO, "%s", bON ? "Wifi NstRsl RxOn UIS8910" : "Wifi NstRsl RxOff UIS8910");

    L1_LTE_EMPTY_REQ_T L1;
    ZeroMemory((void*)&L1, sizeof(L1));

    unsigned short usLength = (unsigned short)(sizeof(L1))
        + sizeof(PC_WIFI_RX_REQ_UIS8910_T);

    L1.SignalSize = usLength;
    if (bON)
    {
        L1.SignalCode = TOOL_MPH_WIFI_RSL_REQ;
    }
    else
    {
        L1.SignalCode = TOOL_MPH_WIFI_RSL_REQ;
    }
    unsigned char* lpBuf = new unsigned char[usLength];
    if (NULL == lpBuf)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Memory allocation failed.", __FUNCTION__);
        return SP_E_PHONE_ALLOC_MEMORY;
    }
    else
    {
        ZeroMemory((void*)lpBuf, usLength);
    }

    unsigned char* pPos = lpBuf;
    uint16 usLen = sizeof(L1);
    memcpy(pPos, &L1, usLen);
    //only para
    pPos += usLen;
    usLen = sizeof(PC_WIFI_RX_REQ_UIS8910_T);
    memcpy(pPos, req, usLen);

    DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE);
    //////////////////////////////////////////////////////////////////////////
    CRecvPkgsList recvList;
    recvList.AddCondition(hd);
    recvList.AddCondition(hd);
    SPRESULT res = SendAndRecv(hd, (const void*)lpBuf, usLength, recvList, m_dwTimeOut);
    delete[] lpBuf;
    if (SP_OK != res)
    {
        return res;
    }

    uint32 recvLen = 0;
    //1st packet
    if (!UnpackPRT(recvList.GetPackage(0), NULL, (void*)&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: 1st packet UnpackPRT return False", __FUNCTION__);
        return SP_E_PHONE_INVALID_DATA;
    }

    //2nd packet
    if (!UnpackPRT(recvList.GetPackage(1), NULL, (void*)&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: 2nd packet UnpackPRT return False", __FUNCTION__);
        return SP_E_PHONE_INVALID_DATA;
    }

    return SP_OK;
}

SPRESULT CCaliCmd::WifiNstRslRlt_UIS8910(PC_WIFI_RSL_RLT_REQ_UIS8910_T* RSL)
{
    CheckValidPointer(RSL);

    LogRawStrA(SPLOGLV_INFO, "Wifi Get BER:");

    L1_LTE_EMPTY_REQ_T L1;
    ZeroMemory((void*)&L1, sizeof(L1));
    L1.SignalCode = Convert16(TOOL_MPH_WIFI_RSL_RLT_REQ);
    L1.SignalSize = Convert16((uint16)sizeof(L1));

    unsigned short usRltLength = (unsigned short)(sizeof(L1))
        + sizeof(PC_WIFI_RSL_RLT_REQ_UIS8910_T);

    DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE);

    CRecvPkgsList recvList;
    recvList.AddCondition(hd);
    recvList.AddCondition(hd);
    SPRESULT res = SendAndRecv(hd, (const void*)&L1, sizeof(L1), recvList, m_dwTimeOut);
    if (SP_OK != res)
    {
        return res;
    }

    for (int nPktIdx = 0; nPktIdx < recvList.GetPkgsCount(); nPktIdx++)
    {
        uint32 recvLen = 0;
        if (!UnpackPRT(recvList.GetPackage(nPktIdx), NULL, (void*)&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
        {
            assert(0);
            continue;
        }

        if (recvLen >= usRltLength)
        {
            L1_LTE_EMPTY_REQ_T* pHead = (L1_LTE_EMPTY_REQ_T*)m_diagBuff;
            uint16 uType = Convert16(pHead->SignalCode);
            if (TOOL_MPH_WIFI_RSL_RLT_REQ != uType)
            {
                LogFmtStrA(SPLOGLV_ERROR, "Invalid response command ID: 0x%X <> 0x%X", uType, TOOL_MPH_WIFI_RSL_RLT_REQ);
                return SP_E_PHONE_INVALID_DATA;
            }

            PC_WIFI_RSL_RLT_REQ_UIS8910_T* pRlt = (PC_WIFI_RSL_RLT_REQ_UIS8910_T*)(m_diagBuff + sizeof(L1));
            memcpy(RSL, pRlt, sizeof(PC_WIFI_RSL_RLT_REQ_UIS8910_T));
            return SP_OK;
        }
    }

    LogRawStrA(SPLOGLV_ERROR, "Invalid response received!");
    return SP_E_PHONE_INVALID_DATA;
}

SPRESULT CCaliCmd::WifiNstRxlevelReq_UIS8910(BOOL bON, const PC_WIFI_RX_REQ_UIS8910_T* req)
{
    CheckValidPointer(req);

    LogFmtStrA(SPLOGLV_INFO, "%s", bON ? "Wifi NstRxlevel RxOn UIS8910" : "Wifi NstRxlevel RxOff UIS8910");

    L1_LTE_EMPTY_REQ_T L1;
    ZeroMemory((void*)&L1, sizeof(L1));

    unsigned short usLength = (unsigned short)(sizeof(L1))
        + sizeof(PC_WIFI_RX_REQ_UIS8910_T);

    L1.SignalSize = usLength;
    if (bON)
    {
        L1.SignalCode = TOOL_MPH_WIFI_NST_RSSI_REQ;
    }
    else
    {
        L1.SignalCode = TOOL_MPH_WIFI_NST_RSSI_REQ;
    }
    unsigned char* lpBuf = new unsigned char[usLength];
    if (NULL == lpBuf)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Memory allocation failed.", __FUNCTION__);
        return SP_E_PHONE_ALLOC_MEMORY;
    }
    else
    {
        ZeroMemory((void*)lpBuf, usLength);
    }

    unsigned char* pPos = lpBuf;
    uint16 usLen = sizeof(L1);
    memcpy(pPos, &L1, usLen);
    //only para
    pPos += usLen;
    usLen = sizeof(PC_WIFI_RX_REQ_UIS8910_T);
    memcpy(pPos, req, usLen);

    DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE);
    //////////////////////////////////////////////////////////////////////////
    CRecvPkgsList recvList;
    recvList.AddCondition(hd);
    recvList.AddCondition(hd);
    SPRESULT res = SendAndRecv(hd, (const void*)lpBuf, usLength, recvList, m_dwTimeOut);
    delete[] lpBuf;
    if (SP_OK != res)
    {
        return res;
    }

    uint32 recvLen = 0;
    //1st packet
    if (!UnpackPRT(recvList.GetPackage(0), NULL, (void*)&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: 1st packet UnpackPRT return False", __FUNCTION__);
        return SP_E_PHONE_INVALID_DATA;
    }

    //2nd packet
    if (!UnpackPRT(recvList.GetPackage(1), NULL, (void*)&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: 2nd packet UnpackPRT return False", __FUNCTION__);
        return SP_E_PHONE_INVALID_DATA;
    }

    return SP_OK;
}

SPRESULT CCaliCmd::WifiNstRxlevelRlt_UIS8910(PC_WIFI_RSSIRLT_REQ_UIS8910_T* RSSI)
{
    CheckValidPointer(RSSI);

    LogRawStrA(SPLOGLV_INFO, "Wifi Get NST Rxlevel:");

    L1_LTE_EMPTY_REQ_T L1;
    ZeroMemory((void*)&L1, sizeof(L1));
    L1.SignalCode = Convert16(TOOL_MPH_WIFI_NST_RSSI_RLT_REQ);
    L1.SignalSize = Convert16((uint16)sizeof(L1));

    unsigned short usRltLength = (unsigned short)(sizeof(L1))
        + sizeof(PC_WIFI_RSSIRLT_REQ_UIS8910_T);

    DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE);

    CRecvPkgsList recvList;
    recvList.AddCondition(hd);
    recvList.AddCondition(hd);
    SPRESULT res = SendAndRecv(hd, (const void*)&L1, sizeof(L1), recvList, m_dwTimeOut);
    if (SP_OK != res)
    {
        return res;
    }

    for (int nPktIdx = 0; nPktIdx < recvList.GetPkgsCount(); nPktIdx++)
    {
        uint32 recvLen = 0;
        if (!UnpackPRT(recvList.GetPackage(nPktIdx), NULL, (void*)&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
        {
            assert(0);
            continue;
        }

        if (recvLen >= usRltLength)
        {
            L1_LTE_EMPTY_REQ_T* pHead = (L1_LTE_EMPTY_REQ_T*)m_diagBuff;
            uint16 uType = Convert16(pHead->SignalCode);
            if (TOOL_MPH_WIFI_NST_RSSI_RLT_REQ != uType)
            {
                LogFmtStrA(SPLOGLV_ERROR, "Invalid response command ID: 0x%X <> 0x%X", uType, TOOL_MPH_WIFI_NST_RSSI_RLT_REQ);
                return SP_E_PHONE_INVALID_DATA;
            }

            PC_WIFI_RSSIRLT_REQ_UIS8910_T* pRlt = (PC_WIFI_RSSIRLT_REQ_UIS8910_T*)(m_diagBuff + sizeof(L1));
            memcpy(RSSI, pRlt, sizeof(PC_WIFI_RSSIRLT_REQ_UIS8910_T));
            return SP_OK;
        }
    }

    LogRawStrA(SPLOGLV_ERROR, "Invalid response received!");
    return SP_E_PHONE_INVALID_DATA;
}