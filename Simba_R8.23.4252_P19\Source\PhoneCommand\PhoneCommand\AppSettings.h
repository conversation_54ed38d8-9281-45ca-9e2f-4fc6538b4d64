#pragma once

#include "global_def.h"
#include "CLocks.h"
#include "CSimpleLock.h"

//////////////////////////////////////////////////////////////////////////

class CAppSettings sealed
{
public:
    static CAppSettings& GetInstance( void )
    {
        if ( NULL == s_instance )
        {
			CLocks lock(s_lock);
            if ( NULL == s_instance )
            {
                s_instance = new CAppSettings();
            }
        }
        return *s_instance;
    }
    
	static void DeleteInstance(void)
	{
		if (NULL != s_instance)
		{
			delete s_instance;
			s_instance = NULL;
		}
	}

    enum SettingType
    {
        LogLevel        = 0,
        TimeOut         = 1,
        //  MagicNumber     = 2,  @JXP 20181129:  MagicNumber replaced by PhaseCheck.ini
        Endian          = 3,
        ComparePort     = 4,
        Socket          = 5,
        DevHound        = 6,
        IgnoreSn        = 7,
        //  ForceIgnoreSn   = 8,
        ModemToPC       = 9,
        CheckVComDrvVersion     = 10,
        SaveLogelData   = 11,
		MaxTimeout = 12,
		MinTimeout = 13,
		AuthMode = 14,
		WaitTimeLogPort
    };
    
    void GetValue( SettingType eType, LPVOID lpValue );
    
    BOOL IsValidDriver( LPCWSTR lpszDrivName );
	BOOL IsValidLogDriver( LPCWSTR lpszDrivName );
    
private:
    void Load( void );
    
    CAppSettings( void );
    CAppSettings( const CAppSettings& ) {};
    CAppSettings& operator=( const CAppSettings& ) {};
    
private:
    UINT    m_nLogLevel;
    UINT    m_nDevHoundLevel;
    DWORD   m_dwTimeOut;
	DWORD   m_MaxTimeout;
	DWORD   m_MinTimeout;
    UINT    m_nEndian;
    BOOL    m_bComparePort;
    std::wstring m_sDrivName;
	std::wstring m_sDrivLogName;
    std::wstring m_strIgnoreSn;
    SOCKET_SETTING m_Socket;
    BOOL    m_bModemToPC;
    BOOL    m_bCheckVComDrvVersion;
    BOOL    m_bSaveLogelData;
	BOOL    m_bAuthMode;
	UINT	m_nWaitTimeLogPort = 200; //ms
    
	static CAppSettings* s_instance;
	static CSimpleLock s_lock;
};
