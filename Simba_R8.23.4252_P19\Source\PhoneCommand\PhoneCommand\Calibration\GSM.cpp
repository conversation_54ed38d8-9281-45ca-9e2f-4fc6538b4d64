#include "StdAfx.h"
#include "CaliCmd.h"
#include <assert.h>
#include <vector>


LPCSTR CCaliCmd::SCZ_GSM_BAND[BI_GSM_MAX_BAND] = {"GSM850M", "EGSM900", "DCS1800", "PCS1900"};

//////////////////////////////////////////////////////////////////////////
uint16 CCaliCmd::gsmIoBand(SP_BAND_INFO eBand)
{
    uint16 IoBand = 0; 
    switch(eBand)
    {
    case BI_GSM_850:
        IoBand = 4;
        break;
    case BI_EGSM:
        IoBand = 0;
        break;
    case BI_DCS:
        IoBand = 1;
        break;
    case BI_PCS:
        IoBand = 3;
        break;
    default:
        LogFmtStrA(SPLOGLV_WARN, "Invalid IO band = %d", eBand);
        assert(0);
        break;
    }

    return IoBand;
}

BOOL CCaliCmd::gsmIsValidPCL(SP_BAND_INFO eBand, uint16 pcl)
{
    switch(eBand)
    {
    case BI_EGSM:
    case BI_GSM_850:
        return (pcl >= 5 && pcl <= 19);
    case BI_DCS:
    case BI_PCS:
        return (pcl >= 0 && pcl <= 15);
    default:
        return FALSE;
    }
}

BOOL CCaliCmd::gsmIsValidArfcn(SP_BAND_INFO eBand, uint16 nArfcn)
{
    switch(eBand)
    {
    case BI_EGSM:
        return (nArfcn <= 124 || (nArfcn >= 940 && nArfcn <= 1023));

    case BI_GSM_850:
        return (nArfcn >= 128 && nArfcn <= 251);

    case BI_DCS:
        return (nArfcn >= 512 && nArfcn <= 885);
        
    case BI_PCS:
        return (nArfcn >= 512 && nArfcn <= 810);

    default:
        break;
    }

    return FALSE;
}

FREQ_LEVEL_E CCaliCmd::gsmArfcnFreqLv(SP_BAND_INFO eBand, uint16 nArfcn)
{
    uint16 arrArfcnBoundIndex[4][4] = 
    {
        {0x23D, 0x256, 0x26F, 0x288},   // GB_GSM850
        {0x1F , 0x3E , 0x5D , 0x1F2},   // GB_GSM900
        {0xC8 , 0x113, 0x15E, 0x1A9},   // GB_DCS
        {0xB9 , 0xFF , 0x13B, 0x177},   // GB_PCS
    };

    int iBandIndex  = 1;
    int iArfcnIndex = nArfcn;
    switch(eBand)
    {
    case BI_EGSM:
        iBandIndex  = 1;
        iArfcnIndex = nArfcn;
        break;

    case BI_DCS:
        iBandIndex  = 2;
        iArfcnIndex = nArfcn-512+125;
        break;

    case BI_PCS:
        iBandIndex  = 3;
        iArfcnIndex = nArfcn-512+125;
        break;

    case BI_GSM_850:
        iBandIndex  = 0;
        iArfcnIndex = nArfcn-128+548;
        break;
    default:
        break;
    }

    for (int Lv=FREQ_LEVEL_LOWER; Lv<FREQ_LEVEL_HIGHER; Lv++)
    {
        if (iArfcnIndex <= arrArfcnBoundIndex[iBandIndex][Lv]) 
        {
            //
            return (FREQ_LEVEL_E)Lv;
        }
    }

    return FREQ_LEVEL_HIGHER;
}

SPRESULT CCaliCmd::gsmSaveParam(
    uint16 is_nv, 
    L1_CALIBRATION_PARAM_E eType, 
    SP_BAND_INFO eBand, 
    uint16 nIndex, 
    uint16 nLength, 
    LPCVOID lpData, 
    uint32 u32TimeOut
    )
{
    TOOL_L1_DIAG_CALI_PARAM_T L1;
    L1.is_nv    = Convert16((uint16)is_nv);
    L1.band     = Convert16((uint16)gsmIoBand(eBand));
    L1.type     = Convert16((uint16)eType);
    L1.index    = Convert16((uint16)nIndex);
    L1.length   = Convert16((uint16)nLength);

    DeclareDiagHeader(hd, DIAG_CALIBRATION, L1_WRITE);

    /// -->: TOOL_L1_DIAG_CALI_PARAM_T + data
    CopyMemory((void* )&m_diagBuff[0], (const void* )&L1, sizeof(L1));
    uint32 u32SendSize = sizeof(L1);
    if (NULL != lpData && nLength > 0)
    {
        CopyMemory((void* )&m_diagBuff[sizeof(L1)], lpData, nLength);
        u32SendSize += nLength;
    }

    uint8  recvBuf[256] = {0};
    uint32 u32revSize = 0;
    SPRESULT  res = SendAndRecv(hd, (const void* )m_diagBuff, u32SendSize, hd, (void* )recvBuf, sizeof(recvBuf), &u32revSize, u32TimeOut);
    if (SP_OK == res)
    {
        uint32 u32ExpSize = sizeof(DIAG_TOOL_CNF_T);
        if (u32revSize < u32ExpSize)
        {
            LogFmtStrA(SPLOGLV_ERROR, "Invalid response length! %d < %d", u32revSize, u32ExpSize);
            return SP_E_PHONE_INVALID_LENGTH;
        }

        if (L1_FLASH == is_nv)
        {
            DIAG_TOOL_CNF_T* pCNF = reinterpret_cast<DIAG_TOOL_CNF_T* >(recvBuf);
            uint16 nState = Convert16(pCNF->operation_status);
            if (1 == nState)
            {
                // pass 
                if (m_pContainer != NULL && lpData != NULL)
                {
                    std::wstring strKey= CgsmUtility::GetShareKey(eType, eBand, nIndex);
                    m_pContainer->SetValue(strKey.c_str(), lpData, nLength);
                }
                return res;
            }
            else
            {
                // Flash operate failed
                LogFmtStrA(SPLOGLV_ERROR, "gLayer1 save failed, state = %d", nState);
                return SP_E_PHONE_INVALID_STATE;
            }
        }
    }

    return res;
}

SPRESULT CCaliCmd::gsmLoadParam( 
    uint16 is_nv, 
    L1_CALIBRATION_PARAM_E eType, 
    SP_BAND_INFO eBand, 
    uint16 nIndex, 
    uint16 nLength, 
    LPVOID lpData, 
    uint32 u32TimeOut
    )
{
    TOOL_L1_DIAG_CALI_PARAM_T L1;
    L1.is_nv    = Convert16((uint16)is_nv);
    L1.band     = Convert16((uint16)gsmIoBand(eBand));
    L1.type     = Convert16((uint16)eType);
    L1.index    = Convert16((uint16)nIndex);
    L1.length   = Convert16((uint16)nLength);

    DeclareDiagHeader(hd, DIAG_CALIBRATION, L1_READ);
    uint32 u32revSize = 0;
    SPRESULT res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &u32revSize, u32TimeOut);
    if (SP_OK == res)
    {
        /// <-- : DIAG_TOOL_CNF_T + data
        uint32 u32ExpSize = sizeof(DIAG_TOOL_CNF_T);
        if (u32revSize < u32ExpSize)
        {
            LogFmtStrA(SPLOGLV_ERROR, "Invalid response length! %d < %d", u32revSize, u32ExpSize);
            return SP_E_PHONE_INVALID_LENGTH;
        }
		DIAG_TOOL_CNF_T* pCNF = reinterpret_cast<DIAG_TOOL_CNF_T* >(m_diagBuff);
		uint16 nState = Convert16(pCNF->operation_status);
		if (1 != nState)
		{
			LogFmtStrA(SPLOGLV_ERROR, "gLayer1 load failed, state = %d", nState);
			return SP_E_PHONE_INVALID_STATE;
		}
        uint32 u32DataSize = u32revSize - u32ExpSize;
        if (NULL != lpData && u32DataSize > 0)
        {
            CopyMemory(lpData, (const void* )&m_diagBuff[u32ExpSize], u32DataSize);
        }
    }

    return res;
}

void CCaliCmd::gsmSetTxParam(const PC_TX_PARAM_T& tx)
{
    LogFmtStrA(SPLOGLV_INFO, "Set TX parameters, DSP TX = %d", tx.data_type);
    m_gsmTxParam = tx;
}

SPRESULT CCaliCmd::gsmActive(BOOL bActive)
{
    LogFmtStrA(SPLOGLV_INFO, "%s", bActive ? "GSM Active" : "GSM DeActive");
    
    uint16 u16Value = Convert16(bActive ? 1 : 0);

    SPRESULT res = gsmSaveParam(L1_RAM, CALI_ACTIVE_DEACTIVE, BI_EGSM, 0, 2, (const void*)& u16Value, m_dwTimeOut);
    
    if (SP_OK != res)
    {
        LogFmtStrA(SPLOGLV_ERROR, "gsmActive Fail!");
    }

    return res;
}

SPRESULT CCaliCmd::gsmSetArfcn(SP_BAND_INFO eBand, uint16 u16Arfcn)
{
    if (!gsmIsValidArfcn(eBand, u16Arfcn))
    {
        LogFmtStrA(SPLOGLV_ERROR, "Invalid Channel! Band = %d, CH = %d", eBand, u16Arfcn);
        return SP_E_PHONE_INVALID_PARAMETER;
    }
    else
    {
        LogFmtStrA(SPLOGLV_INFO, "Set %s channel %d:", SCZ_GSM_BAND[eBand], u16Arfcn);

        uint16 u16Value = Convert16(u16Arfcn);
        return gsmSaveParam(L1_RAM, CALI_TX_ARFCN, eBand, 0, 2, (const void* )&u16Value, m_dwTimeOut);
    }
}

SPRESULT CCaliCmd::gsmSetPCL(SP_BAND_INFO eBand, uint16 u16Pcl)
{
    if (!gsmIsValidPCL(eBand, u16Pcl))
    {
        LogFmtStrA(SPLOGLV_ERROR, "Invalid PCL! Band = %d, PCL = %d", eBand, u16Pcl);
        return SP_E_PHONE_INVALID_PARAMETER;
    }
    else
    {
        LogFmtStrA(SPLOGLV_INFO, "Set %s PCL %d:", SCZ_GSM_BAND[eBand], u16Pcl);

        uint16 u16Value = Convert16(u16Pcl);
        return gsmSaveParam(L1_RAM, CALI_TX_PCL, eBand, 0, 2, (const void* )&u16Value, m_dwTimeOut);
    }
}

SPRESULT CCaliCmd::gsmSetPwrFactor(const PC_PWR_FACTOR_PARAM_T* req)
{
    if (   (NULL == req) 
        || !gsmIsValidArfcn(req->eBand, req->uArfcn)
        || !gsmIsValidPCL  (req->eBand, req->uPCL)
        || !IN_RANGE(MIN_GSM_TX_FACTOR, req->uFactor, MAX_GSM_TX_FACTOR)
        )
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid parameters!", __FUNCTION__);
        return SP_E_PHONE_INVALID_PARAMETER;
    }
    else
    {
        LogFmtStrA(SPLOGLV_INFO, "Set %s factor, CH = %d, PCL = %d, Factor = %d", \
            SCZ_GSM_BAND[req->eBand], req->uArfcn, req->uPCL, req->uFactor);

        FREQ_LEVEL_E eLv = gsmArfcnFreqLv(req->eBand, req->uArfcn);
        int          pcl = getIndexPCL(req->eBand, req->uPCL); 
        uint16    nIndex = (uint16)(eLv*MAX_GSM_PCL + pcl);  
        uint16    nValue = (uint16)Convert16((uint16)req->uFactor);

        return gsmSaveParam(L1_RAM, CALI_TX_PWR_STEP_FACTOR, req->eBand, nIndex, 2, (const void* )&nValue, m_dwTimeOut);   
    }
}

SPRESULT CCaliCmd::gsmTxOn(SP_BAND_INFO eBand, BOOL bOn)
{
    if (!gsmIsValidBand(eBand))
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Unknown GSM band %d!", __FUNCTION__, eBand);
        return SP_E_PHONE_INVALID_PARAMETER;
    }
    else
    {
        LogFmtStrA(SPLOGLV_INFO, "%s TX %s", SCZ_GSM_BAND[eBand], bOn ? "On" : "Off");

        L1_TX_RX_REQ_T      L1;
        ZeroMemory((void* )&L1, sizeof(L1));
        L1.coding_scheme       = Convert16((uint16)m_gsmTxParam.coding_scheme);
        L1.data_type           = Convert32((uint32)m_gsmTxParam.data_type);
        L1.puncture_type       = Convert16((uint16)m_gsmTxParam.puncture_type);
        L1.training_sequence   = Convert16((uint16)m_gsmTxParam.training_sequence);
        L1.on_off              = (uint8)(bOn ? 1 : 0);

        return gsmSaveParam(L1_RAM, CALI_TX_ON_OFF, eBand, 0, sizeof(L1), (const void* )&L1, m_dwTimeOut);
    }
}

SPRESULT CCaliCmd::gsmSetCdac(int16 cdac)
{
    LogFmtStrA(SPLOGLV_INFO, "Set DCXO CDAC %d:", cdac);

    int16  nValue = (int16)Convert16(cdac);
    return gsmSaveParam(L1_RAM, CALI_AFC_CDAC, BI_EGSM, 0, 2, (const void* )&nValue, m_dwTimeOut);
}

SPRESULT CCaliCmd::gsmSetCafc(int16 cafc)
{
    LogFmtStrA(SPLOGLV_INFO, "Set DCXO CAFC %d:", cafc);

    int16  nValue = (int16)Convert16(cafc);
    return gsmSaveParam(L1_RAM, CALI_AFC_CAFC, BI_EGSM, 0, 2, (const void* )&nValue, m_dwTimeOut);
}

SPRESULT CCaliCmd::gsmSetAfcCenter(int16 afc_center)
{
    LogFmtStrA(SPLOGLV_INFO, "Set TCXO CAFC %d:", afc_center);

    int16  nValue = (int16)Convert16(afc_center);
    return gsmSaveParam(L1_RAM, CALI_AFC_CENTER, BI_EGSM, 0, 2, (const void* )&nValue, m_dwTimeOut);
}

SPRESULT CCaliCmd::gsmAFC(BOOL bAfcStart, const PC_AFC_REQ_T* req)
{
    CheckValidPointer(req);

    SPRESULT res = SP_OK;
    if (bAfcStart)
    {
        res = gsmSetArfcn(req->eBand, req->uArfcn);
        if (SP_OK != res)
        {
            return res;
        }

        res = gsmSetPCL(req->eBand, req->uPCL);
        if (SP_OK != res)
        {
            return res;
        }

        if (req->bVCTCXO)
        {
            res = gsmSetAfcCenter(req->uAfcVal);
            if (SP_OK != res)
            {
                return res;
            }
        }
        else
        {
            res = gsmSetCdac(req->uDacVal);
            if (SP_OK != res)
            {
                return res;
            }

            res = gsmSetCafc(req->uAfcVal);
            if (SP_OK != res)
            {
                return res;
            }
        }
    }

    return gsmTxOn(req->eBand, bAfcStart);
}

SPRESULT CCaliCmd::gsmAFCStart( BOOL bAfcStart )
{
	LogFmtStrA(SPLOGLV_INFO, "%s", bAfcStart ? "GSM AFC Start" : "GSM AFC End");

	uint16 u16Value = Convert16(bAfcStart ? 1 : 0);
	return gsmSaveParam(L1_RAM, CALI_AFC_START, BI_EGSM, 0, 2, (const void* )&u16Value, m_dwTimeOut);
}

SPRESULT CCaliCmd::gsmSetCafc_32Bit(uint32 cafc)
{
	LogFmtStrA(SPLOGLV_INFO, "Set CAFC %d:", cafc);

	uint32  nValue = (uint32)Convert32(cafc);
	return gsmSaveParam(L1_RAM, CALI_AFC_CAFC, BI_EGSM, 0, sizeof(nValue), (const void* )&nValue, m_dwTimeOut);
}

SPRESULT CCaliCmd::gsmSetCdac_32Bit(uint32 cdac)
{
	LogFmtStrA(SPLOGLV_INFO, "Set CDAC %d:", cdac);

	uint32  nValue = (uint32)Convert32(cdac);
	return gsmSaveParam(L1_RAM, CALI_AFC_CDAC, BI_EGSM, 0, sizeof(nValue), (const void* )&nValue, m_dwTimeOut);
}

SPRESULT CCaliCmd::gsmSetAfcCenter_32Bit(uint32 afc_center)
{
	LogFmtStrA(SPLOGLV_INFO, "Set TCXO CAFC %d:", afc_center);

	uint32  nValue = (int16)Convert32(afc_center);
	return gsmSaveParam(L1_RAM, CALI_AFC_CENTER, BI_EGSM, 0, sizeof(nValue), (const void* )&nValue, m_dwTimeOut);
}


SPRESULT CCaliCmd::gsmPmicAFC( BOOL bAfcStart, const PC_AFC_REQ_T* req )
{
	CheckValidPointer(req);

	SPRESULT res = SP_OK;
	if (bAfcStart)
	{
		res = gsmSetArfcn(req->eBand, req->uArfcn);
		if (SP_OK != res)
		{
			return res;
		}

		res = gsmSetPCL(req->eBand, req->uPCL);
		if (SP_OK != res)
		{
			return res;
		}

		if (req->bVCTCXO)
		{
			res = gsmSetAfcCenter(req->uAfcVal);
			if (SP_OK != res)
			{
				return res;
			}
		}
		else
		{
			res = apSetCDAC(req->uDacVal);
			if (SP_OK != res)
			{
				return res;
			}

			res = gsmSetCafc(req->uAfcVal);
			if (SP_OK != res)
			{
				return res;
			}
		}
	}

	return gsmTxOn(req->eBand, bAfcStart);
}

SPRESULT CCaliCmd::gsmPmicAFC_32Bit( BOOL bAfcStart, const PC_PMIC_AFC_32Bit_REQ_T* req )
{
	CheckValidPointer(req);

	SPRESULT res = SP_OK;

	res = gsmAFCStart(bAfcStart);
	if (SP_OK != res)
	{
		return res;
	}

	if (bAfcStart)
	{
		res = gsmSetArfcn(req->eBand, req->uArfcn);
		if (SP_OK != res)
		{
			return res;
		}

		res = gsmSetPCL(req->eBand, req->uPCL);
		if (SP_OK != res)
		{
			return res;
		}

		if (req->bVCTCXO)
		{
			res = gsmSetAfcCenter_32Bit(req->uAfcVal);
			if (SP_OK != res)
			{
				return res;
			}
		}
		else
		{
			res = apSetCDAC(req->uDacVal);
			if (SP_OK != res)
			{
				return res;
			}

			res = gsmSetCafc_32Bit(req->uAfcVal);
			if (SP_OK != res)
			{
				return res;
			}
		}
	}

	return gsmTxOn(req->eBand, bAfcStart);
}
SPRESULT CCaliCmd::gsmAPC(BOOL bApcStart, const PC_APC_REQ_T* req)
{
    CheckValidPointer(req);

    SPRESULT res = SP_OK;
    if (bApcStart)
    {
        res = gsmSetArfcn(req->pwrFactor.eBand, req->pwrFactor.uArfcn);
        if (SP_OK != res)
        {
            return res;
        }

        res = gsmSetPCL(req->pwrFactor.eBand, req->pwrFactor.uPCL);
        if (SP_OK != res)
        {
            return res;
        }

        res = gsmSetPwrFactor(&req->pwrFactor);
        if (SP_OK != res)
        {
            return res;
        }
        else
        {
            /// Wait parameters to setup 
            Sleep(100);
        }
    }

    return gsmTxOn(req->pwrFactor.eBand, bApcStart);
}

SPRESULT CCaliCmd::gsmRxOn(SP_BAND_INFO eBand, BOOL bOn, uint16 u16GainIndex, uint16 u16GainValue, uint16 u16SampleCount)
{
    if (!gsmIsValidBand(eBand))
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Unknown GSM band %d!", __FUNCTION__, eBand);
        return SP_E_PHONE_INVALID_PARAMETER;
    }
    else
    {
        LogFmtStrA(SPLOGLV_INFO, "%s RX %s, gain_index = %d, gain_value = 0x%X, sample = %d", SCZ_GSM_BAND[eBand], \
                                                bOn ? "On" : "Off",  u16GainIndex, u16GainValue, u16SampleCount);

        L1_TX_RX_REQ_T L1;
        ZeroMemory((void* )&L1, sizeof(L1));
        L1.data_type           = Convert32((uint32)DSP_TX_TYPE_RANDOM);
        L1.gain_ind            = Convert16((uint16)u16GainIndex);
        L1.gain_val            = Convert16((uint16)u16GainValue);
        L1.sample_couter       = Convert16((uint16)u16SampleCount);
        L1.on_off              = (uint8)(bOn ? 1 : 0);
        L1.is_dcvoltage_meas   = (uint8)(0);
        
        return gsmSaveParam(L1_RAM, CALI_RX_ON_OFF, eBand, 0, sizeof(L1), (const void* )&L1, m_dwTimeOut);
    }
}

SPRESULT CCaliCmd::gsmGetRxRSSI(SP_BAND_INFO eBand, uint16* lpu16rssi)
{
    CheckValidPointer(lpu16rssi);

    if (!gsmIsValidBand(eBand))
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Unknown GSM band %d!", __FUNCTION__, eBand);
        return SP_E_PHONE_INVALID_PARAMETER;
    }

    LogFmtStrA(SPLOGLV_INFO, "Get %s RSSI: ", SCZ_GSM_BAND[eBand]);
    *lpu16rssi = 0;

    SPRESULT res = SP_OK;
    int   nCount = 0;
    do 
    {
        uint16 rssi  = 0;
        res = gsmLoadParam(L1_RAM, CALI_RSSI, eBand, 0, 2, (void* )&rssi, m_dwTimeOut);
        if ((SP_OK == res) && (rssi > 0))
        {
            *lpu16rssi = Convert16((uint16)rssi);
            break;
        }
        else
        {
            Sleep(20);
        }

    } while (nCount++ < 5);

    if (SP_OK != res)
    {
        return res;
    }

    LogFmtStrA(SPLOGLV_INFO, "rssi = %d (0x%X)", *lpu16rssi, *lpu16rssi);
    return res;
}

SPRESULT CCaliCmd::gsmGetRxLevel(SP_BAND_INFO eBand, uint16* lpu16rxlv)
{
    CheckValidPointer(lpu16rxlv);

    if (!gsmIsValidBand(eBand))
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Unknown GSM band %d!", __FUNCTION__, eBand);
        return SP_E_PHONE_INVALID_PARAMETER;
    }

    LogFmtStrA(SPLOGLV_INFO, "Get %s RX Level:", SCZ_GSM_BAND[eBand]);

    uint16 rxlev = 0;
    SPRESULT res = gsmLoadParam(L1_RAM, CALI_RXLEV, eBand, 0, sizeof(uint16), (void *)&rxlev, m_dwTimeOut);
    if (SP_OK != res)
    {
        return res;
    }
    else
    {
        *lpu16rxlv = Convert16((uint16)rxlev);
        LogFmtStrA(SPLOGLV_INFO, "rxlev = %d (0x%X)", *lpu16rxlv, *lpu16rxlv);
        return SP_OK;
    }
}

SPRESULT CCaliCmd::gsmAGC(BOOL bAgcStart, const PC_AGC_REQ_T* req, PC_AGC_VALUE_CNF_T* cnf)
{
    if (NULL == req || NULL == cnf)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid parameters!", __FUNCTION__);
        return SP_E_PHONE_INVALID_PARAMETER;
    }

    SPRESULT res = SP_OK;
    if (bAgcStart)
    {
        res = gsmSetArfcn(req->eBand, req->uArfcn);
        if (SP_OK != res)
        {
            return res;
        }
    }

    res = gsmRxOn(req->eBand, bAgcStart, req->uGainIndex, req->uGainValue, DEFAULT_RX_SAMPLE_COUNT);
    if (SP_OK != res)
    {
        return res;
    }

    if (bAgcStart)
    {
        Sleep(WAIT_TIME_FOR_GET_RSSI);

        //
        uint16 rssi = 0;
        res = gsmGetRxRSSI(req->eBand, &rssi);
        if (SP_OK != res)
        {
            return res;
        }

        uint16 rxlv = 0;
        res = gsmGetRxLevel(req->eBand, &rxlv);
        if (SP_OK != res)
        {
            return res;
        }

        cnf->nRssi  = rssi;
        cnf->nRxlev = rxlv;
    }

    return res;
}

SPRESULT CCaliCmd::gsmSaveToFlash(uint32 u32TimeOut)
{
    LogFmtStrA(SPLOGLV_INFO, "gsmSaveToFlash: TimeOut = %d ms", u32TimeOut);

    return gsmSaveParam(L1_FLASH, CALI_SAVE_TO_NV, BI_EGSM, 0, 0, NULL, u32TimeOut);
}

SPRESULT CCaliCmd::gsmFDT(const GSM_FDT_REQ* req, GSM_FDT_RES* ack, uint32 u32TimeOut)
{
    if (    (NULL == req) 
        || ((GSM_FDT_RECV_RX == req->cmd) && (NULL == ack))
        )
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid parameters! (NULL)", __FUNCTION__);
        return SP_E_PHONE_INVALID_PARAMETER;
    }

    LPCSTR SCZ_CMD[MAX_GSM_FDT_CMD] = 
    {
        "SetTxFrames",              // 0
        "SetRxFrames",              // 1
        "Unknown",                  // 2
        "StartTX",                  // 3
        "StartRX",                  // 4
        "ReceiveRXValues",          // 5
        "Un-Supported",             // 6
        "Un-Supported",             // 7
        "ReceiveTxFramesConfirm"    // 8
    };

    LogFmtStrA(SPLOGLV_INFO, "GSM FDT: Cmd = %s, %d frames.", SCZ_CMD[req->cmd], req->frame_count);

    L1_FDT_REQ          L1FDT;
    ZeroMemory((void *)&L1FDT, sizeof(L1_FDT_REQ));

    L1FDT.nCmdType     = Convert16((uint16)req->cmd);
    if (   GSM_FDT_START_TX_8PSK == req->cmd 
        || GSM_FDT_SET_RX_PARAM  == req->cmd
        || GSM_FDT_START_RX      == req->cmd
        || GSM_FDT_RECV_RX       == req->cmd
        )
	{
        L1FDT.nReserv1 = Convert16((uint16)(req->nParam1));
	    L1FDT.nReserv2 = Convert16((uint16)(req->nParam2));
	    L1FDT.nReserv3 = Convert16((uint16)(req->nParam3));
	    L1FDT.nReserv4 = Convert16((uint16)(req->nParam4));
	}
	else
	{
		L1FDT.nReserv1 = Convert16((uint16)(0 == req->nParam1 ? 21 : req->nParam1));
		L1FDT.nReserv2 = Convert16((uint16)33);
		L1FDT.nReserv3 = Convert16((uint16)(0 == req->nParam1 ? 15 : req->nParam1));
       	L1FDT.nReserv4 = Convert16((uint16)req->nParam4);
	}

	L1FDT.nFmCount     = Convert16((uint16)req->frame_count);

    void*    pL1frameData = NULL;
    uint32   u32frameSize = 0;
    
    L1_FDT_TX_FRAME* pL1TxFrames = NULL;
    L1_FDT_RX_FRAME* pL1RxFrames = NULL;
    switch (req->cmd)
    {
    case GSM_FDT_SET_TX_PARAM:
        {
            try
            {
                pL1TxFrames = new L1_FDT_TX_FRAME[req->frame_count];
            }
            catch (const std::bad_alloc& /*e*/)
            {
                pL1TxFrames = NULL;
            }
            
            if (NULL == pL1TxFrames)
            {
                LogRawStrA(SPLOGLV_ERROR, "Memory allocation failed.");
                return SP_E_PHONE_ALLOC_MEMORY;
            }

            u32frameSize = req->frame_count * sizeof(L1_FDT_TX_FRAME);
            pL1frameData = pL1TxFrames;
            ZeroMemory((void* )pL1TxFrames, u32frameSize);
            
            GSM_FDT_TX_FRAME* pTxFrames = (GSM_FDT_TX_FRAME* )req->pFrames;
            for (unsigned int i=0; i<req->frame_count; i++)
            {
                pL1TxFrames[i].band   = Convert32((uint32)(gsmIoBand(pTxFrames[i].band)));
                pL1TxFrames[i].arfcn  = Convert16((uint16)(pTxFrames[i].arfcn));
                
                // slot mask & number
				uint8 slotmask = pTxFrames[i].slot_mask;
				uint8 slotnum  = 0;
                int   slot     = 0;
                for (slot=0; slot<MAX_GSM_TDMA_SLOT; slot++)
                {
                    if ((slotmask>>slot) & 0x01)
                    {
                        slotnum++;
                    }
                }

                if (slotnum > GSM_MAX_FDT_SLOT)
                {
                    delete []pL1TxFrames;
                    pL1TxFrames = NULL;

                    LogFmtStrA(SPLOGLV_ERROR, "Too many slots %d, exceed the max count %d!", slotnum, GSM_MAX_FDT_SLOT);
                    return SP_E_PHONE_INVALID_PARAMETER;
                }
                
                pL1TxFrames[i].slot_mask = pTxFrames[i].slot_mask;
                pL1TxFrames[i].slot_num  = slotnum;
                for (slot=0; slot<pL1TxFrames[i].slot_num; slot++)
                {
                    pL1TxFrames[i].factor[slot]       = Convert16((uint16)(pTxFrames[i].factor[slot]));
                    pL1TxFrames[i].factor_index[slot] = Convert16((uint16)(pTxFrames[i].factor_index[slot]));
                    pL1TxFrames[i].pcl[slot]          = Convert16((uint16)10/* Default PCL */);
                }
            }
        }
        break;

    case GSM_FDT_SET_RX_PARAM:
        {
            try
            {
                pL1RxFrames = new L1_FDT_RX_FRAME[req->frame_count];
            }
            catch (const std::bad_alloc& /*e*/)
            {
                pL1RxFrames = NULL;
            }

            if (NULL == pL1RxFrames)
            {
                LogFmtStrA(SPLOGLV_ERROR, "Memory allocation failed.");
                return SP_E_PHONE_ALLOC_MEMORY;
            }

            u32frameSize = req->frame_count * sizeof(L1_FDT_RX_FRAME);
            pL1frameData = pL1RxFrames;
            ZeroMemory((void* )pL1RxFrames, u32frameSize);
            
            GSM_FDT_RX_FRAME* pRxFrames = (GSM_FDT_RX_FRAME* )req->pFrames;
            for (unsigned int i=0; i<req->frame_count; i++)
            {
                pL1RxFrames[i].band     = Convert32((uint32)(gsmIoBand(pRxFrames[i].band)));
                pL1RxFrames[i].arfcn    = Convert16((uint16)(pRxFrames[i].arfcn));
                
                // slot mask & number
                uint8 slotmask = pRxFrames[i].slot_mask;
                uint8 slotnum  = 0;
                int   slot     = 0;
                for (slot=0; slot<MAX_GSM_TDMA_SLOT; slot++)
                {
                    if ((slotmask>>slot) & 0x1)
                    {
                        slotnum++;
                    }
                }

                if (slotnum > GSM_MAX_FDT_SLOT)
                {
                    delete []pL1RxFrames;
                    pL1RxFrames = NULL;

                    LogFmtStrA(SPLOGLV_ERROR, "Too many slots %d, exceed the max count %d!", slotnum, GSM_MAX_FDT_SLOT);
                    return SP_E_PHONE_INVALID_PARAMETER;
                }
                else
                {
                    pL1RxFrames[i].slot_mask  = pRxFrames[i].slot_mask;
                    pL1RxFrames[i].slot_num   = slotnum;
                }
                
                // slot information
                for (slot=0; slot<GSM_MAX_FDT_SLOT; slot++)
                {
                    pL1RxFrames[i].gain_value[slot] = Convert16((uint16)(pRxFrames[i].gain_value[slot]));
                    pL1RxFrames[i].gain_index[slot] = Convert16((uint16)(pRxFrames[i].gain_index[slot]));
                    pL1RxFrames[i].slot_type [slot] = Convert32((uint32)(pRxFrames[i].slot_type [slot]));
                }
            }
        }
        break;
    default:
        break;
    }

    TOOL_L1_DIAG_CALI_PARAM_T L1;
    L1.is_nv    = Convert16((uint16)L1_RAM);
    L1.band     = Convert16((uint16)0);
    L1.type     = Convert16((uint16)CALI_FDT);
    L1.index    = Convert16((uint16)0);
    L1.length   = Convert16((uint16)(sizeof(L1_FDT_REQ) + u32frameSize));

    uint32 u32SendSize = sizeof(L1) + sizeof(L1FDT) + u32frameSize;
    std::vector<uint8> vec(u32SendSize, 0x00);

    //-->: TOOL_L1_DIAG_CALI_PARAM_T + L1_FDT_REQ + L1_FDT_TX_FRAME/L1_FDT_RX_FRAME
    CopyMemory((void* ) &vec[0], (const void* )&L1, sizeof(L1));
    void*  p = (void* )(&vec[0] + sizeof(L1));
    CopyMemory((void* ) p, (const void* )&L1FDT, sizeof(L1FDT));
    if (NULL != pL1frameData && u32frameSize > 0)
    {
        p = (void* )(&vec[0] + sizeof(L1) + sizeof(L1FDT));
        CopyMemory((void* )p, (const void* )pL1frameData, u32frameSize);
    }

    if (NULL != pL1TxFrames)
    {
        delete []pL1TxFrames;
        pL1TxFrames = NULL;
    }

    if (NULL != pL1RxFrames)
    {
        delete []pL1RxFrames;
        pL1RxFrames = NULL;
    }
    
    DeclareDiagHeader(hd, DIAG_CALIBRATION, L1_WRITE);
    hd.sn = 0;

    SPRESULT res = SP_OK;
    uint32 u32recvSize = 0;

	if (GSM_FDT_START_RX == req->cmd || GSM_FDT_SET_TX_PARAM == req->cmd)
	{
        CRecvPkgsList recvCond;
        recvCond.AddCondition(hd);

	    res = SendCmd(hd, (const void* )&vec[0], u32SendSize, recvCond);
    }
	else if (GSM_FDT_RECV_RX == req->cmd || GSM_FDT_RECV_TX_PARAM_CNF == req->cmd)
	{
        res = RecvCmd((void* )m_diagBuff, sizeof(m_diagBuff), &u32recvSize, u32TimeOut);
	}
	else
	{
		res = SendAndRecv(hd, (const void* )&vec[0], u32SendSize, hd, (void* )m_diagBuff, sizeof(m_diagBuff), &u32recvSize, u32TimeOut);
	}

    if (SP_OK == res)
    {
        //<--: L1_FDT_ACK + data(int [])
		if (GSM_FDT_START_RX == req->cmd || GSM_FDT_SET_TX_PARAM == req->cmd)
		{
			// Dummy, get the response in FDT_RECV_RX function
			return res;
		}
		else
		{
			L1_FDT_ACK L1ACK;
			if (u32recvSize < sizeof(L1ACK))
			{
                LogFmtStrA(SPLOGLV_ERROR, "Invalid response data length %d.", u32recvSize);
                return SP_E_PHONE_INVALID_LENGTH;
			}
			
            CopyMemory((void* )&L1ACK, (const void* )m_diagBuff, sizeof(L1ACK));

			// state
			uint16 u16State = Convert16(L1ACK.nState);
			L1ACK.nState = u16State;
            if (GSM_FDT_SUCC != L1ACK.nState)
            {
                LogFmtStrA(SPLOGLV_ERROR, "GSM request operation failed, state = %d", L1ACK.nState);
                if (L1ACK.nState == GSM_FDT_RET_SCH_FAIL)
                {
                    res = SP_E_GSM_AGC_SYNC;
                }
                else
                {
                    return SP_E_PHONE_INVALID_STATE;
                }
            }

			// count
			uint16  u16Count = Convert16(L1ACK.nDataCount);
			L1ACK.nDataCount = u16Count;
			if (GSM_FDT_RECV_RX == req->cmd)
			{
				ack->state      = static_cast<GSM_FDT_STATE_E>(L1ACK.nState);
				ack->data_num   = static_cast<unsigned int>(L1ACK.nDataCount);
                if (L1ACK.nDataCount > MAX_GSM_FDT_RSSI_COUNT)
                {
                    LogFmtStrA(SPLOGLV_ERROR, "Too many RSSI values! %d > %d", L1ACK.nDataCount, MAX_GSM_FDT_RSSI_COUNT);
                    return SP_E_PHONE_INVALID_LENGTH;
                }
        
				void* lpValue = (void* )(m_diagBuff + sizeof(L1ACK));
				CopyMemory((void* )ack->data, lpValue, L1ACK.nDataCount*sizeof(unsigned int));
				Convert32((uint8* )ack->data, L1ACK.nDataCount*sizeof(unsigned int));
                for (uint16 i=0; i<ack->data_num; i++)
                {
                    LogFmtStrA(SPLOGLV_INFO, "RSSI[%3d] = %d, 0x%X", i, ack->data[i], ack->data[i]);
                }
			}
		}
    }
    
    return res;
}

SPRESULT CCaliCmd::gsmNST(const PC_NONSIGNAL_REQ_T* req, PC_NONSIGNAL_CNF_T* ack)
{
    if (NULL == req || NULL == ack)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid parameters!", __FUNCTION__);
        return SP_E_PHONE_INVALID_PARAMETER;
    }
    
    if (!gsmIsValidBand(req->eBand))
    {
        LogFmtStrA(SPLOGLV_ERROR, "Invalid GSM band %d!", req->eBand);
        return SP_E_PHONE_INVALID_PARAMETER;
    }

    LPCSTR SCZ_CMD[CMD_RXTXLOOP_MAX] = 
    {
        "Invalid", "InitiateLoop", "StartLoop", "StopLoop", "ChangePCL", "ChangeTCH", "GetRxLevel", "Sync.NV", "InitiateSEBER", "GetSEBER"
    };

    LogFmtStrA(SPLOGLV_INFO, "%s NonSignal %s, CH = %d, PCL = %d", SCZ_GSM_BAND[req->eBand], SCZ_CMD[req->eCmd], req->nArfcn, req->nPcl);

    CALI_L1_RXTXLOOP_PARAM_T nst;
    nst.arfcn      = Convert16((uint16)req->nArfcn);
    nst.traffic_tn = Convert16((uint16)req->nTrafficTn);
    nst.tsc        = Convert16((uint16)req->nTsc);
    nst.power_level= Convert16((uint16)req->nPcl);
    nst.mode       = Convert16((uint16)req->eMode);
    nst.command    = Convert16((uint16)req->eCmd);
    nst.band       = Convert16((uint16)gsmIoBand(req->eBand));
    nst.dummy0     = Convert16((uint16)req->nTotalBits);
    nst.dummy1     = Convert16((uint16)req->nBitsMode);

    TOOL_L1_DIAG_CALI_PARAM_T L1;
    L1.is_nv    = Convert16((uint16)L1_RAM);
    L1.band     = Convert16((uint16)gsmIoBand(req->eBand));
    L1.type     = Convert16((uint16)CALI_NOSINGAL_RXTXLOOP);
    L1.index    = Convert16((uint16)0);
    L1.length   = Convert16((uint16)sizeof(nst));

    DeclareDiagHeader(hd, DIAG_CALIBRATION, L1_WRITE);
    hd.sn = 0;    // SW bug. SN of response packet is always 0

    //-->: TOOL_L1_DIAG_CALI_PARAM_T + CALI_L1_RXTXLOOP_PARAM_T
    const uint32 u32SendSize = sizeof(L1) + sizeof(nst);
    uint8 u8SendBuff[u32SendSize] = {0};
    CopyMemory((void *)&u8SendBuff[0], (const void* )&L1, sizeof(L1));
    CopyMemory((void *)&u8SendBuff[sizeof(L1)], &nst, sizeof(nst));
    
    uint32 u32recvSize = 0;
    SPRESULT res = SendAndRecv(hd, (const void* )u8SendBuff, u32SendSize, hd, (void* )m_diagBuff, sizeof(m_diagBuff), &u32recvSize, m_dwTimeOut);
    if (SP_OK == res)
    {
        uint32 u32ExpSize = (CMD_RXTXLOOP_GET_SEBER == req->eCmd) ?  sizeof(PC_NONSIGNAL_CNF_T) : (sizeof(PC_NONSIGNAL_CNF_T) - 2/*nErrorBits*/ - 2/*nTotalBits*/);
        if (u32recvSize < u32ExpSize)
        {
            LogFmtStrA(SPLOGLV_ERROR, "Invalid response length: %d < %d!", u32recvSize, u32ExpSize);
            return SP_E_PHONE_INVALID_LENGTH;
        }

        PC_NONSIGNAL_CNF_T* pCnf = (PC_NONSIGNAL_CNF_T*)&m_diagBuff[0];
        ack->nResult   = Convert16((uint16)pCnf->nResult);
        ack->nRxLevel  = Convert16((uint16)pCnf->nRxLevel);
        ack->nRxQual   = Convert16((uint16)pCnf->nRxQual);
        ack->nDummy0   = Convert16((uint16)pCnf->nDummy0);
        L1_RXTXLOOP_RESULT_E eState = (L1_RXTXLOOP_RESULT_E)(pCnf->nResult);
        if (CMD_RXTXLOOP_GET_SEBER == req->eCmd)
        {
            ack->nErrorBits = Convert16((uint16)pCnf->nErrorBits);     
            ack->nTotalBits = Convert16((uint16)pCnf->nTotalBits); 
            if (RET_RXTXLOOP_SEBER_UNDER_TESTING == eState)
            {
                LogFmtStrA(SPLOGLV_WARN, "%s SE-BER is still under testing...", SCZ_GSM_BAND[req->eBand]);
                return SP_E_PHONE_SEBER_UNDER_TESTING;
            }
            else if (RET_RXTXLOOP_SEBER_READY == eState)
            {
                LogFmtStrA(SPLOGLV_INFO, "SE-BER = %d/%d (%.2f).", ack->nErrorBits, ack->nTotalBits, 1.0*ack->nErrorBits/ack->nTotalBits);
                return SP_OK;
            }
            else
            {
                LogFmtStrA(SPLOGLV_ERROR, "Get SE-BER failed! state = %d", eState);
                return SP_E_PHONE_INVALID_STATE;
            }
        }
        else
        {
            if (RET_RXTXLOOP_OK != eState)
            {
                LogFmtStrA(SPLOGLV_ERROR, "NonSignal command failed, state: %d", eState);
                return SP_E_PHONE_INVALID_STATE;
            }
        }        
    }

    return res;
}

SPRESULT CCaliCmd::gsmLoadCalFlag(uint32 *lpflag)
{
    CheckValidPointer(lpflag);

    LogRawStrA(SPLOGLV_INFO, "Load GSM/TD calibration flag:");

    uint32 u32flag = 0;
    SPRESULT res = gsmLoadParam(L1_RAM, CALI_ADC, BI_EGSM, 11, 4, (LPVOID)&u32flag, m_dwTimeOut);
    if (SP_OK == res)
    {
        *lpflag = Convert32(u32flag);
        LogFmtStrA(SPLOGLV_INFO, "flag = 0x%X", *lpflag);
    }
 
    return res;
}

SPRESULT CCaliCmd::gsmSaveCalFlag(uint32 flag)
{
    LogFmtStrA(SPLOGLV_INFO, "Save GSM/TD calibration flag: 0x%X", flag);

    uint32 u32flag = Convert32(flag);
	// GL1 bug NemoG
    SPRESULT res = gsmSaveParam(L1_RAM, CALI_ADC, BI_EGSM, 11, 4, (LPCVOID)&u32flag, m_dwTimeOut);
	
	if (SP_OK == res)
    {
        res = gsmSaveParam(L1_FLASH, CALI_ADC, BI_EGSM, 11, 4, (LPCVOID)&u32flag, m_dwTimeOut);
    }

    return res;
}

SPRESULT CCaliCmd::gsmCalSwitchToTDCal(void)
{
    LogFmtStrA(SPLOGLV_INFO, "Switch from GSM CAL mode to TD-SCDMA CAL mode");

    /// Change 
    TOOL_L1_DIAG_CALI_PARAM_T L1;
    L1.is_nv    = Convert16((uint16)0);
    L1.band     = Convert16((uint16)0);
    L1.type     = Convert16((uint16)CALI_MODE_T2G);
    L1.index    = Convert16((uint16)0);
    L1.length   = Convert16((uint16)0);

    DeclareDiagHeader(hd, DIAG_CALIBRATION, L1_WRITE);
    SPRESULT res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, NULL, 0, NULL, m_dwTimeOut);
    if (SP_OK != res)
    {
        return res;
    }

    Sleep(500);

    /// Init
    L1_TOOL_MPH_CALI_G2T_INIT_REQ req;
    ZeroMemory((void* )&req,  sizeof(req));
    req.SignalSize = Convert16((uint16)sizeof(req));
    req.SignalCode = Convert16((uint16)TOOL_MPH_CALI_G2T_INIT_REQ);

    DeclareDiagHeader(rd, DIAG_RF_F, ZERO_SUBTYPE); 

    CRecvPkgsList recvList;
    recvList.AddCondition(rd);
    recvList.AddCondition(rd);
    res = SendAndRecv(rd, (const void* )&req, sizeof(req), recvList, m_dwTimeOut); 
    if (SP_OK != res)
    {
        return res;
    }

    for (int i=0; i<recvList.GetPkgsCount(); i++)
    {
        uint32 recvLen = 0;
        if (!UnpackPRT(recvList.GetPackage(i), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
        {
            assert(0);
            continue;
        }

        if (recvLen >= sizeof(L1_TOOL_MPH_CALI_G2T_INIT_CNF))
        {
            L1_TOOL_MPH_CALI_G2T_INIT_CNF *pRLT = (L1_TOOL_MPH_CALI_G2T_INIT_CNF *)m_diagBuff;
            uint16 uType = Convert16(pRLT->SignalCode);
            if (TOOL_MPH_CALI_G2T_INIT_CNF != uType)
            {
                LogFmtStrA(SPLOGLV_ERROR, "Invalid response command ID: 0x%X <> 0x%X", uType, TOOL_MPH_CALI_G2T_INIT_CNF);
                return SP_E_PHONE_INVALID_DATA;       
            }

            uint32 uState = Convert32(pRLT->is_success);
            if (0 != uState)
            {
                LogFmtStrA(SPLOGLV_ERROR, "Change GSM CAL mode to TD-SCDMA CAL mode failed, state = %d.", uState);
                return SP_E_PHONE_INVALID_STATE;
            }
        }
    }

    return res;
}


SPRESULT CCaliCmd::gsmLmt_Init(const PC_NONSIGNAL_LMT_PARAM_T *pFDTReq)
{
	LogFmtStrA(SPLOGLV_INFO, "gsmLMT_Init,TxCfgNum : %d, RxCfgNum : %d",pFDTReq->nTxCfgNum,pFDTReq->nRxCfgNum);

	CALI_L1_LMT_PARAM_T param;
	ZeroMemory(&param,sizeof(param));
	param.nCmd		= Convert16((uint16)CMD_LMT_INITIATE);
	param.tsc		= Convert16((uint16)pFDTReq->tsc);
	param.LoopMode	= Convert16((uint16)pFDTReq->LoopMode);          
	param.nBerMode	= Convert16((uint16)pFDTReq->nBerMode);          
	param.nTxCfgNum	= Convert16((uint16)pFDTReq->nTxCfgNum);
	param.nRxCfgNum	= Convert16((uint16)pFDTReq->nRxCfgNum);

    PC_NONSIGNAL_LMT_TX_CFG_T* tTxCfg = NULL;
    try
    {
        tTxCfg = new PC_NONSIGNAL_LMT_TX_CFG_T[pFDTReq->nTxCfgNum];
    }
    catch (const std::bad_alloc& /*e*/)
    {
        tTxCfg = NULL;
    }
    if (NULL == tTxCfg)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Memory allocation failed!");
        assert(0);
        return SP_E_PHONE_ALLOC_MEMORY;
    }
    else
    {
	    memcpy(tTxCfg,pFDTReq->tTxCfg,sizeof(PC_NONSIGNAL_LMT_TX_CFG_T)*pFDTReq->nTxCfgNum);
	    for (int i=0; i<pFDTReq->nTxCfgNum; i++)
	    {
		    tTxCfg[i].nBand = gsmIoBand((SP_BAND_INFO)tTxCfg[i].nBand);
	    }
    }

	PC_NONSIGNAL_LMT_RX_CFG_T* tRxCfg = NULL;
    try
    {
        tRxCfg = new PC_NONSIGNAL_LMT_RX_CFG_T[pFDTReq->nRxCfgNum];
    }
    catch (const std::bad_alloc& /*e*/)
    {
        tRxCfg = NULL;
    }
    if (NULL == tRxCfg)
    {
        delete []tTxCfg;

        LogFmtStrA(SPLOGLV_ERROR, "%s: Memory allocation failed!");
        assert(0);
        return SP_E_PHONE_ALLOC_MEMORY;
    }
    else
    {
	    memcpy(tRxCfg,pFDTReq->tRxCfg, sizeof(PC_NONSIGNAL_LMT_RX_CFG_T)*pFDTReq->nRxCfgNum);
	    for (int i=0; i<pFDTReq->nRxCfgNum; i++)
	    {
		    tRxCfg[i].nBand = gsmIoBand((SP_BAND_INFO)tRxCfg[i].nBand);
	    }
    }

	TOOL_L1_DIAG_CALI_PARAM_T L1;
	L1.is_nv    = Convert16((uint16)0);
	L1.band     = Convert16((uint16)0);
	L1.type     = Convert16((uint16)CALI_NOSINGAL_LMT);
	L1.index    = Convert16((uint16)0);
	L1.length   = Convert16((uint16)(sizeof(param)+ 
		sizeof(PC_NONSIGNAL_LMT_TX_CFG_T) * pFDTReq->nTxCfgNum + 
		sizeof(PC_NONSIGNAL_LMT_RX_CFG_T) * pFDTReq->nRxCfgNum));
	DIAG_HEADER hd;            
	hd.type    = DIAG_CALIBRATION;        
	hd.subtype = L1_WRITE;         
	hd.sn      = 0;
	hd.len     = 0;
	unsigned long  uDataLen = sizeof(TOOL_L1_DIAG_CALI_PARAM_T) +
		sizeof(CALI_L1_LMT_PARAM_T)+ 
		sizeof(PC_NONSIGNAL_LMT_TX_CFG_T) * pFDTReq->nTxCfgNum + 
		sizeof(PC_NONSIGNAL_LMT_RX_CFG_T) * pFDTReq->nRxCfgNum;

	unsigned char *pData = NULL;
    try
    {
        pData  = new unsigned char[uDataLen];
        ZeroMemory((void *) pData, uDataLen);

        CopyMemory((void *) pData, (const void *)&L1, sizeof(L1));
        void *p =  (void *)(pData + sizeof(TOOL_L1_DIAG_CALI_PARAM_T));
        CopyMemory((void *) p, &param, sizeof(param));
        p = (char *)p + sizeof(CALI_L1_LMT_PARAM_T);
        CopyMemory((void *) p, tTxCfg, sizeof(PC_NONSIGNAL_LMT_TX_CFG_T) * pFDTReq->nTxCfgNum);
        p = (char *)p + sizeof(PC_NONSIGNAL_LMT_TX_CFG_T) * pFDTReq->nTxCfgNum;
        CopyMemory((void *) p, tRxCfg, sizeof(PC_NONSIGNAL_LMT_RX_CFG_T) * pFDTReq->nRxCfgNum);
    }
    catch (const std::bad_alloc& /*e*/)
    {
    	pData = NULL;
    }
    if (NULL == pData)
    {
        delete []tTxCfg;
        delete []tRxCfg;

        LogFmtStrA(SPLOGLV_ERROR, "%s: Memory allocation failed!");
        assert(0);
        return SP_E_PHONE_ALLOC_MEMORY;
    }
   
	delete []tTxCfg;
	delete []tRxCfg;
	
	ZeroMemory((void *)&m_diagBuff, sizeof(m_diagBuff));
	uint32 recvLen = 0;
	int nOperCode = SendAndRecv(hd, (const void *)pData, uDataLen, \
		hd, (void *)&m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);
	delete []pData;
	pData = NULL;
	if (SP_OK == nOperCode)
	{
		unsigned long iExpLength =  sizeof(CALI_L1_LMT_CNF_T) ;
		if (recvLen < iExpLength)
		{
			LogFmtStrA(SPLOGLV_ERROR, "gsmLmt_init, Invalid response length %d!",recvLen);
			return SP_E_PHONE_INVALID_LENGTH;
		}
		CALI_L1_LMT_CNF_T *pCnf = (CALI_L1_LMT_CNF_T *)m_diagBuff;
		L1_RXTXLOOP_RESULT_E eOperRes = (L1_RXTXLOOP_RESULT_E)(pCnf->nOperation_status);
		if (RET_RXTXLOOP_OK != eOperRes)
		{
			LogFmtStrA(SPLOGLV_INFO, "gsmLMT_lnit,Operation status: %d", eOperRes);
			return SP_E_PHONE_INVALID_STATE;
		}
	}

	return nOperCode;
}

SPRESULT CCaliCmd::gsmLmt_IsSync()
{
	LogFmtStrA(SPLOGLV_INFO, "gsmLmt_IsSync");

	CALI_L1_LMT_PARAM_T param;
	ZeroMemory(&param,sizeof(param));
	param.nCmd		= Convert16((uint16)CMD_LMT_SYNC);
	TOOL_L1_DIAG_CALI_PARAM_T L1;
	L1.is_nv    = Convert16((uint16)0);
	L1.band     = Convert16((uint16)0);
	L1.type     = Convert16((uint16)CALI_NOSINGAL_LMT);
	L1.index    = Convert16((uint16)0);
	L1.length   = Convert16((uint16)sizeof(param));

	DIAG_HEADER hd;            
	hd.type    = DIAG_CALIBRATION;        
	hd.subtype = L1_WRITE;         
	hd.sn      = 0;    // SW bug. SN of response packet is always 0. 
	hd.len     = 0;
	unsigned long  uDataLen = sizeof(L1) + sizeof(param);

    unsigned char* pData = NULL;
    try
    {
        pData = new unsigned char[uDataLen];
        ZeroMemory((void *) pData, uDataLen);

        CopyMemory((void *) pData, (const void *)&L1, sizeof(L1));
        void *p =  (void *)(pData + sizeof(L1));
        CopyMemory((void *) p, &param, sizeof(param));
    }
    catch (const std::bad_alloc& /*e*/)
    {
        pData = NULL;
    }
    if (NULL == pData)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Memory allocation failed!");
        assert(0);
        return SP_E_PHONE_ALLOC_MEMORY;
    }

	uint32 recvLen = 0;
	int nOperCode = SendAndRecv(hd, (const void *)pData, uDataLen, \
		hd, (void *)&m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);

	delete []pData;
	pData = NULL;
	if (SP_OK == nOperCode)
	{
		unsigned long iExpLength =  sizeof(CALI_L1_LMT_CNF_T) ;
		if (recvLen < iExpLength)
		{
			LogFmtStrA(SPLOGLV_ERROR, "gsmLmt_Sync, Invalid response length %d!",recvLen);
			return SP_E_PHONE_INVALID_LENGTH;
		}
		CALI_L1_LMT_CNF_T *pCnf = (CALI_L1_LMT_CNF_T *)m_diagBuff;
		L1_RXTXLOOP_RESULT_E eOperRes = (L1_RXTXLOOP_RESULT_E)(pCnf->nOperation_status);
		if (RET_RXTXLOOP_OK != eOperRes)
		{
			LogFmtStrA(SPLOGLV_INFO, "gsmLmt_Sync Operation status: %d", eOperRes);
			return SP_E_PHONE_INVALID_STATE;
		}
	}
	return nOperCode;
}

SPRESULT CCaliCmd::gsmLmt_start()
{
	LogFmtStrA(SPLOGLV_INFO, "gsmLmt_start");
	CALI_L1_LMT_PARAM_T param;
	ZeroMemory(&param,sizeof(param));
	param.nCmd		= Convert16((uint16)CMD_LMT_START);
	TOOL_L1_DIAG_CALI_PARAM_T L1;
	L1.is_nv    = Convert16((uint16)0);
	L1.band     = Convert16((uint16)0);
	L1.type     = Convert16((uint16)CALI_NOSINGAL_LMT);
	L1.index    = Convert16((uint16)0);
	L1.length   = Convert16((uint16)sizeof(param));
	DIAG_HEADER hd;            
	hd.type    = DIAG_CALIBRATION;        
	hd.subtype = L1_WRITE;         
	hd.sn      = 0;    // SW bug. SN of response packet is always 0. 
	hd.len     = 0;
	unsigned long  uDataLen = sizeof(L1) + sizeof(param);

    unsigned char* pData = NULL;
    try
    {
        pData    = new unsigned char[uDataLen];
        ZeroMemory((void *) pData, uDataLen);

        CopyMemory((void *) pData, (const void *)&L1, sizeof(L1));
        void *p =  (void *)(pData + sizeof(L1));
        CopyMemory((void *) p, &param, sizeof(param));
    }
    catch (const std::bad_alloc& /*e*/)
    {
        pData = NULL;
    }
    if (NULL == pData)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Memory allocation failed!");
        assert(0);
        return SP_E_PHONE_ALLOC_MEMORY;
    }

	uint32 recvLen = 0;
	int nOperCode = SendAndRecv(hd, (const void *)pData, uDataLen, \
		hd, (void *)&m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);
	delete []pData;
	pData = NULL;
	if (SP_OK == nOperCode)
	{
		unsigned long iExpLength =  sizeof(CALI_L1_LMT_CNF_T) ;
		if (recvLen < iExpLength)
		{
			LogFmtStrA(SPLOGLV_ERROR, "gsmLmt_start, Invalid response length %d!",recvLen);
			return SP_E_PHONE_INVALID_LENGTH;
		}
		CALI_L1_LMT_CNF_T *pCnf = (CALI_L1_LMT_CNF_T *)m_diagBuff;
		L1_RXTXLOOP_RESULT_E eOperRes = (L1_RXTXLOOP_RESULT_E)(pCnf->nOperation_status);
		if (RET_RXTXLOOP_OK != eOperRes)
		{
			LogFmtStrA(SPLOGLV_INFO, "gsmLMT_Start Operation status: %d", eOperRes);
			return SP_E_PHONE_INVALID_STATE;
		}
	}
	return nOperCode;
}

SPRESULT CCaliCmd::gsmLmt_GetRxResult(int nDataCount, PC_NONSIGNAL_LMT_RX_CNF_T *ptRxRes)
{
	LogFmtStrA(SPLOGLV_INFO, "gsmLmt_GetRxResult");

	if (nDataCount == 0 ||ptRxRes == NULL )
	{
		LogFmtStrA(SPLOGLV_INFO, "gsmLMT_GetRXLvl, nDataCount:%d ,ptRxRes:%x" ,nDataCount , ptRxRes);
		return SP_E_PHONE_INVALID_PARAMETER;
	}
	CALI_L1_LMT_PARAM_T param;
	ZeroMemory(&param,sizeof(param));
	param.nCmd		= Convert16((uint16)CMD_LMT_GET_RXLEV_SEBER);
	TOOL_L1_DIAG_CALI_PARAM_T L1;
	L1.is_nv    = Convert16((uint16)0);
	L1.band     = Convert16((uint16)0);
	L1.type     = Convert16((uint16)CALI_NOSINGAL_LMT);
	L1.index    = Convert16((uint16)0);
	L1.length   = Convert16((uint16)sizeof(param));
	DIAG_HEADER hd;            
	hd.type    = DIAG_CALIBRATION;        
	hd.subtype = L1_WRITE;         
	hd.sn      = 0;    // SW bug. SN of response packet is always 0. 
	hd.len     = 0;
	unsigned long  uDataLen = sizeof(L1) + sizeof(param);

    unsigned char* pData = NULL;
    try
    {
        pData = new unsigned char[uDataLen];
        ZeroMemory((void *) pData, uDataLen);

        CopyMemory((void *) pData, (const void *)&L1, sizeof(L1));
        void *p =  (void *)(pData + sizeof(L1));
        CopyMemory((void *) p, &param, sizeof(param));
    }
    catch (const std::bad_alloc& /*e*/)
    {
        pData = NULL;
    }
    if (NULL == pData)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Memory allocation failed!");
        assert(0);
        return SP_E_PHONE_ALLOC_MEMORY;
    }
	
	uint32 recvLen = 0;
	int nOperCode = SendAndRecv(hd, (const void *)pData, uDataLen, \
		hd, (void *)&m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);
	delete []pData;
	pData = NULL;
	if (SP_OK == nOperCode)
	{
		if (recvLen < sizeof(CALI_L1_LMT_CNF_T))
		{
			LogFmtStrA(SPLOGLV_ERROR, "gsmLMT_GetRXLvl, Invalid response length %d!", recvLen);
			return SP_E_PHONE_INVALID_LENGTH;
		}
		CALI_L1_LMT_CNF_T  tCnf ;
		ZeroMemory(&tCnf , sizeof(CALI_L1_LMT_CNF_T));
		CopyMemory(&tCnf, m_diagBuff, sizeof(CALI_L1_LMT_CNF_T));
		if (tCnf.nOperation_status == RET_RXTXLOOP_SEBER_UNDER_TESTING)
		{
			return RET_RXTXLOOP_SEBER_UNDER_TESTING;
		}
		if (tCnf.nOperation_status == RET_RXTXLOOP_OK)
		{
			if (nDataCount < tCnf.nDataCount)
			{
				LogFmtStrA(SPLOGLV_INFO, "gsmLMT_GetRXLvl Operation status: %d ,data count: %d", tCnf.nOperation_status,tCnf.nDataCount );
				return SP_E_PHONE_INVALID_LENGTH;
			}
			if (recvLen < sizeof(CALI_L1_LMT_CNF_T) + sizeof(PC_NONSIGNAL_LMT_RX_CNF_T) * tCnf.nDataCount)
			{
				LogFmtStrA(SPLOGLV_INFO, "gsmLMT_GetRXLvl Operation status: %d ,data count: %d", tCnf.nOperation_status,tCnf.nDataCount );
				return SP_E_PHONE_INVALID_LENGTH;
			}
			CopyMemory(ptRxRes, (void *)(m_diagBuff + sizeof(CALI_L1_LMT_CNF_T)), sizeof(PC_NONSIGNAL_LMT_RX_CNF_T) * tCnf.nDataCount );
			return nOperCode;
		}
		else
		{
			LogFmtStrA(SPLOGLV_INFO, "gsmLMT_Start Operation status: %d", tCnf.nOperation_status);
			return SP_E_PHONE_INVALID_STATE;
		}
	}
	return nOperCode;
}
SPRESULT CCaliCmd::gsmLmt_Stop()
{
	LogFmtStrA(SPLOGLV_INFO, "gsmLmt_stop");
	CALI_L1_LMT_PARAM_T param;
	ZeroMemory(&param,sizeof(param));
	param.nCmd		= Convert16((uint16)CMD_LMT_STOP);
	TOOL_L1_DIAG_CALI_PARAM_T L1;
	L1.is_nv    = Convert16((uint16)0);
	L1.band     = Convert16((uint16)0);
	L1.type     = Convert16((uint16)CALI_NOSINGAL_LMT);
	L1.index    = Convert16((uint16)0);
	L1.length   = Convert16((uint16)sizeof(param));
	DIAG_HEADER hd;            
	hd.type    = DIAG_CALIBRATION;        
	hd.subtype = L1_WRITE;         
	hd.sn      = 0;    // SW bug. SN of response packet is always 0. 
	hd.len     = 0;
	unsigned long  uDataLen = sizeof(L1) + sizeof(param);

    unsigned char* pData = NULL;
    try
    {
        pData = new unsigned char[uDataLen];
        ZeroMemory((void *) pData, uDataLen);

        CopyMemory((void *) pData, (const void *)&L1, sizeof(L1));
        void *p =  (void *)(pData + sizeof(L1));
        CopyMemory((void *) p, &param, sizeof(param));
    }
    catch (const std::bad_alloc& /*e*/)
    {
        pData = NULL;
    }
    if (NULL == pData)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Allocate memory failed!");
        assert(0);
        return SP_E_PHONE_ALLOC_MEMORY;
    }
	
	uint32 recvLen = 0;
	int nOperCode = SendAndRecv(hd, (const void *)pData, uDataLen, \
		hd, (void *)&m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);
	delete []pData;
	pData = NULL;
	if (SP_OK == nOperCode)
	{
		unsigned long iExpLength =  sizeof(CALI_L1_LMT_CNF_T) ;
		if (recvLen < iExpLength)
		{
			LogFmtStrA(SPLOGLV_ERROR, "gsmLmt_stop, Invalid response length %d!",recvLen);
			return SP_E_PHONE_INVALID_LENGTH;
		}
		CALI_L1_LMT_CNF_T *pCnf = (CALI_L1_LMT_CNF_T *)m_diagBuff;
		L1_RXTXLOOP_RESULT_E eOperRes = (L1_RXTXLOOP_RESULT_E)(pCnf->nOperation_status);
		if (RET_RXTXLOOP_OK != eOperRes)
		{
			LogFmtStrA(SPLOGLV_INFO, "gsmLmt_stop Operation status: %d", eOperRes);
			return SP_E_PHONE_INVALID_STATE;
		}
	}
	return nOperCode;
}

SPRESULT CCaliCmd::gsmGetTransTemperature( TRANSCEIVER_TEMP_T *pTemp )
{
	LogFmtStrA(SPLOGLV_INFO, "Get Transceiver Temperature:");
	if (NULL == pTemp || IsBadWritePtr(pTemp, sizeof(TRANSCEIVER_TEMP_T)))
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid parameters!", __FUNCTION__);
		return SP_E_PHONE_INVALID_PARAMETER;
	}
	L1_TRANSCEIVER_TEMP_T temp;
	int nOperCode = gsmLoadParam(L1_RAM, CALI_TRANSCEIVER_TEMP, BI_EGSM, 0, sizeof(temp), (void *)&temp, m_dwTimeOut);
	if ((SP_OK == nOperCode))
	{
		pTemp->adc1 = Convert16(temp.adc1);
		pTemp->k = Convert16(temp.k);
		pTemp->adc0 = Convert16(temp.adc0);
		pTemp->c0 = Convert16(temp.c0);
		pTemp->c1 = Convert16(temp.c1);
		LogFmtStrA(SPLOGLV_INFO, "Transceiver Temperature: adc1 = %d , adc0 = %d, k = %d, c0 = %d, c1 = %d", pTemp->adc1, pTemp->adc0, pTemp->k, pTemp->c0, pTemp->c1);
		return SP_OK;
	}

	return nOperCode;
}

//////////////////////////////////////////////////////////////////////////
///					MGB
SPRESULT CCaliCmd::mgbGsmRFICSWitch( SP_BAND_INFO eBand,unsigned int nRFIC )
{
	
	LogFmtStrA(SPLOGLV_INFO, "RFICSWitch: ");
	
    TOOL_L1_DIAG_RFIC_T param;
	param.rf_ic = nRFIC;

    TOOL_L1_DIAG_CALI_PARAM_T L1;
    L1.is_nv    = 0; //RAM
    L1.band     = Convert16((uint16)gsmIoBand(eBand));
    L1.type     = Convert16((uint16)CALI_RFIC_SWITCH);
    L1.index    = Convert16((uint16)0);
    L1.length   = Convert16((uint16)sizeof(param));
    
    DIAG_HEADER hd;            
    hd.type    = DIAG_CALIBRATION;        
    hd.subtype = L1_WRITE;         
    hd.sn      = 0;    // SW bug. SN of response packet is always 0. 
    hd.len     = 0;

    // Request Data: TOOL_L1_DIAG_CALI_PARAM_T + CALI_L1_RXTXLOOP_PARAM_T
    unsigned long  uDataLen = sizeof(L1) + sizeof(param);

	unsigned char* pData = NULL;
	try
	{
		pData = new unsigned char[uDataLen];
		ZeroMemory((void *) pData, uDataLen);
		CopyMemory((void *) pData, (const void *)&L1, sizeof(L1));
		void *p =  (void *)(pData + sizeof(L1));
		CopyMemory((void *) p, &param, sizeof(param));
	}
	catch (const std::bad_alloc& /*e*/)
	{
		pData = NULL;
	}
    
    ZeroMemory((void *)&m_diagBuff, sizeof(m_diagBuff));
	uint32 recvLen = 0;
	int nOperCode = SendAndRecv(hd, (const void *)pData, uDataLen, \
		hd, (void *)&m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);

    delete []pData;
    pData = NULL;

    // Response Data: PC_NONSIGNAL_CNF_T
    if (SP_OK == nOperCode)
    {
        DIAG_TOOL_CNF_T  *pCNF = (DIAG_TOOL_CNF_T *)m_diagBuff;
		uint16 	noperation_status = Convert16(pCNF->operation_status);
		
		if (1 == noperation_status)
		{
			// pass 
			return nOperCode;
		}
		else
		{
			LogFmtStrA(SPLOGLV_ERROR, "[RFICSWitch] Operation failed, state = %d.", noperation_status);
			// Flash operate failed
			return SP_E_PHONE_INVALID_STATE;
		}       
    }

	return nOperCode;
}

SPRESULT CCaliCmd::mgbGsmLoCalInit()
{
	LogFmtStrA(SPLOGLV_INFO, "mgbGsmLoCalInit: ");

	TOOL_L1_DIAG_CALI_PARAM_T L1;
	L1.is_nv    = 0; //RAM
	L1.band     = Convert16((uint16)gsmIoBand(BI_EGSM));
	L1.type     = Convert16((uint16)CALI_MGB_CAL);
	L1.index    = Convert16((uint16)0);
	L1.length   = 0;

	DeclareDiagHeader(hd, DIAG_CALIBRATION, CALI_RF_LO_INIT);

	// Request Data: TOOL_L1_DIAG_CALI_PARAM_T + CALI_L1_RXTXLOOP_PARAM_T
	unsigned long  uDataLen = sizeof(L1) ;

	unsigned char* pData = NULL;
	try
	{
		pData = new unsigned char[uDataLen];
		ZeroMemory((void *) pData, uDataLen);
		CopyMemory((void *) pData, (const void *)&L1, sizeof(L1));
	}
	catch (const std::bad_alloc& /*e*/)
	{
		pData = NULL;
	}

	ZeroMemory((void *)&m_diagBuff, sizeof(m_diagBuff));
	uint32 recvLen = 0;
	int nOperCode = SendAndRecv(hd, (const void *)pData, uDataLen, \
		hd, (void *)&m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);

	delete []pData;
	pData = NULL;

	// Response Data: PC_NONSIGNAL_CNF_T
	if (SP_OK == nOperCode)
	{
		if (recvLen < sizeof(DIAG_TOOL_CNF_T))
		{
			LogFmtStrA(SPLOGLV_ERROR, "[mgbGsmLoCalInit] Invalid data length.");
			// Flash operate failed
			return SP_E_PHONE_INVALID_STATE;		
		}

		DIAG_TOOL_CNF_T  *pCNF = (DIAG_TOOL_CNF_T *)m_diagBuff;
		uint16 	noperation_status = Convert16(pCNF->operation_status);

		if (1 == noperation_status)
		{

			return SP_OK;
		}
		else
		{
			LogFmtStrA(SPLOGLV_ERROR, "[mgbGsmLoCalInit] Operation failed, state = %d.", noperation_status);
			// Flash operate failed
			return SP_E_PHONE_INVALID_STATE;
		}       
	}

	return nOperCode;
}

SPRESULT CCaliCmd::mgbGsmLoCalEnd()
{
	LogFmtStrA(SPLOGLV_INFO, "mgbGsmLoCalEnd: ");

	TOOL_L1_DIAG_CALI_PARAM_T L1;
	L1.is_nv    = 0; //RAM
	L1.band     = Convert16((uint16)gsmIoBand(BI_EGSM));
	L1.type     = Convert16((uint16)CALI_MGB_CAL);
	L1.index    = Convert16((uint16)0);
	L1.length   = 0;

	DeclareDiagHeader(hd, DIAG_CALIBRATION, CALI_RF_LO_END);

	// Request Data: TOOL_L1_DIAG_CALI_PARAM_T + CALI_L1_RXTXLOOP_PARAM_T
	unsigned long  uDataLen = sizeof(L1) ;

	unsigned char* pData = NULL;
	try
	{
		pData = new unsigned char[uDataLen];
		ZeroMemory((void *) pData, uDataLen);
		CopyMemory((void *) pData, (const void *)&L1, sizeof(L1));
	}
	catch (const std::bad_alloc& /*e*/)
	{
		pData = NULL;
	}

	ZeroMemory((void *)&m_diagBuff, sizeof(m_diagBuff));
	uint32 recvLen = 0;
	int nOperCode = SendAndRecv(hd, (const void *)pData, uDataLen, \
		hd, (void *)&m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);

	delete []pData;
	pData = NULL;

	// Response Data: PC_NONSIGNAL_CNF_T
	if (SP_OK == nOperCode)
	{
		DIAG_TOOL_CNF_T  *pCNF = (DIAG_TOOL_CNF_T *)m_diagBuff;
		uint16 	noperation_status = Convert16(pCNF->operation_status);

		if (1 == noperation_status)
		{
			// pass 
			return nOperCode;
		}
		else
		{
			LogFmtStrA(SPLOGLV_ERROR, "[mgbGsmLoCalEnd] Operation failed, state = %d.", noperation_status);
			// Flash operate failed
			return SP_E_PHONE_INVALID_STATE;
		}       
	}

	return nOperCode;
}

SPRESULT CCaliCmd::mgbGsmLoCal( const CALI_LO_PARAM_REQ_T* pLoCalparam )
{

	LogFmtStrA(SPLOGLV_INFO, "mgbGsmLoCal: ");

	TOOL_L1_DIAG_LO_PARAM_T param;

	param.flag			= Convert16(pLoCalparam->flag); 
	param.link_path		= Convert16(pLoCalparam->link_path);
	param.gainIndex_TX	= Convert16(pLoCalparam->gainIndex_TX);
	param.gainIndex_RX	= Convert16(pLoCalparam->gainIndex_RX);
	param.reg_273		= Convert16(pLoCalparam->reg_273);
	param.reg_274		= Convert16(pLoCalparam->reg_274);
	param.LNA			= Convert16(pLoCalparam->LNA);
	param.reserved		= Convert16(pLoCalparam->reserved);

	TOOL_L1_DIAG_CALI_PARAM_T L1;
	L1.is_nv    = 0; //RAM
	L1.band     = Convert16((uint16)BI_EGSM);
	L1.type     = Convert16((uint16)CALI_MGB_CAL);
	L1.index    = Convert16((uint16)0);
	L1.length   = Convert16((uint16)sizeof(param));

	DeclareDiagHeader(hd, DIAG_CALIBRATION, CALI_RF_LO_CAL);

	// Request Data: TOOL_L1_DIAG_CALI_PARAM_T + CALI_L1_RXTXLOOP_PARAM_T
	unsigned long  uDataLen = sizeof(L1) + sizeof(param);

	unsigned char* pData = NULL;
	try
	{
		pData = new unsigned char[uDataLen];
		ZeroMemory((void *) pData, uDataLen);
		CopyMemory((void *) pData, (const void *)&L1, sizeof(L1));
		void *p =  (void *)(pData + sizeof(L1));
		CopyMemory((void *) p, &param, sizeof(param));
	}
	catch (const std::bad_alloc& /*e*/)
	{
		pData = NULL;
	}

	ZeroMemory((void *)&m_diagBuff, sizeof(m_diagBuff));
	uint32 recvLen = 0;
	int nOperCode = SendAndRecv(hd, (const void *)pData, uDataLen, \
		hd, (void *)&m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);

	delete []pData;
	pData = NULL;

	// Response Data: PC_NONSIGNAL_CNF_T
	if (SP_OK == nOperCode)
	{
		DIAG_TOOL_CNF_T  *pCNF = (DIAG_TOOL_CNF_T *)m_diagBuff;
		uint16 	noperation_status = Convert16(pCNF->operation_status);

		if (1 == noperation_status)
		{
			// pass 
			return nOperCode;
		}
		else
		{
			LogFmtStrA(SPLOGLV_ERROR, "[mgbGsmLoCal] Operation failed, state = %d.", noperation_status);
			// Flash operate failed
			return SP_E_PHONE_INVALID_STATE;
		}       
	}

	return nOperCode;
}

SPRESULT CCaliCmd::mgbGsmReadPdValue(SP_BAND_INFO eBand, uint16 *pPdValue)
{
	LogFmtStrA(SPLOGLV_INFO, "mgbGsmGetPdValue: band = %d", eBand);

	if ( NULL == pPdValue )
	{
		LogFmtStrA(SPLOGLV_ERROR, "[mgbGsmGetPdValue] Invalid parameters!");
		return SP_E_PHONE_INVALID_PARAMETER;
	}
	else
	{
		*pPdValue = 0;
	}

	uint16 nPdValue = 0;
	SPRESULT res = gsmLoadParam(L1_RAM, CALI_RF_EDGE_POWER_DETECT, eBand, 0, sizeof(uint16), (void *)&nPdValue, m_dwTimeOut);

	if (SP_OK != res)
	{
		return res;
	}
	else
	{
		*pPdValue = Convert16((uint16)nPdValue);
		LogFmtStrA(SPLOGLV_INFO, "rxlev = %d (0x%X)", *pPdValue, *pPdValue);
		return SP_OK;
	}
}


SPRESULT CCaliCmd::gsmAntSwitch(SP_BAND_INFO eBand, uint32 nAnt)
{
	LogFmtStrA(SPLOGLV_INFO, "gsmAntSwitch Ant: %d", nAnt);

	TOOL_L1_DIAG_RFIC_T param;
	param.rf_ic = nAnt;

	TOOL_L1_DIAG_CALI_PARAM_T L1;
	L1.is_nv    = 0; //RAM
	L1.band     = Convert16((uint16)gsmIoBand(eBand));
	L1.type     = Convert16((uint16)CALI_ANT_SWITCH);
	L1.index    = Convert16((uint16)0);
	L1.length   = Convert16((uint16)sizeof(param));

	DIAG_HEADER hd;            
	hd.type    = DIAG_CALIBRATION;        
	hd.subtype = L1_WRITE;         
	hd.sn      = 0;    // SW bug. SN of response packet is always 0. 
	hd.len     = 0;

	unsigned long  uDataLen = sizeof(L1) + sizeof(param);

	unsigned char* pData = NULL;
	try
	{
		pData = new unsigned char[uDataLen];
		ZeroMemory((void *) pData, uDataLen);
		CopyMemory((void *) pData, (const void *)&L1, sizeof(L1));
		void *p =  (void *)(pData + sizeof(L1));
		CopyMemory((void *) p, &param, sizeof(param));
	}
	catch (const std::bad_alloc& /*e*/)
	{
		pData = NULL;
	}

	ZeroMemory((void *)&m_diagBuff, sizeof(m_diagBuff));
	uint32 recvLen = 0;
	int nOperCode = SendAndRecv(hd, (const void *)pData, uDataLen, \
		hd, (void *)&m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);

	delete []pData;
	pData = NULL;

	// Response Data: PC_NONSIGNAL_CNF_T
	if (SP_OK == nOperCode)
	{
		DIAG_TOOL_CNF_T  *pCNF = (DIAG_TOOL_CNF_T *)m_diagBuff;
		uint16 	noperation_status = Convert16(pCNF->operation_status);

		if (1 == noperation_status)
		{
			// pass 
			return nOperCode;
		}
		else
		{
			LogFmtStrA(SPLOGLV_ERROR, "[gsmAntSwitch] Operation failed, state = %d.", noperation_status);
			// Flash operate failed
			return SP_E_PHONE_INVALID_STATE;
		}       
	}

	return nOperCode;
}


SPRESULT CCaliCmd::ModemV3_GSM_COMMOM_GetVersion(GSM_VERSION_T* pVersion)
{
	CFnLog fuLog(GetISpLogObject(), L"ModemV3_GSM_COMMOM_GetVersion");

	if ( NULL == pVersion )
	{
		LogFmtStrA(SPLOGLV_INFO, "GSM version value is empty.");
		return SP_E_PHONE_INVALID_PARAMETER;
	}

	uint32 recvSize = 0;

	L1_RFB_COM_QUERY_CAL_IF_VERSION_REQ_T L1;
	ZeroMemory((void* )&L1, sizeof(L1));
	L1.head.SubCmdCode  = RF_COM_QUERY_CAL_IF_VERSION_REQ;
	L1.head.SubCmdSize  = (uint16)sizeof(L1);

	L1.rf_mode = RF_MODE_GSM;

	DeclareDiagHeader(hd, DIAG_MODEM_RF_E, ZERO_SUBTYPE);

	CHKRESULT(SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvSize, m_dwTimeOut));

	if (recvSize < sizeof(L1_RFB_COM_QUERY_CAL_IF_VERSION_RLT_T))
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s:  Invalid response package size.", __FUNCTION__);
		return SP_E_PHONE_INVALID_LENGTH;
	}

	CHKRESULT(DetermineStatus(RF_COM_QUERY_CAL_IF_VERSION_REQ, m_diagBuff, recvSize));

	L1_RFB_COM_QUERY_CAL_IF_VERSION_RLT_T* pRLT = (L1_RFB_COM_QUERY_CAL_IF_VERSION_RLT_T*)m_diagBuff;
	memcpy(pVersion, pRLT->if_version, sizeof(GSM_VERSION_T));

	return SP_OK;
}

SPRESULT CCaliCmd::edgeSetFtFlag(PC_EDGE_FT_FLAG flagState)
{
    LogFmtStrA(SPLOGLV_INFO, "Set Edge FT Scene Flag %s", flagState.edge_ft_on_off? "On" : "Off");

	CALI_EDGE_FT_T L1;
	L1.edge_ft_on_off = flagState.edge_ft_on_off;

    return gsmSaveParam(L1_RAM, CALI_EDGE_FT_ON_OFF, BI_EGSM, 0, sizeof(L1), (const void* )&L1, m_dwTimeOut);
}
