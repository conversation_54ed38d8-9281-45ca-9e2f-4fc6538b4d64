#include "StdAfx.h"
#include "AppSettings.h"
#include "phdef.h"
#include "CLocks.h"

extern std::wstring GetAppPath(void);

CAppSettings* CAppSettings::s_instance = NULL;
CSimpleLock CAppSettings::s_lock;
//////////////////////////////////////////////////////////////////////////
CAppSettings::CAppSettings(void)
	: m_nLogLevel(1/*SPLOGLV_ERROR*/)
	, m_nDevHoundLevel(5)
	, m_dwTimeOut(TIMEOUT_10S)
	, m_nEndian(SP_LITTLE_ENDIAN)
	, m_bComparePort(TRUE)
	, m_bModemToPC(FALSE)
	, m_bCheckVComDrvVersion(FALSE)
	, m_bSaveLogelData(FALSE)
	, m_bAuthMode(FALSE)
{
	Load();
}

void CAppSettings::Load(void)
{
	std::wstring strAppPath = GetAppPath() + L"\\PhoneCommand.ini";

	/// Log Level
	m_nLogLevel = GetPrivateProfileIntW(L"Log", L"Level", 1 /*SPLOGLV_ERROR*/, strAppPath.c_str());
	m_nDevHoundLevel = GetPrivateProfileIntW(L"Log", L"DevHound", 5 /*SPLOGLV_VERBOSE*/, strAppPath.c_str());

	/// Socket
	LPCWSTR SOCKET = L"Socket";
	m_Socket.bConnect = (1 == GetPrivateProfileIntW(SOCKET, L"Connect", 0, strAppPath.c_str())) ? TRUE : FALSE;
	wchar_t szIP[MAX_IP_ADDR_LEN] = { 0 };
	GetPrivateProfileStringW(SOCKET, L"IP", L"127.0.0.1", szIP, _countof(szIP), strAppPath.c_str());
	WideCharToMultiByte(CP_ACP, 0, szIP, -1, m_Socket.szIP, sizeof(m_Socket.szIP), NULL, NULL);
	m_Socket.dwPort = GetPrivateProfileIntW(SOCKET, L"Port", 36667, strAppPath.c_str());

	LPCWSTR PROPERTY = L"Property";
	/// TimeOut
	m_dwTimeOut = GetPrivateProfileIntW(PROPERTY, L"Command Timeout", TIMEOUT_10S, strAppPath.c_str());

	/// MaxTimeOut
	m_MaxTimeout = GetPrivateProfileIntW(PROPERTY, L"Command MaxTimeout", TIMEOUT_180S, strAppPath.c_str());

	/// MinTimeOut
	m_MinTimeout = GetPrivateProfileIntW(PROPERTY, L"Command MinTimeout", TIMEOUT_1S, strAppPath.c_str());

	/// Endian
	m_nEndian = GetPrivateProfileIntW(PROPERTY, L"Endian Mode", SP_LITTLE_ENDIAN, strAppPath.c_str());

	/// Compare port
	m_bComparePort = (1 == GetPrivateProfileIntW(PROPERTY, L"Compare USB port", 1, strAppPath.c_str())) ? TRUE : FALSE;

	/// USB driver filter
	WCHAR szValue[2048] = { 0 };
	GetPrivateProfileStringW(PROPERTY, L"USB Driver Name", L"SPRD U2S Diag", szValue, _countof(szValue), strAppPath.c_str());
	m_sDrivName = szValue;

	GetPrivateProfileStringW(PROPERTY, L"USB Log Name", L"USB LOG Port", szValue, _countof(szValue), strAppPath.c_str());
	m_sDrivLogName = szValue;

	GetPrivateProfileStringW(PROPERTY, L"Ignore SN", L"", szValue, _countof(szValue), strAppPath.c_str());
	m_strIgnoreSn = szValue;

	m_bModemToPC = (1 == GetPrivateProfileIntW(PROPERTY, L"ModemToPC", 0, strAppPath.c_str())) ? TRUE : FALSE;

	m_bCheckVComDrvVersion = (1 == GetPrivateProfileIntW(PROPERTY, L"Check VCOM Driver Version", FALSE, strAppPath.c_str())) ? TRUE : FALSE;

	m_bSaveLogelData = (1 == GetPrivateProfileIntW(PROPERTY, L"SaveLogelFile", FALSE, strAppPath.c_str())) ? TRUE : FALSE;

	m_bAuthMode = (1 == GetPrivateProfileIntW(PROPERTY, L"AuthMode", FALSE, strAppPath.c_str())) ? TRUE : FALSE;

	m_nWaitTimeLogPort = GetPrivateProfileIntW(PROPERTY, L"WaitingTimeForLogPort", 200, strAppPath.c_str());
}

void CAppSettings::GetValue(SettingType eType, LPVOID lpValue)
{
	switch (eType)
	{
	case LogLevel:
		*((UINT*)lpValue) = m_nLogLevel;
		break;

	case TimeOut:
		*((DWORD*)lpValue) = m_dwTimeOut;
		break;

	case Endian:
		*((UINT*)lpValue) = m_nEndian;
		break;

	case ComparePort:
		*((BOOL*)lpValue) = m_bComparePort;
		break;

	case DevHound:
		*((UINT*)lpValue) = m_nDevHoundLevel;
		break;

	case Socket:
		*((SOCKET_SETTING*)lpValue) = m_Socket;
		break;

	case IgnoreSn:
		*((std::wstring*)lpValue) = m_strIgnoreSn;
		break;

	case ModemToPC:
		*((BOOL*)lpValue) = m_bModemToPC;
		break;

	case CheckVComDrvVersion:
		*((BOOL*)lpValue) = m_bCheckVComDrvVersion;
		break;

	case SaveLogelData:
		*((BOOL*)lpValue) = m_bSaveLogelData;
		break;

	case MaxTimeout:
		*((DWORD*)lpValue) = m_MaxTimeout;
		break;

	case MinTimeout:
		*((DWORD*)lpValue) = m_MinTimeout;
		break;

	case AuthMode:
		*((DWORD*)lpValue) = m_bAuthMode;
		break;

	case WaitTimeLogPort:
		*((UINT*)lpValue) = m_nWaitTimeLogPort;
		break;

	default:
		break;
	}
}

BOOL CAppSettings::IsValidDriver(LPCWSTR lpszDrivName)
{
	if (NULL == lpszDrivName)
	{
		return FALSE;
	}
	else
	{
		return (std::wstring::npos != m_sDrivName.find(lpszDrivName)) ? TRUE : FALSE;
	}
}

BOOL CAppSettings::IsValidLogDriver(LPCWSTR lpszDrivName)
{
	if (NULL == lpszDrivName)
	{
		return FALSE;
	}
	else
	{
		return (std::wstring::npos != m_sDrivLogName.find(lpszDrivName)) ? TRUE : FALSE;
	}
}
