﻿#pragma once

#include "PhoneCommand.h"
#include "Tr.h"
#include "diagdef.h"
#include "RecvPkgsList.h"
#include "IDevMonitor.h"
#include "EndianConvert.h"
#include "UeAssert.h"
#include "CSimpleLock.h"
#include "IContainer.h"
#include "PortCapture.h"

//////////////////////////////////////////////////////////////////////////
class IProtocolChannel;

/// 
#define MAX_DIAG_BUFF_SIZE      (0xFFFF)  

///
/// DIAG header declaration
///
#define DeclareDiagHeader(_hd, _type, _sub)       \
    DIAG_HEADER _hd;            \
    _hd.type    = _type;        \
    _hd.subtype = _sub;         \
    _hd.sn      = SPECIAL_SN;   \
    _hd.len     = 0;


/// Not including the protocol character such as 0x7D 0x5D
#define MIN_PKT_SIZE(_datalen)        (DWORD)(sizeof(DIAG_HEADER) + (_datalen) + 1/*7E*/ + 1/*7E*/) 

/// 
#define CheckResponseLength(rspLen, expLen)   \
    { \
        if ((rspLen) != (expLen)) { \
            LogFmtStrA(SPLOGLV_ERROR, "Invalid response length %d != %d!", (rspLen), (expLen)); \
            return SP_E_PHONE_INVALID_LENGTH; \
        }  \
    }

#define CheckValidPointer(_p)   \
    if (NULL == (_p)) { \
        LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid parameters!", __FUNCTION__); \
        return SP_E_PHONE_INVALID_PARAMETER; \
    }


class CCallBackMonitor : public IDevMonitor
{
public:
	CCallBackMonitor();
	virtual ~CCallBackMonitor();
	void OnDevChange(INT nEvent, LPCVOID lpData);
	BOOL OnDevAssert(DUT_ASSERT_STATE eState, LPCVOID lpData);
	void SetCB_OnDevChange(SPCB_OnDevChange cb, LPCVOID lpUserData)
	{
		m_cbDevChange = cb;
		m_lpDevChangeUser = lpUserData;
	}
	void SetCB_OnDevAssert(SPCB_OnDevAssert cb, LPCVOID lpUserData)
	{
		m_cbDevAssert = cb;
		m_lpDevAssertUser = lpUserData;
	}
private:
	SPCB_OnDevChange m_cbDevChange;
	SPCB_OnDevAssert m_cbDevAssert;
	LPCVOID m_lpDevChangeUser;
	LPCVOID m_lpDevAssertUser;
};

//////////////////////////////////////////////////////////////////////////
/// 
class CDiagBase : public IProtocolObserver
	, public CTr
{
public:
	CDiagBase(void);
	virtual ~CDiagBase(void);

	BOOL     Startup(ISpLog* lpTr);
	void     Cleanup(void);

	SPRESULT Open(PCCHANNEL_ATTRIBUTE lpArg);
	void     Close(void);

	uint32   GetUsbPort()
	{
		return m_ca.Com.dwPortNum;
	}

	SPRESULT SetProperty(INT nOption, INT nFlag, LPCVOID lpValue);
	SPRESULT GetProperty(INT nOption, INT nFlag, LPVOID  lpValue);

	/// raw data send and receive
	uint32   Write(LPCVOID lpData, uint32 u32BytesToSend, BOOL bCheckAssert = TRUE);
	uint32   Read(LPVOID lpBuff, uint32 u32BytesToRead, uint32 u32TimeOut);

	SPRESULT SendAndRecvRawPackage(const void* lpSendBuf, uint32 u32BytesToSend,
		void* lpBuff, uint32 u32BufSize, uint32* lpu32RecvBytes,
		DWORD dwTimeOut = TIMEOUT_3S);

	SPRESULT SendATCommand(LPCSTR lpszAT, BOOL bWantReply, LPVOID lpBuff, uint32 u32BufSize, uint32* lpu32RecvBytes, uint32 u32TimeOut = TIMEOUT_3S);

	//Bug 1732369 GE3F内存小，CNR的计算放到工具侧	
	SPRESULT ReadModuleCNR(LPCSTR lpszAT, LPVOID lpBuff, uint32 u32BufSize, uint32* lpu32RecvBytes, uint32 u32TimeOut = TIMEOUT_5S);
	void WakeupModuleCNRResponse(PRT_BUFF* lpData);
	// 
	SPRESULT SendAndRecv(
		const DIAG_HEADER& wh, const void* lpSendBuf, uint32 u32BytesToSend,
		DIAG_HEADER& rh, void* lpBuff, uint32 u32BufSize, uint32* lpu32RecvBytes,
		DWORD dwTimeOut = TIMEOUT_3S
	);

	SPRESULT SendDiagPackage(const DIAG_HEADER& wh, const void* lpSendBuf, uint32 u32BytesToSend)
	{
		return SendCmd(wh, lpSendBuf, u32BytesToSend);
	}

	///
	/// Endian
	///
	CEnianConvert m_EndianConvert;
#define Convert16 m_EndianConvert.Conv16
#define Convert32 m_EndianConvert.Conv32

	void AddMonitr(IDevMonitor* lpMonitor) { m_lpMonitor = lpMonitor; };
	IDevMonitor* GetMonitor(void) { return m_lpMonitor; };

	ICommChannel* GetLowChannel(BOOL bProtocol = FALSE)const;

	void SetDevChangeCallBack(SPCB_OnDevChange cb, LPCVOID lpUserData)
	{
		m_CallBackMonitor.SetCB_OnDevChange(cb, lpUserData);
	}
	void SetDevAssertCallBack(SPCB_OnDevAssert cb, LPCVOID lpUserData)
	{
		m_CallBackMonitor.SetCB_OnDevAssert(cb, lpUserData);
	}

	BOOL Opend();
	void WakeUpOnDumpStop(void);

	BOOL m_bAuthMode;
protected:
	void     Clear(void);



	///
	/// Unpack the response package
	///
	BOOL     UnpackPRT(LPCPRT_BUFF lpOrg, DIAG_HEADER* lpHD, void* lpBuff, uint32 u32BufSize, uint32* lpUnpackedSize);

	/// 
	/// Send command & Receive response package  
	///
	SPRESULT SendCmd(const DIAG_HEADER& hd, const void* lpData = NULL, uint32 u32BytesToSend = 0, BOOL bCheckAssert = TRUE);
	SPRESULT SendCmd(const DIAG_HEADER& hd, const void* lpData, uint32 u32BytesToSend, const CRecvPkgsList& recvCond);

	SPRESULT RecvCmd(CRecvPkgsList& recvList, DWORD dwTimeOut);
	SPRESULT RecvCmd(void* lpBuff, uint32 u32BufSize, uint32* lpu32RecvBytes, DWORD dwTimeOut = TIMEOUT_3S);

	///
	SPRESULT SendAndRecv(
		const DIAG_HEADER& hd, const void* lpSendBuf, uint32 u32BytesToSend,
		CRecvPkgsList& recvList,
		DWORD dwTimeOut = TIMEOUT_3S
	);

	void StartCaptureLog();


	///
	void WakeUpOnResponse(void);

	virtual void WakeupATResponse(PRT_BUFF* lpBuff);
	virtual void WakeupAPResponse(PRT_BUFF* lpBuff) = 0;
	virtual void WakeupAudioRecordResponse(PRT_BUFF* lpBuff) = 0;

protected:
	///
	/// TimeOut, Unit: ms
	///
	DWORD m_dwTimeOut;

	///
	/// MaxTimeout, Unit: ms
	///
	DWORD m_MaxTimeout;

	///
	/// MinTimeout, Unit: ms
	///
	DWORD m_MinTimeout;

	///
	/// DIAG buffer
	///
	uint8  m_diagBuff[MAX_DIAG_BUFF_SIZE];

	///
	/// Port open argument
	///
	CHANNEL_ATTRIBUTE m_ca;

	///
	/// BOOT up notify event
	///
	HANDLE m_hBootUpEvent;

	///
	SPDBT_INFO  m_DBT;

	/// UE Assert 
	CUeAssert* m_lpAssert;

	IContainer* m_pContainer;

	/// This is used for new project which has two ports when doing calibration.
	/// If this function returned -1, the log port is same to diag port.
	uint32     m_nLogPort;
	SP_HANDLE   m_hCapture;

	/// Save *.logel file 
	BOOL m_bSaveLogel;
	BOOL m_bLogUsingOtherPort;
	UINT m_nWaitTimeLogPort = 200;
private:
	///
	/// IProtocolObserver 
	///
	virtual int  OnChannelData(LPVOID lpData, ULONG reserved);
	virtual int  OnChannelEvent(unsigned int event, void* lpEventData);

	///
	/// Start or stop AYSN. receiving process
	/// 
	void StartAysnRecving(BOOL bStart);

	///
	/// Wake up and wait receiving event
	///
	//BOOL WaitForResponse(DWORD dwTimeOut);
	BOOL WaitForMultipleEvent(DWORD dwTimeout);


	///
	/// Buffer dispatch 
	///
	void Dispatching(LPCDIAG_HEADER lpHead, PRT_BUFF* lpBuff);
	BOOL CompareCond(LPCDIAG_HEADER lpCond1, LPCDIAG_HEADER lpCond2);

	///
	/// Observers 
	///
	BOOL RegisterObservers(void);
	void CleanUpObservers(void);

private:
	///
	/// DIAG. Channel 
	/// 
	IProtocolChannel* m_lpDiagChannel;
	BOOL m_bDeviceOpen;

	//
	// Callback
	//
	SPCALLBACK_PARAM m_Callback;

	///
	/// Magic Number for phase check
	///
	DWORD m_dwMagicNumber;

	///
	/// AYSN response receiving
	/// 简化处理用m_bStartAysnRecving替代临界锁
	///
	volatile BOOL m_bStartAysnRecving;
	HANDLE        m_hWakeUpEvent;
	CRecvPkgsList m_recvPkgList;
	HANDLE        m_hDumpStopEvent;
	///
	/// Observers
	///
	enum OBS_TYPE
	{
		OBS_SYSTEM = 0,
		OBS_FILE,
		OBS_SOCKET,
		OBS_MAX
	};
	///
	struct OBSERVER_T
	{
		int id;
		IProtocolObserver* obs;
		OBSERVER_T(void)
		{
			id = -1;
			obs = NULL;
		}
	} m_obsList[OBS_MAX];

	///
	/// Monitor
	///
	IDevMonitor* m_lpMonitor;

	/// 
	CSimpleLock  m_Lock;

	// 
	SOCKET_SETTING m_SocketSetting;

	CCallBackMonitor m_CallBackMonitor;

	//Bug 1732369 GE3F内存小，CNR的计算放到工具侧	
	BOOL    m_bStartModuleCNRRecving;
};

//////////////////////////////////////////////////////////////////////////
///
inline ICommChannel* CDiagBase::GetLowChannel(BOOL bProtocol /* = FALSE */)const
{
	ICommChannel* lpChannel = NULL;
	m_lpDiagChannel->GetLowerChannel(&lpChannel, bProtocol ? true : false);
	return lpChannel;
}

inline void CDiagBase::Clear(void)
{
	m_lpDiagChannel->Clear();
}

inline void CDiagBase::StartAysnRecving(BOOL bStart)
{
	m_bStartAysnRecving = bStart;
}

inline void CDiagBase::WakeUpOnResponse(void)
{
	m_bStartAysnRecving = FALSE;
	SetEvent(m_hWakeUpEvent);
}

inline BOOL CDiagBase::WaitForMultipleEvent( DWORD dwTimeout )
{
    const HANDLE arrHandles[] = { m_hWakeUpEvent, m_hDumpStopEvent };
    DWORD dwCode = WaitForMultipleObjects( ARRAY_SIZE( arrHandles ), arrHandles, FALSE, dwTimeout );
    switch ( dwCode )
    {
    case WAIT_OBJECT_0:
        ResetEvent( m_hWakeUpEvent );
        return TRUE;
    case WAIT_OBJECT_0 + 1:
		LogFmtStrA(SPLOGLV_ERROR, "WaitForMultipleEvent DumpStop.");
        ResetEvent( m_hDumpStopEvent );
		break;
    case WAIT_TIMEOUT:
        LogFmtStrA( SPLOGLV_ERROR, "WaitForMultipleEvent Timeout." );
        break;
    }
    return FALSE;
}

//inline BOOL CDiagBase::WaitForResponse(DWORD dwTimeOut)
//{
//	if (WAIT_OBJECT_0 == WaitForSingleObject(m_hWakeUpEvent, dwTimeOut))
//	{
//		ResetEvent(m_hWakeUpEvent);
//		return TRUE;
//	}
//	else
//	{
//		return FALSE;
//	}
//}

#define MSG_TIME_OUT 0xD3
#define MSG_UNKNOWN_STRUCT  0xD8
inline BOOL CDiagBase::CompareCond(const DIAG_HEADER * lpCond1, const DIAG_HEADER * lpCond2)
{
	if (NULL == lpCond1 || NULL == lpCond2)
	{
		return FALSE;
	}
	else if (lpCond2->type == RM_BBAT)
	{
		return TRUE;
	}
	if (SPECIAL_SN == lpCond1->sn || SPECIAL_SN == lpCond2->sn)
	{
		return (lpCond1->type == lpCond2->type && lpCond1->subtype == lpCond2->subtype);
	}
	else if (RM_COMMAND_T == lpCond1->type || RM_COMMAND_T == lpCond2->type)
	{
		// 
		if (RM_SPECIAL_SUBTYPE == lpCond1->subtype || RM_SPECIAL_SUBTYPE == lpCond2->subtype)
		{
			// Bug1013478: 
			// PC --> DUT: 7E 00 00 00 00 00 08 FE 80|X 7E
			// PC <-- DUT: 7E 00 00 00 00 00 08 FE FF   7E  
			// If FE FF is received, that means DUT is under specified test mode, so we cannot check subtype here.
			return (lpCond1->type == lpCond2->type && lpCond1->sn == lpCond2->sn);
		}
		else
		{
			return (lpCond1->type == lpCond2->type && lpCond1->subtype == lpCond2->subtype && lpCond1->sn == lpCond2->sn);
		}
	}
	else if ((MSG_TIME_OUT <= lpCond1->type && MSG_UNKNOWN_STRUCT >= lpCond1->type)
		|| (MSG_TIME_OUT <= lpCond2->type && MSG_UNKNOWN_STRUCT >= lpCond2->type)
		)
	{
		// Type between MSG_TIME_OUT and MSG_UNKNOWN_STRUCT means this package is contain
		// a error code for previous request,it use the same sn with the request package
		return lpCond1->sn == lpCond2->sn;
	}
	else
	{
		//write sn++
		return (lpCond1->sn == lpCond2->sn && lpCond1->type == lpCond2->type && lpCond1->subtype == lpCond2->subtype);
	}
}
