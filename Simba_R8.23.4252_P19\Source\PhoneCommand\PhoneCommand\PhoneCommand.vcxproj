﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{20DBA394-1B23-4226-8B02-A3E26D8455A0}</ProjectGuid>
    <RootNamespace>PhoneCommand</RootNamespace>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.17763.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.30319.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">..\..\..\Bin\App\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">$(Configuration)\</IntDir>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">true</LinkIncremental>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">..\..\..\Bin\App\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">$(Configuration)\</IntDir>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">false</LinkIncremental>
    <CodeAnalysisRuleSet Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">AllRules.ruleset</CodeAnalysisRuleSet>
    <CodeAnalysisRules Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" />
    <CodeAnalysisRuleAssemblies Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" />
    <CodeAnalysisRuleSet Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">AllRules.ruleset</CodeAnalysisRuleSet>
    <CodeAnalysisRules Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" />
    <CodeAnalysisRuleAssemblies Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" />
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>$(ProjectDir);..\..\Common\Include\Security;..\..\Common\Include;..\..\Common\dlmalloc;..\..\Common\lock;..\..\Common\UtilFn;..\..\Common\Tr;..\..\Common\Thread;Include;..\..\Common\Include\device;..\Common\DiagDefine;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>WIN32;_DEBUG;_WINDOWS;_USRDLL;PHONECOMMAND_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MinimalRebuild>true</MinimalRebuild>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <StructMemberAlignment>Default</StructMemberAlignment>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level4</WarningLevel>
      <DebugInformationFormat>EditAndContinue</DebugInformationFormat>
    </ClCompile>
    <Link>
      <AdditionalDependencies>Security_Auth.lib;Channel9.lib;DiagChan9.lib;iSpLog.lib;PortCapture.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <OutputFile>$(OutDir)PhoneCommand.dll</OutputFile>
      <AdditionalLibraryDirectories>..\..\Common\Lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Windows</SubSystem>
      <ImportLibrary>..\..\Common\Lib\PhoneCommand.lib</ImportLibrary>
      <TargetMachine>MachineX86</TargetMachine>
      <ProgramDatabaseFile>..\..\..\Source\Common\pdb\$(TargetName).pdb</ProgramDatabaseFile>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <Optimization>MaxSpeed</Optimization>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <AdditionalIncludeDirectories>$(ProjectDir);..\..\Common\Include\Security;..\..\Common\Include;..\..\Common\dlmalloc;..\..\Common\lock;..\..\Common\UtilFn;..\..\Common\Tr;..\..\Common\Thread;Include;..\..\Common\Include\device;..\Common\DiagDefine;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>WIN32;NDEBUG;_WINDOWS;_USRDLL;PHONECOMMAND_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <StructMemberAlignment>4Bytes</StructMemberAlignment>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level4</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
    </ClCompile>
    <Link>
      <AdditionalDependencies>Security_Auth.lib;Channel9.lib;DiagChan9.lib;iSpLog.lib;PortCapture.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <OutputFile>$(OutDir)PhoneCommand.dll</OutputFile>
      <AdditionalLibraryDirectories>..\..\Common\Lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Windows</SubSystem>
      <OptimizeReferences>true</OptimizeReferences>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <ImportLibrary>..\..\Common\Lib\PhoneCommand.lib</ImportLibrary>
      <TargetMachine>MachineX86</TargetMachine>
      <ProgramDatabaseFile>..\..\..\Source\Common\pdb\$(TargetName).pdb</ProgramDatabaseFile>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="..\..\Common\UtilFn\gsmUtility.cpp" />
    <ClCompile Include="..\..\Common\UtilFn\LteUtility.cpp" />
    <ClCompile Include="..\..\Common\UtilFn\NrUtility.cpp" />
    <ClCompile Include="..\..\Common\UtilFn\RunMode.cpp" />
    <ClCompile Include="..\..\Common\UtilFn\TDUtility.cpp" />
    <ClCompile Include="..\..\Common\UtilFn\wcdmaUtility.cpp" />
    <ClCompile Include="ApiExport.cpp" />
    <ClCompile Include="AT.cpp" />
    <ClCompile Include="Audio.cpp" />
    <ClCompile Include="Calibration\C2K.cpp" />
    <ClCompile Include="Calibration\Common.cpp" />
    <ClCompile Include="Calibration\NR.cpp" />
    <ClCompile Include="Calibration\UIS8811.cpp" />
    <ClCompile Include="Calibration\UIS891x.cpp" />
    <ClCompile Include="ExtendCmd.cpp" />
    <ClCompile Include="IgnoreSprdVcomDrvInRegister.cpp" />
    <ClCompile Include="LoadPhaseCheckConfig.cpp" />
    <ClCompile Include="stdafx.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">Create</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="Calibration\DMR.cpp" />
    <ClCompile Include="Calibration\GSM.cpp" />
    <ClCompile Include="Calibration\LTE.cpp" />
    <ClCompile Include="Calibration\TD.cpp" />
    <ClCompile Include="Calibration\WCDMA.cpp" />
    <ClCompile Include="DiagBase.cpp" />
    <ClCompile Include="RecvPkgsList.cpp" />
    <ClCompile Include="UeAssert.cpp" />
    <ClCompile Include="AP.cpp" />
    <ClCompile Include="CommCmd.cpp" />
    <ClCompile Include="DevMode.cpp" />
    <ClCompile Include="PhoneManager.cpp" />
    <ClCompile Include="..\..\Common\UtilFn\LogFile.cpp" />
    <ClCompile Include="AppSettings.cpp" />
    <ClCompile Include="DevHound\DevHound.cpp" />
    <ClCompile Include="..\..\Common\Tr\Tr.cpp" />
    <ClCompile Include="..\..\Common\dlmalloc\dlmalloc.c">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
      </PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
      </PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="CRC\crc16.c">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
      </PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
      </PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="..\..\Common\thread\Thread.cpp" />
    <ClCompile Include="..\..\Common\UtilFn\AppVer.cpp" />
    <ClCompile Include="..\..\Common\lock\CLocks.cpp" />
    <ClCompile Include="..\..\Common\lock\CSimpleLock.cpp" />
    <ClCompile Include="EndianConvert.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\..\Common\Include\PhoneCommand.h" />
    <ClInclude Include="..\..\Common\Include\Phonecommand_NR.h" />
    <ClInclude Include="..\..\Common\Include\Phonecommand_NVM.h" />
    <ClInclude Include="..\..\Common\Include\PortCapture.h" />
    <ClInclude Include="..\..\Common\UtilFn\gsmUtility.h" />
    <ClInclude Include="..\..\Common\UtilFn\LteUtility.h" />
    <ClInclude Include="..\..\Common\UtilFn\NrUtility.h" />
    <ClInclude Include="..\..\Common\UtilFn\RunMode.h" />
    <ClInclude Include="..\..\Common\UtilFn\TDUtility.h" />
    <ClInclude Include="..\..\Common\UtilFn\wcdmaUtility.h" />
    <ClInclude Include="..\Common\DiagDefine\C2kDef.h" />
    <ClInclude Include="..\Common\DiagDefine\CommonDef.h" />
    <ClInclude Include="..\Common\DiagDefine\GsmDef.h" />
    <ClInclude Include="LoadPhaseCheckConfig.h" />
    <ClInclude Include="..\Common\DiagDefine\LteDef.h" />
    <ClInclude Include="..\Common\DiagDefine\NrDef.h" />
    <ClInclude Include="resource.h" />
    <ClInclude Include="stdafx.h" />
    <ClInclude Include="targetver.h" />
    <ClInclude Include="Calibration\CaliCmd.h" />
    <ClInclude Include="DiagBase.h" />
    <ClInclude Include="RecvPkgsList.h" />
    <ClInclude Include="..\Common\DiagDefine\TdDef.h" />
    <ClInclude Include="UeAssert.h" />
    <ClInclude Include="CommCmd.h" />
    <ClInclude Include="DevMode.h" />
    <ClInclude Include="DiagPhone.h" />
    <ClInclude Include="PhoneManager.h" />
    <ClInclude Include="Include\FileOBS.h" />
    <ClInclude Include="..\..\Common\UtilFn\LogFile.h" />
    <ClInclude Include="Include\SocketOBS.h" />
    <ClInclude Include="..\Common\DiagDefine\diagdef.h" />
    <ClInclude Include="..\..\Common\Include\global_def.h" />
    <ClInclude Include="..\..\Common\Include\global_err.h" />
    <ClInclude Include="AppSettings.h" />
    <ClInclude Include="DevHound\DevHound.h" />
    <ClInclude Include="..\..\Common\Tr\Tr.h" />
    <ClInclude Include="..\..\Common\dlmalloc\dlmalloc.h" />
    <ClInclude Include="CRC\crc16.h" />
    <ClInclude Include="..\..\Common\thread\Thread.h" />
    <ClInclude Include="..\..\Common\UtilFn\AppVer.h" />
    <ClInclude Include="..\..\Common\lock\CLocks.h" />
    <ClInclude Include="..\..\Common\lock\CSimpleLock.h" />
    <ClInclude Include="EndianConvert.h" />
    <ClInclude Include="..\Common\DiagDefine\WcdmaDef.h" />
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="PhoneCommand.rc" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>