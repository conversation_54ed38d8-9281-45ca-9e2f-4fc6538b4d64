#include"StdAfx.h"
#include"CaliCmd.h"


SPRESULT CCaliCmd::AiotRfDebugAfc(const AIOT_DIAG_AFC_REQ_T* Param)
{
	if (NULL == Param)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid pointer!", __FUNCTION__);
		return SP_E_POINTER;
	}
	LogFmtStrA(SPLOGLV_INFO, __FUNCTION__);
	DeclareDiagHeader(hd, DIAG_LTE_RF, 0x0C);
	//SPRESULT res = SendCmd(hd, (const void*)Param, sizeof(AIOT_DIAG_FLAG_REQ_T), m_dwTimeOut);
	UINT32 RecvLen;
	SPRESULT res = SendAndRecv(hd, (const void*)Param, sizeof(AIOT_DIAG_AFC_REQ_T), hd, (void*)m_diagBuff, sizeof(m_diagBuff), &RecvLen, m_dwTimeOut);
	if (SP_OK != res) {
		return res;
	}
	return SP_OK;
}
SPRESULT CCaliCmd::AiotRfDebugTx(const AIOT_DIAG_TX_REQ_T* Param)
{
	if (NULL == Param)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid pointer!", __FUNCTION__);
		return SP_E_POINTER;
	}
	LogFmtStrA(SPLOGLV_INFO, __FUNCTION__);
	DeclareDiagHeader(hd, DIAG_LTE_RF, 0x05);
	//SPRESULT res = SendCmd(hd, (const void*)Param, sizeof(AIOT_DIAG_FLAG_REQ_T), m_dwTimeOut);
	UINT32 RecvLen;
	SPRESULT res = SendAndRecv(hd, (const void*)Param, sizeof(AIOT_DIAG_TX_REQ_T), hd, (void*)m_diagBuff, sizeof(m_diagBuff), &RecvLen, m_dwTimeOut);
	if (SP_OK != res) {
		return res;
	}
	return SP_OK;
}
SPRESULT CCaliCmd::AiotRfDebugRx(const AIOT_DIAG_RX_REQ_T* Param)
{
	if (NULL == Param)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid pointer!", __FUNCTION__);
		return SP_E_POINTER;
	}
	LogFmtStrA(SPLOGLV_INFO, __FUNCTION__);
	DeclareDiagHeader(hd, DIAG_LTE_RF, 0x06);
	//SPRESULT res = SendCmd(hd, (const void*)Param, sizeof(AIOT_DIAG_FLAG_REQ_T), m_dwTimeOut);
	UINT32 RecvLen;
	SPRESULT res = SendAndRecv(hd, (const void*)Param, sizeof(AIOT_DIAG_RX_REQ_T), hd, (void*)m_diagBuff, sizeof(m_diagBuff), &RecvLen, m_dwTimeOut);
	if (SP_OK != res) {
		return res;
	}
	return SP_OK;
}

SPRESULT CCaliCmd::AiotRfDebugTxOff(const UINT8 Stub_Cmd)
{
	if (NULL == Stub_Cmd)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid pointer!", __FUNCTION__);
		return SP_E_POINTER;
	}
	LogFmtStrA(SPLOGLV_INFO, __FUNCTION__);
	DeclareDiagHeader(hd, DIAG_LTE_RF, Stub_Cmd);
	//SPRESULT res = SendCmd(hd, NULL, NULL, m_dwTimeOut);
	UINT32 RecvLen;
	SPRESULT res = SendAndRecv(hd, NULL, NULL, hd, (void*)m_diagBuff, sizeof(m_diagBuff), &RecvLen, m_dwTimeOut);
	if (SP_OK != res) {
		return res;
	}
	return SP_OK;
}
SPRESULT CCaliCmd::AiotRfDebugRxOff(const UINT8 Stub_Cmd)
{
	if (NULL == Stub_Cmd)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid pointer!", __FUNCTION__);
		return SP_E_POINTER;
	}
	LogFmtStrA(SPLOGLV_INFO, __FUNCTION__);
	DeclareDiagHeader(hd, DIAG_LTE_RF, Stub_Cmd);
	//SPRESULT res = SendCmd(hd, NULL, NULL, m_dwTimeOut);
	UINT32 RecvLen;
	SPRESULT res = SendAndRecv(hd, NULL, NULL, hd, (void*)m_diagBuff, sizeof(m_diagBuff), &RecvLen, m_dwTimeOut);
	if (SP_OK != res) {
		return res;
	}
	return SP_OK;
}


SPRESULT CCaliCmd::AiotRfReadNV(const UINT8 Stub_Cmd)
{
	if (NULL == Stub_Cmd)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid pointer!", __FUNCTION__);
		return SP_E_POINTER;
	}
	LogFmtStrA(SPLOGLV_INFO, __FUNCTION__);
	DeclareDiagHeader(hd, DIAG_LTE_RF, Stub_Cmd);
	//SPRESULT res = SendCmd(hd, NULL, NULL, m_dwTimeOut);
	UINT32 RecvLen;
	SPRESULT res = SendAndRecv(hd, NULL, NULL, hd,(void*)m_diagBuff,sizeof(m_diagBuff), &RecvLen,m_dwTimeOut);
	if (SP_OK != res) {
		return res; }
	return SP_OK;
}

SPRESULT CCaliCmd::AiotRfStatusClear(const UINT8 Stub_Cmd)
{
	if (NULL == Stub_Cmd)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid pointer!", __FUNCTION__);
		return SP_E_POINTER;
	}
	LogFmtStrA(SPLOGLV_INFO, __FUNCTION__);
	DeclareDiagHeader(hd, DIAG_LTE_RF, Stub_Cmd);
	//SPRESULT res = SendCmd(hd, NULL, NULL, m_dwTimeOut);
	UINT32 RecvLen;
	SPRESULT res = SendAndRecv(hd, NULL, NULL, hd, (void*)m_diagBuff, sizeof(m_diagBuff), &RecvLen, m_dwTimeOut);
	if (SP_OK != res) {
		return res;
	}
	return SP_OK;
}

SPRESULT CCaliCmd::AiotRfWriteNV(const UINT8 Stub_Cmd)
{
	if (NULL == Stub_Cmd)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid pointer!", __FUNCTION__);
		return SP_E_POINTER;
	}
	LogFmtStrA(SPLOGLV_INFO, __FUNCTION__);

	DeclareDiagHeader(hd, DIAG_LTE_RF, Stub_Cmd);
	//SPRESULT res = SendCmd(hd, NULL, NULL, m_dwTimeOut);
	UINT32 RecvLen;
	SPRESULT res = SendAndRecv(hd, NULL, NULL, hd, (void*)m_diagBuff, sizeof(m_diagBuff), &RecvLen, m_dwTimeOut);
	if (SP_OK != res) {
		return res; }
	return SP_OK;
}
SPRESULT CCaliCmd::AiotSetCalibFlg(const UINT8 Stub_Cmd, const AIOT_DIAG_FLAG_REQ_T* Param)
{
	if (NULL == Stub_Cmd)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid pointer!", __FUNCTION__);
		return SP_E_POINTER;
	}
	LogFmtStrA(SPLOGLV_INFO, __FUNCTION__);
	DeclareDiagHeader(hd, DIAG_LTE_RF, Stub_Cmd);
	//SPRESULT res = SendCmd(hd, (const void*)Param, sizeof(AIOT_DIAG_FLAG_REQ_T), m_dwTimeOut);
	UINT32 RecvLen;
	SPRESULT res = SendAndRecv(hd, (const void*)Param, sizeof(AIOT_DIAG_FLAG_REQ_T), hd, (void*)m_diagBuff, sizeof(m_diagBuff), &RecvLen, m_dwTimeOut);
	if (SP_OK != res) {
		return res;
	}
	return SP_OK;
}
SPRESULT CCaliCmd::AiotRfCalNumber(const INT UeType, const UINT8 Stub_Cmd, AIOT_DIAG_CALIB_NUM_REQ_V2_T* pParam)
{
    UINT32 SendLen = sizeof(AIOT_DIAG_CALIB_NUM_REQ_V2_T);
    void* pSendParam = pParam;

	if (NULL == Stub_Cmd)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid pointer!", __FUNCTION__);
		return SP_E_POINTER;
	}
    LogFmtStrA(SPLOGLV_INFO, __FUNCTION__"0x%x", UeType);
	DeclareDiagHeader(hd, DIAG_LTE_RF, Stub_Cmd);

    if (0x8811 == UeType)
    {
        AIOT_DIAG_CALIB_NUM_REQ_T TempParam;
        SendLen = sizeof(AIOT_DIAG_CALIB_NUM_REQ_T);
        TempParam.Band = (UINT8)pParam->Band;
        TempParam.Flag = pParam->Flag;
        TempParam.Number = pParam->Number;
        pSendParam = &TempParam;
    }

	UINT32 RecvLen;
    SPRESULT res = SendAndRecv(hd, (const void*)pSendParam, SendLen, hd, (void*)m_diagBuff, sizeof(m_diagBuff), &RecvLen, m_dwTimeOut);
	if (SP_OK != res) {
		return res;
	}
	return SP_OK;
}

SPRESULT CCaliCmd::AiotRfCalTx(const INT UeType, const UINT8 Stub_Cmd, AIOT_DIAG_TX_REQ_V2_T* pParam)
{
    UINT32 SendLen = sizeof(AIOT_DIAG_TX_REQ_V2_T);
    void* pSendParam = pParam;

	if (NULL == Stub_Cmd)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid pointer!", __FUNCTION__);
		return SP_E_POINTER;
	}
    LogFmtStrA(SPLOGLV_INFO, __FUNCTION__"0x%x", UeType);
	DeclareDiagHeader(hd, DIAG_LTE_RF, Stub_Cmd);

    if (0x8811 == UeType)
    {
        AIOT_DIAG_TX_REQ_T TempParam;
        SendLen = sizeof(AIOT_DIAG_TX_REQ_T);
        TempParam.Channel = pParam->Channel;
        TempParam.McsIndex = pParam->McsIndex;
        TempParam.Power = pParam->Power;
        TempParam.Band = (UINT8)pParam->Band;
        TempParam.PAmode = pParam->PAmode;
        TempParam.Again = pParam->Again;  //A Gain index
        TempParam.Dgain = pParam->Dgain;  //D Gain index
        TempParam.SubcarrierSpacing = pParam->SubcarrierSpacing;
        TempParam.SubCarrierNumber = pParam->SubCarrierNumber;
        TempParam.SubCarrierPosition = pParam->SubCarrierPosition;
        TempParam.Modulation = pParam->Modulation;//QPSK/BPSK
        TempParam.Poffset = pParam->Poffset;
        pSendParam = &TempParam;
    }

	//SPRESULT res = SendCmd(hd, (const void*)Param, sizeof(AIOT_DIAG_TX_REQ_T), m_dwTimeOut);
	UINT32 RecvLen;
    SPRESULT res = SendAndRecv(hd, (const void*)pSendParam, SendLen, hd, (void*)m_diagBuff, sizeof(m_diagBuff), &RecvLen, m_dwTimeOut);
	if (SP_OK != res) {
		return res;
	}
	return SP_OK;
}

SPRESULT CCaliCmd::AiotRfCalTxSave(const INT UeType, const UINT8 Stub_Cmd, AIOT_DIAG_TX_CALIB_DATA_REQ_V2_T* pParam)
{
    UINT32 SendLen = sizeof(AIOT_DIAG_TX_CALIB_DATA_REQ_V2_T);
    void* pSendParam = pParam;

	if (NULL == Stub_Cmd)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid pointer!", __FUNCTION__);
		return SP_E_POINTER;
	}
    LogFmtStrA(SPLOGLV_INFO, __FUNCTION__"0x%x", UeType);
	DeclareDiagHeader(hd, DIAG_LTE_RF, Stub_Cmd);

    if (0x8811 == UeType)
    {
        AIOT_DIAG_TX_CALIB_DATA_REQ_T TempParam;
        SendLen = sizeof(AIOT_DIAG_TX_CALIB_DATA_REQ_T);
        TempParam.Band = (UINT8)pParam->Band;
        TempParam.MeasPow = pParam->MeasPow;
        TempParam.TxFlatFlag = pParam->TxFlatFlag;
        TempParam.reserved[0] = pParam->reserved[0];
        TempParam.reserved[1] = pParam->reserved[1];
        TempParam.reserved[2] = pParam->reserved[2];
        TempParam.reserved[3] = pParam->reserved[3];
        pSendParam = &TempParam;
    }

	//SPRESULT res = SendCmd(hd, (const void*)Param, sizeof(AIOT_DIAG_TX_CALIB_DATA_REQ_T), m_dwTimeOut);
	UINT32 RecvLen;
    SPRESULT res = SendAndRecv(hd, (const void*)pSendParam, SendLen, hd, (void*)m_diagBuff, sizeof(m_diagBuff), &RecvLen, m_dwTimeOut);
	if (SP_OK != res) {
		return res;
	}
	return SP_OK;
}
SPRESULT CCaliCmd::AiotRfCalRx(const INT UeType, const UINT8 Stub_Cmd, AIOT_DIAG_RX_REQ_V2_T* pParam)
{
    UINT32 SendLen = sizeof(AIOT_DIAG_RX_REQ_V2_T);
    void* pSendParam = pParam;

	if (NULL == Stub_Cmd)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid pointer!", __FUNCTION__);
		return SP_E_POINTER;
	}
    LogFmtStrA(SPLOGLV_INFO, __FUNCTION__"0x%x", UeType);
	DeclareDiagHeader(hd, DIAG_LTE_RF, Stub_Cmd);

    if (0x8811 == UeType)
    {
        AIOT_DIAG_RX_REQ_T TempParam;
        SendLen = sizeof(AIOT_DIAG_RX_REQ_T);
        TempParam.Channel = pParam->Channel;
        TempParam.CellPow = pParam->CellPow;
        TempParam.CellId = pParam->CellId;
        TempParam.Band = (UINT8)pParam->Band;
        TempParam.Again = pParam->Again;  //A Gain index
        TempParam.Dgain = pParam->Dgain;  //D Gain index
        pSendParam = &TempParam;
    }

	//SPRESULT res = SendCmd(hd, (const void*)Param, sizeof(AIOT_DIAG_RX_REQ_T), m_dwTimeOut);
	UINT32 RecvLen;
    SPRESULT res = SendAndRecv(hd, (const void*)pSendParam, SendLen, hd, (void*)m_diagBuff, sizeof(m_diagBuff), &RecvLen, m_dwTimeOut);
	if (SP_OK != res) {
		return res;
	}
	return SP_OK;
}
SPRESULT CCaliCmd::AiotRfCalRxSave(const INT UeType, const UINT8 Stub_Cmd, AIOT_DIAG_RX_CALIB_DATA_REQ_V2_T* pParam)
{
    UINT32 SendLen = sizeof(AIOT_DIAG_RX_CALIB_DATA_REQ_V2_T);
    void* pSendParam = pParam;

	if (NULL == Stub_Cmd)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid pointer!", __FUNCTION__);
		return SP_E_POINTER;
	}
	LogFmtStrA(SPLOGLV_INFO, __FUNCTION__);
	DeclareDiagHeader(hd, DIAG_LTE_RF, Stub_Cmd);

    if (0x8811 == UeType)
    {
        AIOT_DIAG_RX_CALIB_DATA_REQ_T TempParam;
        SendLen = sizeof(AIOT_DIAG_RX_CALIB_DATA_REQ_T);
        TempParam.Band = (UINT8)pParam->Band;
        TempParam.RxFlatFlag = pParam->RxFlatFlag;
        TempParam.RxOffset = pParam->RxOffset;
        TempParam.reserved[0] = pParam->reserved[0];
        TempParam.reserved[1] = pParam->reserved[1];
        pSendParam = &TempParam;
    }

	//SPRESULT res = SendCmd(hd, (const void*)Param, sizeof(AIOT_DIAG_RX_CALIB_DATA_REQ_T), m_dwTimeOut);
	UINT32 RecvLen;
    SPRESULT res = SendAndRecv(hd, (const void*)pSendParam, SendLen, hd, (void*)m_diagBuff, sizeof(m_diagBuff), &RecvLen, m_dwTimeOut);
	if (SP_OK != res) {
		return res;
	}
	return SP_OK;
}
SPRESULT CCaliCmd::AiotRfCalFreq(const INT UeType, const UINT8 Stub_Cmd, AIOT_DIAG_AFC_REQ_V2_T* pParam)
{
    UINT32 SendLen = sizeof(AIOT_DIAG_AFC_REQ_V2_T);
    void* pSendParam = pParam;

	if (NULL == Stub_Cmd)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid pointer!", __FUNCTION__);
		return SP_E_POINTER;
	}
LogFmtStrA(SPLOGLV_INFO, __FUNCTION__"0x%x",UeType);
	DeclareDiagHeader(hd, DIAG_LTE_RF, Stub_Cmd);

    if (0x8811 == UeType)
    {
        AIOT_DIAG_AFC_REQ_T TempParam;
        SendLen = sizeof(AIOT_DIAG_AFC_REQ_T);
        TempParam.Channel = pParam->Channel;
        TempParam.Bound = pParam->Bound;
        TempParam.McsIndex = pParam->McsIndex;
        TempParam.Power = pParam->Power;
        TempParam.Band = (UINT8)pParam->Band;
        TempParam.PAmode = pParam->PAmode;
        TempParam.Again = pParam->Again;  //A Gain index
        TempParam.Dgain = pParam->Dgain;  //D Gain index
        TempParam.SubcarrierSpacing = pParam->SubcarrierSpacing;
        TempParam.SubCarrierNumber = pParam->SubCarrierNumber;
        TempParam.SubCarrierPosition = pParam->SubCarrierPosition;
        TempParam.Modulation = pParam->Modulation;//QPSK/BPSK
        pSendParam = &TempParam;
    }

	//SPRESULT res = SendCmd(hd, (const void*)Param, sizeof(AIOT_DIAG_AFC_REQ_T), m_dwTimeOut);
	UINT32 RecvLen;
    SPRESULT res = SendAndRecv(hd, (const void*)pSendParam, SendLen, hd, (void*)m_diagBuff, sizeof(m_diagBuff), &RecvLen, m_dwTimeOut);
	if (SP_OK != res) {
		return res;
	}
	return SP_OK;
}
SPRESULT CCaliCmd::AiotRfCalFreqSave(const UINT8 Stub_Cmd, const AIOT_DIAG_AFC_CALIB_DATA_REQ_T* Param)
{
	if (NULL == Stub_Cmd)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid pointer!", __FUNCTION__);
		return SP_E_POINTER;
	}
	LogFmtStrA(SPLOGLV_INFO, __FUNCTION__);
	DeclareDiagHeader(hd, DIAG_LTE_RF, Stub_Cmd);
	//SPRESULT res = SendCmd(hd, (const void*)Param, sizeof(AIOT_DIAG_AFC_CALIB_DATA_REQ_T), m_dwTimeOut);
	UINT32 RecvLen;
	SPRESULT res = SendAndRecv(hd, (const void*)Param, sizeof(AIOT_DIAG_AFC_CALIB_DATA_REQ_T), hd, (void*)m_diagBuff, sizeof(m_diagBuff), &RecvLen, m_dwTimeOut);
	if (SP_OK != res) {
		return res;
	}
	return SP_OK;
}

SPRESULT CCaliCmd::AiotRfCheckCrc(const UINT8 Stub_Cmd, UINT32& CrcFlag)
{
	if (NULL == Stub_Cmd)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid pointer!", __FUNCTION__);
		return SP_E_POINTER;
	}
	LogFmtStrA(SPLOGLV_INFO, __FUNCTION__);
	DeclareDiagHeader(hd, DIAG_LTE_RF, Stub_Cmd);
	UINT32 RecvLen;
	SPRESULT res = SendAndRecv(hd, NULL, NULL, hd, (void*)m_diagBuff, sizeof(m_diagBuff), &RecvLen, m_dwTimeOut);
	if (SP_OK != res) {
		return res;
	}
	AIOT_DIAG_CHECK_CRC_IND_T* Flag = (AIOT_DIAG_CHECK_CRC_IND_T*)m_diagBuff;
	CrcFlag= Flag->CrcFlag;
	return SP_OK;
}

SPRESULT CCaliCmd::AiotRfTxStop(const UINT8 Stub_Cmd, UINT8& TxStopRsp)
{
	if (NULL == Stub_Cmd)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid pointer!", __FUNCTION__);
		return SP_E_POINTER;
	}
	LogFmtStrA(SPLOGLV_INFO, __FUNCTION__);
	DeclareDiagHeader(hd, DIAG_LTE_RF, Stub_Cmd);
	UINT32 RecvLen;
	SPRESULT res = SendAndRecv(hd, NULL, NULL, hd, (void*)m_diagBuff, sizeof(m_diagBuff), &RecvLen, m_dwTimeOut);
	if (SP_OK != res) {
		return res;
	}
	AIOT_DIAG_TX_STOP_IND_T* Stop = (AIOT_DIAG_TX_STOP_IND_T*)m_diagBuff;
	TxStopRsp = Stop->TxStopRsp;
	return SP_OK;
}

SPRESULT CCaliCmd::AiotFetchCalData(INT UeType, const UINT8 Stub_Cmd, AIOT_DIAG_FETCH_CAL_DATA_REQ_V2_T* pParam, AIOT_DIAG_FETCH_CAL_DATA_CNF_T* Fetch)
{
    UINT32 SendLen = sizeof(AIOT_DIAG_FETCH_CAL_DATA_REQ_V2_T);
    void* pSendParam = pParam;

	if (NULL == Stub_Cmd)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid pointer!", __FUNCTION__);
		return SP_E_POINTER;
	}
    LogFmtStrA(SPLOGLV_INFO, __FUNCTION__"0x%x", UeType);
	DeclareDiagHeader(hd, DIAG_LTE_RF, Stub_Cmd);

    if (0x8811 == UeType)
    {
        AIOT_DIAG_FETCH_CAL_DATA_REQ_T TempParam;
        SendLen = sizeof(AIOT_DIAG_FETCH_CAL_DATA_REQ_T);
        TempParam.Band = (UINT8)pParam->Band;
        TempParam.RtxFlag = pParam->RtxFlag;
        pSendParam = &TempParam;
    }

	UINT32 RecvLen;
    SPRESULT res = SendAndRecv(hd, (const void*)pSendParam, SendLen, hd, (void*)m_diagBuff, sizeof(m_diagBuff), &RecvLen, m_dwTimeOut);
	if (SP_OK != res) {
		return res;
	}
	AIOT_DIAG_FETCH_CAL_DATA_CNF_T* Cnf = (AIOT_DIAG_FETCH_CAL_DATA_CNF_T*)m_diagBuff;
	memcpy(Fetch, Cnf, sizeof(AIOT_DIAG_FETCH_CAL_DATA_CNF_T));
	return SP_OK;
	
}
SPRESULT CCaliCmd::AiotRfFtGetgBer(const UINT8 Stub_Cmd, AIOT_DIAG_NSFT_BER_REQ_T* Ber)
{
	if (NULL == Stub_Cmd)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid pointer!", __FUNCTION__);
		return SP_E_POINTER;
	}
	LogFmtStrA(SPLOGLV_INFO, __FUNCTION__);
	DeclareDiagHeader(hd, DIAG_LTE_RF, Stub_Cmd);
	UINT32 RecvLen;
	SPRESULT res = SendAndRecv(hd, NULL, NULL, hd, (void*)m_diagBuff, sizeof(m_diagBuff), &RecvLen, m_dwTimeOut);
	if (SP_OK != res) {
		return res;
	}

	AIOT_DIAG_NSFT_BER_REQ_T* Cnf = (AIOT_DIAG_NSFT_BER_REQ_T*)m_diagBuff;
	memcpy(Ber, Cnf, sizeof(AIOT_DIAG_NSFT_BER_REQ_T));
	return SP_OK;
}

SPRESULT CCaliCmd::AiotRfFtInitBer(const UINT8 Stub_Cmd, const AIOT_DIAG_INIT_BER_REQ_T* Ber)
{
	if (NULL == Stub_Cmd)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid pointer!", __FUNCTION__);
		return SP_E_POINTER;
	}
	LogFmtStrA(SPLOGLV_INFO, __FUNCTION__);
	DeclareDiagHeader(hd, DIAG_LTE_RF, Stub_Cmd);
	//SPRESULT res = SendCmd(hd, (const void*)Param, sizeof(AIOT_DIAG_AFC_CALIB_DATA_REQ_T), m_dwTimeOut);
	UINT32 RecvLen;
	SPRESULT res = SendAndRecv(hd, (const void*)Ber, sizeof(AIOT_DIAG_INIT_BER_REQ_T), hd, (void*)m_diagBuff, sizeof(m_diagBuff), &RecvLen, m_dwTimeOut);
	if (SP_OK != res) {
		return res;
	}
	return SP_OK;
}


SPRESULT CCaliCmd::AiotRfGoldenSamplSave(UINT8 Stub_Cmd, const AIOT_DIAG_GOLDEN_SAMPLE_REQ_T* Param)
{
	if (NULL == Stub_Cmd)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid pointer!", __FUNCTION__);
		return SP_E_POINTER;
	}
	LogFmtStrA(SPLOGLV_INFO, __FUNCTION__);
	DeclareDiagHeader(hd, DIAG_LTE_RF, Stub_Cmd);
	UINT32 RecvLen;
	SPRESULT res = SendAndRecv(hd, (const void*)Param, sizeof(AIOT_DIAG_GOLDEN_SAMPLE_REQ_T), hd, (void*)m_diagBuff, sizeof(m_diagBuff), &RecvLen, m_dwTimeOut);
	if (SP_OK != res) {
		return res;
	}
	return SP_OK;
}

SPRESULT CCaliCmd::AiotRfCalibLoss(UINT8 Stub_Cmd, AIOT_DIAG_CALIB_LOSS_REQ_T* Cnf)
{
	if (NULL == Stub_Cmd)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid pointer!", __FUNCTION__);
		return SP_E_POINTER;
	}
	LogFmtStrA(SPLOGLV_INFO, __FUNCTION__);
	DeclareDiagHeader(hd, DIAG_LTE_RF, Stub_Cmd);
	UINT32 RecvLen;
	SPRESULT res = SendAndRecv(hd, NULL, NULL, hd, (void*)m_diagBuff, sizeof(m_diagBuff), &RecvLen, m_dwTimeOut);
	if (SP_OK != res) {
		return res;
	}

	AIOT_DIAG_CALIB_LOSS_REQ_T* Fetch = (AIOT_DIAG_CALIB_LOSS_REQ_T*)m_diagBuff;
	memcpy(Cnf, Fetch, sizeof(AIOT_DIAG_CALIB_LOSS_REQ_T));
	return SP_OK;
}



SPRESULT CCaliCmd::AiotRfCalGetRssi(const UINT8 Stub_Cmd, const AIOT_DIAG_CALIB_GETRSSI_REQ_T* Req, AIOT_DIAG_CALIB_GETRSSI_CNF_T* Cnf)
{
	if (NULL == Stub_Cmd)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid pointer!", __FUNCTION__);
		return SP_E_POINTER;
	}
	LogFmtStrA(SPLOGLV_INFO, __FUNCTION__);
	DeclareDiagHeader(hd, DIAG_LTE_RF, Stub_Cmd);
	UINT32 RecvLen;
	SPRESULT res = SendAndRecv(hd, (void*)Req, sizeof(AIOT_DIAG_CALIB_GETRSSI_REQ_T), hd, (void*)m_diagBuff, sizeof(m_diagBuff), &RecvLen, m_dwTimeOut);
	if (SP_OK != res) {
		return res;
	}

	AIOT_DIAG_CALIB_GETRSSI_CNF_T* Buff = (AIOT_DIAG_CALIB_GETRSSI_CNF_T*)m_diagBuff;
	memcpy(Cnf, Buff, sizeof(AIOT_DIAG_CALIB_GETRSSI_CNF_T));
	return SP_OK;
}

SPRESULT CCaliCmd::AiotRfFtGetgSyncFlag(const UINT8 Stub_Cmd, AIOT_DIAG_NSFT_FLAG_REQ_T* Flag)
{
	if (NULL == Stub_Cmd)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid pointer!", __FUNCTION__);
		return SP_E_POINTER;
	}
	LogFmtStrA(SPLOGLV_INFO, __FUNCTION__);
	DeclareDiagHeader(hd, DIAG_LTE_RF, Stub_Cmd);
	UINT32 RecvLen;
	SPRESULT res = SendAndRecv(hd, NULL, NULL, hd, (void*)m_diagBuff, sizeof(m_diagBuff), &RecvLen, m_dwTimeOut);
	if (SP_OK != res) {
		return res;
	}

	AIOT_DIAG_NSFT_FLAG_REQ_T* Cnf = (AIOT_DIAG_NSFT_FLAG_REQ_T*)m_diagBuff;
	memcpy(Flag, Cnf, sizeof(AIOT_DIAG_NSFT_FLAG_REQ_T));
	return SP_OK;
}
SPRESULT CCaliCmd::AiotRfFtSetSyncParam(const INT UeType, const UINT8 Stub_Cmd, AIOT_DIAG_NSFT_FT_SETUP_REQ_V2_T* pParam)
{
    UINT32 SendLen = sizeof(AIOT_DIAG_NSFT_FT_SETUP_REQ_V2_T);
    void* pSendParam = pParam;

	if (NULL == Stub_Cmd)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid pointer!", __FUNCTION__);
		return SP_E_POINTER;
	}
    LogFmtStrA(SPLOGLV_INFO, __FUNCTION__"0x%x", UeType);
	DeclareDiagHeader(hd, DIAG_LTE_RF, Stub_Cmd);

    if (0x8811 == UeType)
    {
        AIOT_DIAG_NSFT_FT_SETUP_REQ_T TempParam;
        SendLen = sizeof(AIOT_DIAG_NSFT_FT_SETUP_REQ_T);
        TempParam.DL.Band = (UINT8)pParam->DL.Band;
        TempParam.DL.AntPortNum = pParam->DL.AntPortNum;
        TempParam.DL.Ran88ScrChqEn = pParam->DL.Ran88ScrChqEn;
        TempParam.DL.RxChannel = pParam->DL.RxChannel;
        TempParam.DL.TxPow = pParam->DL.TxPow;
        TempParam.DL.CellPow = pParam->DL.CellPow;
        TempParam.DL.CellId = pParam->DL.CellId;
        TempParam.DL.Crnti = pParam->DL.Crnti;
        TempParam.DL.ExpectPdschNum = pParam->DL.ExpectPdschNum;
        TempParam.UL.TxChannel = pParam->UL.TxChannel;
        TempParam.UL.TxPow = pParam->UL.TxPow;
        TempParam.UL.TxBand = (UINT8)pParam->UL.TxBand;
        TempParam.UDLFlag = pParam->UDLFlag;
        pSendParam = &TempParam;
    }

	//SPRESULT res = SendCmd(hd, (const void*)Param, sizeof(AIOT_DIAG_TX_REQ_T), m_dwTimeOut);
	UINT32 RecvLen;
    SPRESULT res = SendAndRecv(hd, (const void*)pSendParam, SendLen, hd, (void*)m_diagBuff, sizeof(m_diagBuff), &RecvLen, m_dwTimeOut);
	if (SP_OK != res) {
		return res;
	}
	return SP_OK;
}


SPRESULT CCaliCmd::AiotRfFtTx(const INT UeType, const UINT8 Stub_Cmd, AIOT_DIAG_TX_REQ_V2_T* pParam)
{
    UINT32 SendLen = sizeof(AIOT_DIAG_TX_REQ_V2_T);
    void* pSendParam = pParam;

	if (NULL == Stub_Cmd)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid pointer!", __FUNCTION__);
		return SP_E_POINTER;
	}
    LogFmtStrA(SPLOGLV_INFO, __FUNCTION__"0x%x", UeType);
	DeclareDiagHeader(hd, DIAG_LTE_RF, Stub_Cmd);

    if (0x8811 == UeType)
    {
        AIOT_DIAG_TX_REQ_T TempParam;
        SendLen = sizeof(AIOT_DIAG_TX_REQ_T);
        TempParam.Channel = pParam->Channel;
        TempParam.McsIndex = pParam->McsIndex;
        TempParam.Power = pParam->Power;
        TempParam.Band = (UINT8)pParam->Band;
        TempParam.PAmode = pParam->PAmode;
        TempParam.Again = pParam->Again;  //A Gain index
        TempParam.Dgain = pParam->Dgain;  //D Gain index
        TempParam.SubcarrierSpacing = pParam->SubcarrierSpacing;
        TempParam.SubCarrierNumber = pParam->SubCarrierNumber;
        TempParam.SubCarrierPosition = pParam->SubCarrierPosition;
        TempParam.Modulation = pParam->Modulation;//QPSK/BPSK
        TempParam.Poffset = pParam->Poffset;
        pSendParam = &TempParam;
    }

	//SPRESULT res = SendCmd(hd, (const void*)Param, sizeof(AIOT_DIAG_TX_REQ_T), m_dwTimeOut);
	UINT32 RecvLen;
    SPRESULT res = SendAndRecv(hd, (const void*)pSendParam, SendLen, hd, (void*)m_diagBuff, sizeof(m_diagBuff), &RecvLen, m_dwTimeOut);
	if (SP_OK != res) {
		return res;
	}
	return SP_OK;
}

SPRESULT CCaliCmd::AiotRfReadCalFlag(const UINT8 Stub_Cmd, const AIOT_DIAG_CALIB_GETFLAG_REQ_T* Req, AIOT_DIAG_CALIB_GETFLAG_CNF_T* Cnf)
{
	if (NULL == Stub_Cmd)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid pointer!", __FUNCTION__);
		return SP_E_POINTER;
	}
	LogFmtStrA(SPLOGLV_INFO, __FUNCTION__);
	DeclareDiagHeader(hd, DIAG_LTE_RF, Stub_Cmd);
	UINT32 RecvLen;
	SPRESULT res = SendAndRecv(hd, (void*)Req, sizeof(AIOT_DIAG_CALIB_GETFLAG_REQ_T), hd, (void*)m_diagBuff, sizeof(m_diagBuff), &RecvLen, m_dwTimeOut);
	if (SP_OK != res) {
		return res;
	}

	AIOT_DIAG_CALIB_GETFLAG_CNF_T* Buff = (AIOT_DIAG_CALIB_GETFLAG_CNF_T*)m_diagBuff;
	memcpy(Cnf, Buff, sizeof(AIOT_DIAG_CALIB_GETFLAG_CNF_T));
	return SP_OK;
}

SPRESULT CCaliCmd::AiotRfReadNV_V1(UINT8 Stub_Cmd, AIOT_DIAG_CALIG_BAND_INFO& band_info)
{
	if (NULL == Stub_Cmd)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid pointer!", __FUNCTION__);
		return SP_E_POINTER;
	}
	LogFmtStrA(SPLOGLV_INFO, __FUNCTION__);
	DeclareDiagHeader(hd, DIAG_LTE_RF, Stub_Cmd);
	//SPRESULT res = SendCmd(hd, NULL, NULL, m_dwTimeOut);
	UINT32 RecvLen;
	SPRESULT res = SendAndRecv(hd, NULL, NULL, hd, (void*)m_diagBuff, sizeof(m_diagBuff), &RecvLen, m_dwTimeOut);
	if (SP_OK != res) {
		return res;
	}
	AIOT_DIAG_CALIG_BAND_INFO* pBuff = (AIOT_DIAG_CALIG_BAND_INFO*)m_diagBuff;
	memcpy(&band_info,pBuff,sizeof(AIOT_DIAG_CALIG_BAND_INFO));
	return SP_OK;
}

SPRESULT CCaliCmd::AiotRfDebugAfc_V1(const AIOT_DIAG_AFC_REQ_T_V1* Param)
{
	if (NULL == Param)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid pointer!", __FUNCTION__);
		return SP_E_POINTER;
	}
	LogFmtStrA(SPLOGLV_INFO, __FUNCTION__);
	DeclareDiagHeader(hd, DIAG_LTE_RF, 0x0C);
	//SPRESULT res = SendCmd(hd, (const void*)Param, sizeof(AIOT_DIAG_FLAG_REQ_T), m_dwTimeOut);
	UINT32 RecvLen;
	SPRESULT res = SendAndRecv(hd, (const void*)Param, sizeof(AIOT_DIAG_AFC_REQ_T_V1), hd, (void*)m_diagBuff, sizeof(m_diagBuff), &RecvLen, m_dwTimeOut);
	if (SP_OK != res) {
		return res;
	}
	return SP_OK;
}

SPRESULT CCaliCmd::AiotRfCalFreq_V1(const UINT8 Stub_Cmd, const AIOT_DIAG_AFC_REQ_T_V1* Param)
{
	if (NULL == Stub_Cmd)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid pointer!", __FUNCTION__);
		return SP_E_POINTER;
	}
	LogFmtStrA(SPLOGLV_INFO, __FUNCTION__);
	DeclareDiagHeader(hd, DIAG_LTE_RF, Stub_Cmd);
	//SPRESULT res = SendCmd(hd, (const void*)Param, sizeof(AIOT_DIAG_AFC_REQ_T), m_dwTimeOut);
	UINT32 RecvLen;
	SPRESULT res = SendAndRecv(hd, (const void*)Param, sizeof(AIOT_DIAG_AFC_REQ_T_V1), hd, (void*)m_diagBuff, sizeof(m_diagBuff), &RecvLen, m_dwTimeOut);
	if (SP_OK != res) {
		return res;
	}
	return SP_OK;
}

SPRESULT CCaliCmd::AiotRfCalGetRssi_V1(const UINT8 Stub_Cmd, const AIOT_DIAG_CALIB_GETRSSI_REQ_T_V1* Req, AIOT_DIAG_CALIB_GETRSSI_CNF_T_V1* Cnf)
{
	if (NULL == Stub_Cmd)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid pointer!", __FUNCTION__);
		return SP_E_POINTER;
	}
	LogFmtStrA(SPLOGLV_INFO, __FUNCTION__);
	DeclareDiagHeader(hd, DIAG_LTE_RF, Stub_Cmd);
	UINT32 RecvLen;
	SPRESULT res = SendAndRecv(hd, (void*)Req, sizeof(AIOT_DIAG_CALIB_GETRSSI_REQ_T_V1), hd, (void*)m_diagBuff, sizeof(m_diagBuff), &RecvLen, m_dwTimeOut);
	if (SP_OK != res) {
		return res;
	}

	AIOT_DIAG_CALIB_GETRSSI_CNF_T_V1* Buff = (AIOT_DIAG_CALIB_GETRSSI_CNF_T_V1*)m_diagBuff;
	memcpy(Cnf, Buff, sizeof(AIOT_DIAG_CALIB_GETRSSI_CNF_T_V1));
	return SP_OK;
}

SPRESULT CCaliCmd::AiotRfDebugTx_V1(const AIOT_DIAG_TX_REQ_T_V1* Param)
{
	if (NULL == Param)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid pointer!", __FUNCTION__);
		return SP_E_POINTER;
	}
	LogFmtStrA(SPLOGLV_INFO, __FUNCTION__);
	DeclareDiagHeader(hd, DIAG_LTE_RF, 0x05);
	//SPRESULT res = SendCmd(hd, (const void*)Param, sizeof(AIOT_DIAG_FLAG_REQ_T), m_dwTimeOut);
	UINT32 RecvLen;
	SPRESULT res = SendAndRecv(hd, (const void*)Param, sizeof(AIOT_DIAG_TX_REQ_T_V1), hd, (void*)m_diagBuff, sizeof(m_diagBuff), &RecvLen, m_dwTimeOut);
	if (SP_OK != res) {
		return res;
	}
	return SP_OK;
}

SPRESULT CCaliCmd::AiotRfCalTx_V1(const UINT8 Stub_Cmd, const AIOT_DIAG_TX_REQ_T_V1* Param)
{
	if (NULL == Stub_Cmd)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid pointer!", __FUNCTION__);
		return SP_E_POINTER;
	}
	LogFmtStrA(SPLOGLV_INFO, __FUNCTION__);
	DeclareDiagHeader(hd, DIAG_LTE_RF, Stub_Cmd);
	//SPRESULT res = SendCmd(hd, (const void*)Param, sizeof(AIOT_DIAG_TX_REQ_T), m_dwTimeOut);
	UINT32 RecvLen;
	SPRESULT res = SendAndRecv(hd, (const void*)Param, sizeof(AIOT_DIAG_TX_REQ_T_V1), hd, (void*)m_diagBuff, sizeof(m_diagBuff), &RecvLen, m_dwTimeOut);
	if (SP_OK != res) {
		return res;
	}
	return SP_OK;
}

SPRESULT CCaliCmd::AiotRfFtTx_V1(const UINT8 Stub_Cmd, const AIOT_DIAG_TX_REQ_T_V1* Param)
{
	if (NULL == Stub_Cmd)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid pointer!", __FUNCTION__);
		return SP_E_POINTER;
	}
	LogFmtStrA(SPLOGLV_INFO, __FUNCTION__);
	DeclareDiagHeader(hd, DIAG_LTE_RF, Stub_Cmd);
	//SPRESULT res = SendCmd(hd, (const void*)Param, sizeof(AIOT_DIAG_TX_REQ_T), m_dwTimeOut);
	UINT32 RecvLen;
	SPRESULT res = SendAndRecv(hd, (const void*)Param, sizeof(AIOT_DIAG_TX_REQ_T_V1), hd, (void*)m_diagBuff, sizeof(m_diagBuff), &RecvLen, m_dwTimeOut);
	if (SP_OK != res) {
		return res;
	}
	return SP_OK;
}

SPRESULT CCaliCmd::AiotRfCalTxSave_V1(const UINT8 Stub_Cmd, const AIOT_DIAG_TX_CALIB_DATA_REQ_T_V1* Param)
{
	if (NULL == Stub_Cmd)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid pointer!", __FUNCTION__);
		return SP_E_POINTER;
	}
	LogFmtStrA(SPLOGLV_INFO, __FUNCTION__);
	DeclareDiagHeader(hd, DIAG_LTE_RF, Stub_Cmd);
	//SPRESULT res = SendCmd(hd, (const void*)Param, sizeof(AIOT_DIAG_TX_CALIB_DATA_REQ_T), m_dwTimeOut);
	UINT32 RecvLen;
	SPRESULT res = SendAndRecv(hd, (const void*)Param, sizeof(AIOT_DIAG_TX_CALIB_DATA_REQ_T_V1), hd, (void*)m_diagBuff, sizeof(m_diagBuff), &RecvLen, m_dwTimeOut);
	if (SP_OK != res) {
		return res;
	}
	return SP_OK;
}

SPRESULT CCaliCmd::AiotRfDebugRx_V1(const AIOT_DIAG_RX_REQ_T_V1* Param)
{
	if (NULL == Param)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid pointer!", __FUNCTION__);
		return SP_E_POINTER;
	}
	LogFmtStrA(SPLOGLV_INFO, __FUNCTION__);
	DeclareDiagHeader(hd, DIAG_LTE_RF, 0x06);
	//SPRESULT res = SendCmd(hd, (const void*)Param, sizeof(AIOT_DIAG_FLAG_REQ_T), m_dwTimeOut);
	UINT32 RecvLen;
	SPRESULT res = SendAndRecv(hd, (const void*)Param, sizeof(AIOT_DIAG_RX_REQ_T_V1), hd, (void*)m_diagBuff, sizeof(m_diagBuff), &RecvLen, m_dwTimeOut);
	if (SP_OK != res) {
		return res;
	}
	return SP_OK;
}

SPRESULT CCaliCmd::AiotRfCalRx_V1(const UINT8 Stub_Cmd, const AIOT_DIAG_RX_REQ_T_V1* Param)
{
	if (NULL == Stub_Cmd)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid pointer!", __FUNCTION__);
		return SP_E_POINTER;
	}
	LogFmtStrA(SPLOGLV_INFO, __FUNCTION__);
	DeclareDiagHeader(hd, DIAG_LTE_RF, Stub_Cmd);
	//SPRESULT res = SendCmd(hd, (const void*)Param, sizeof(AIOT_DIAG_RX_REQ_T), m_dwTimeOut);
	UINT32 RecvLen;
	SPRESULT res = SendAndRecv(hd, (const void*)Param, sizeof(AIOT_DIAG_RX_REQ_T_V1), hd, (void*)m_diagBuff, sizeof(m_diagBuff), &RecvLen, m_dwTimeOut);
	if (SP_OK != res) {
		return res;
	}
	return SP_OK;
}

SPRESULT CCaliCmd::AiotRfCalRxSave_V1(const UINT8 Stub_Cmd, const AIOT_DIAG_RX_CALIB_DATA_REQ_T_V1* Param)
{
	if (NULL == Stub_Cmd)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid pointer!", __FUNCTION__);
		return SP_E_POINTER;
	}
	LogFmtStrA(SPLOGLV_INFO, __FUNCTION__);
	DeclareDiagHeader(hd, DIAG_LTE_RF, Stub_Cmd);
	//SPRESULT res = SendCmd(hd, (const void*)Param, sizeof(AIOT_DIAG_RX_CALIB_DATA_REQ_T), m_dwTimeOut);
	UINT32 RecvLen;
	SPRESULT res = SendAndRecv(hd, (const void*)Param, sizeof(AIOT_DIAG_RX_CALIB_DATA_REQ_T_V1), hd, (void*)m_diagBuff, sizeof(m_diagBuff), &RecvLen, m_dwTimeOut);
	if (SP_OK != res) {
		return res;
	}
	return SP_OK;
}

SPRESULT CCaliCmd::AiotRfFtSetSyncParam_V1(const UINT8 Stub_Cmd, AIOT_DIAG_NSFT_FT_SETUP_REQ_T_V1* Param)
{
	if (NULL == Stub_Cmd)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid pointer!", __FUNCTION__);
		return SP_E_POINTER;
	}
	LogFmtStrA(SPLOGLV_INFO, __FUNCTION__);
	DeclareDiagHeader(hd, DIAG_LTE_RF, Stub_Cmd);
	//SPRESULT res = SendCmd(hd, (const void*)Param, sizeof(AIOT_DIAG_TX_REQ_T), m_dwTimeOut);
	UINT32 RecvLen;
	SPRESULT res = SendAndRecv(hd, (const void*)Param, sizeof(AIOT_DIAG_NSFT_FT_SETUP_REQ_T_V1), hd, (void*)m_diagBuff, sizeof(m_diagBuff), &RecvLen, m_dwTimeOut);
	if (SP_OK != res) {
		return res;
	}
	return SP_OK;
}

SPRESULT CCaliCmd::AiotRfCalNumber_V1(const UINT8 Stub_Cmd, const AIOT_DIAG_CALIB_NUM_REQ_T_V1* Param)
{
	if (NULL == Stub_Cmd)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid pointer!", __FUNCTION__);
		return SP_E_POINTER;
	}
	LogFmtStrA(SPLOGLV_INFO, __FUNCTION__);
	DeclareDiagHeader(hd, DIAG_LTE_RF, Stub_Cmd);
	UINT32 RecvLen;
	SPRESULT res = SendAndRecv(hd, (const void*)Param, sizeof(AIOT_DIAG_CALIB_NUM_REQ_T_V1), hd, (void*)m_diagBuff, sizeof(m_diagBuff), &RecvLen, m_dwTimeOut);
	if (SP_OK != res) {
		return res;
	}
	return SP_OK;
}

SPRESULT CCaliCmd::AiotFetchCalData_V1(const UINT8 Stub_Cmd, const AIOT_DIAG_FETCH_CAL_DATA_REQ_T_V1* Param, AIOT_DIAG_FETCH_CAL_DATA_CNF_T_V1* Fetch)
{
	if (NULL == Stub_Cmd)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid pointer!", __FUNCTION__);
		return SP_E_POINTER;
	}
	LogFmtStrA(SPLOGLV_INFO, __FUNCTION__);
	DeclareDiagHeader(hd, DIAG_LTE_RF, Stub_Cmd);
	UINT32 RecvLen;
	SPRESULT res = SendAndRecv(hd, (const void*)Param, sizeof(AIOT_DIAG_FETCH_CAL_DATA_REQ_T_V1), hd, (void*)m_diagBuff, sizeof(m_diagBuff), &RecvLen, m_dwTimeOut);
	if (SP_OK != res) {
		return res;
	}
	AIOT_DIAG_FETCH_CAL_DATA_CNF_T_V1* Cnf = (AIOT_DIAG_FETCH_CAL_DATA_CNF_T_V1*)m_diagBuff;
	memcpy(Fetch, Cnf, sizeof(AIOT_DIAG_FETCH_CAL_DATA_CNF_T_V1));
	return SP_OK;
}

SPRESULT CCaliCmd::AiotRfCalibDataBackup(const UINT8 Stub_Cmd, char* Cnf)
{
    if (NULL == Stub_Cmd || NULL == Cnf)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid pointer!", __FUNCTION__);
        return SP_E_POINTER;
    }
    LogFmtStrA(SPLOGLV_INFO, __FUNCTION__);

    DeclareDiagHeader(hd, DIAG_EXTEND_CMD, Stub_Cmd);
    UINT32 RecvLen;
    SPRESULT res = SendAndRecv(hd, NULL, NULL, hd, (void*)m_diagBuff, sizeof(m_diagBuff), &RecvLen, m_dwTimeOut);
    if (SP_OK != res) {
        return res;
    }
    *Cnf = *((char*)m_diagBuff);
    return SP_OK;
}

SPRESULT CCaliCmd::AiotRfFtNprachTx(UINT8 Stub_Cmd, const AIOT_DIAG_NPRACH_REQ* Param)
{
    if (NULL == Stub_Cmd)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid pointer!", __FUNCTION__);
        return SP_E_POINTER;
    }
    LogFmtStrA(SPLOGLV_INFO, __FUNCTION__);
    DeclareDiagHeader(hd, DIAG_LTE_RF, Stub_Cmd);
    //SPRESULT res = SendCmd(hd, (const void*)Param, sizeof(AIOT_DIAG_TX_REQ_T), m_dwTimeOut);
    UINT32 RecvLen;
    SPRESULT res = SendAndRecv(hd, (const void*)Param, sizeof(AIOT_DIAG_NPRACH_REQ), hd, (void*)m_diagBuff, sizeof(m_diagBuff), &RecvLen, m_dwTimeOut);
    if (SP_OK != res) {
        return res;
    }
    return SP_OK;
}

SPRESULT CCaliCmd::AiotRfCalibGetThmTempLoss(const UINT8 Stub_Cmd, const AIOT_DIAG_CALIB_TEMP_VALUE_REQ* pReq, AIOT_DIAG_TEMP_VALUE_CNF* pCnf)
{
    if (NULL == Stub_Cmd || NULL == pCnf || NULL == pReq)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid pointer!", __FUNCTION__);
        return SP_E_POINTER;
    }
    LogFmtStrA(SPLOGLV_INFO, __FUNCTION__);

    DeclareDiagHeader(hd, DIAG_LTE_RF, Stub_Cmd);
    UINT32 RecvLen;
    SPRESULT res = SendAndRecv(hd, (const void*)pReq, sizeof(AIOT_DIAG_CALIB_TEMP_VALUE_REQ), hd, (void*)m_diagBuff, sizeof(m_diagBuff), &RecvLen, m_dwTimeOut);
    if (SP_OK != res) {
        return res;
    }
    AIOT_DIAG_TEMP_VALUE_CNF* Cnf = (AIOT_DIAG_TEMP_VALUE_CNF*)m_diagBuff;
    memcpy(pCnf, Cnf, sizeof(AIOT_DIAG_TEMP_VALUE_CNF));
    return SP_OK;
}