#include "StdAfx.h"
#include "LoadPhaseCheckConfig.h"

//////////////////////////////////////////////////////////////////////////
CLoadPhaseCheckConfig::CLoadPhaseCheckConfig(void)
    : m_eMagic(SP09)
    , m_eSignFlag(PASS_0_FAIL_1)
{
}


CLoadPhaseCheckConfig::~CLoadPhaseCheckConfig(void)
{
}

void CLoadPhaseCheckConfig::Load(HMODULE hApp)
{
    CHAR szAppPath[MAX_PATH] = {0};
    GetModuleFileNameA(hApp, szAppPath, MAX_PATH);
    LPSTR lpChar = strrchr(szAppPath, '\\');
    if (NULL != lpChar)
    {
        *lpChar = '\0';
    }
    strcat_s(szAppPath, "\\PhaseCheck.ini");

    CHAR szValue[64] = {0};
    GetPrivateProfileStringA("VERSION", "MAGIC NUMBER", "SP09", szValue, _countof(szValue), szAppPath);
    if (0 == _stricmp(szValue, "SP05"))
    {
        m_eMagic = SP05;
    }
    else if (0 == _stricmp(szValue, "SP15"))
    {
        m_eMagic = SP15;
    }
    else
    {
        m_eMagic = SP09;
    }

    INT nValue = GetPrivateProfileIntA("STATE FLAG", "PASS VALUE", 0, szAppPath);
    m_eSignFlag = (0 == nValue) ? PASS_0_FAIL_1 : PASS_1_FAIL_0;

    m_vecStations.clear();
    LPCSTR SECTION_STATION = "STATION";
    INT nCount = GetPrivateProfileIntA(SECTION_STATION, "STATION NUMBER", 0, szAppPath);
    for (INT i=0; i<nCount; i++)
    {
        CHAR szKey[32] = {0};
        sprintf_s(szKey, "STATION %d", i+1);
        GetPrivateProfileStringA(SECTION_STATION, szKey, "", szValue, _countof(szValue), szAppPath);
        if (strlen(szValue) > 0)
        {
            m_vecStations.push_back(szValue);
        }
    }
}