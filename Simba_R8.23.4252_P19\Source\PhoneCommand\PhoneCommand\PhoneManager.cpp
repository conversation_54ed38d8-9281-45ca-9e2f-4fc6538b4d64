#include "StdAfx.h"
#include "PhoneManager.h"
#include "CLocks.h"
#include "AppSettings.h"
#include "MemoryCounter.h"

///
CPhoneManager::CPhoneManager(void)
: m_refCount(0)
{

}

CPhoneManager::~CPhoneManager(void)
{

}

SP_HANDLE CPhoneManager::Create(LPVOID pLogUtil)
{
    COUNT_MEMORY0(pLogUtil);

    CDiagPhone* pDiagPhone = NULL;
    try
    {
        pDiagPhone = new CDiagPhone();
    }
    catch (const std::bad_alloc& /*e*/)
    {
        pDiagPhone = NULL; 
    }
    if (NULL == pDiagPhone)
    {
        assert(0);
        return INVALID_NPI_HANDLE;
    }
    
    if (!pDiagPhone->Startup((ISpLog* )pLogUtil))
    {
        delete pDiagPhone;
        return INVALID_NPI_HANDLE;
    }

    SP_HANDLE hDiagPhone = INVALID_NPI_HANDLE;
    CLocks Lock(m_Lock);
    if (0 == m_refCount)
    {   
        // Ignore register for VCOM PID
        extern void RegisterSprdVComKey(const std::wstring& sn);
        std::wstring sn = L"";
        CAppSettings::GetInstance().GetValue(CAppSettings::IgnoreSn, &sn);
        RegisterSprdVComKey(sn);

        UINT nLv = SPLOGLV_VERBOSE;
        CAppSettings::GetInstance().GetValue(CAppSettings::DevHound, (LPVOID)&nLv);

        if (!m_DevHound.Start(nLv))
        {
            pDiagPhone->Cleanup();
            delete pDiagPhone;
            return INVALID_NPI_HANDLE;
        }
    }

    InterlockedIncrement((long* )&m_refCount);

    hDiagPhone = static_cast<SP_HANDLE>(pDiagPhone);
    m_mapPhone.insert(std::make_pair(hDiagPhone, pDiagPhone));
    m_DevHound.Insert((IDevMonitor* )pDiagPhone);
   
    return hDiagPhone;
}

CDiagPhone* CPhoneManager::Find(SP_HANDLE hDiagPhone)
{
    if (INVALID_NPI_HANDLE != hDiagPhone)
    {
        CMapPhone::iterator it = m_mapPhone.find(hDiagPhone);
        if (it != m_mapPhone.end())
        {
            return it->second;
        }
    }

    return NULL;
}

void CPhoneManager::Free(SP_HANDLE hDiagPhone)
{
    if (INVALID_NPI_HANDLE == hDiagPhone)
    {
        assert(0);
        return ;
    }

    CLocks Lock(m_Lock);
    CMapPhone::iterator it = m_mapPhone.find(hDiagPhone);
    if (it != m_mapPhone.end())
    {
        CDiagPhone* pDiagPhone = it->second;
        if (NULL == pDiagPhone)
        {
            assert(0);
            return ;
        }

        pDiagPhone->Cleanup();

        m_mapPhone.erase(it);
        m_DevHound.Remove((IDevMonitor* )pDiagPhone);

        delete pDiagPhone;
        pDiagPhone = NULL;

        InterlockedDecrement((long* )&m_refCount);
        if (0 == m_refCount)
        {
            /// Stop Device Monitor
            m_DevHound.Stop();
        }
    }
}
