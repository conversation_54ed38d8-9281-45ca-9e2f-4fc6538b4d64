﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="04.Interface">
      <UniqueIdentifier>{31dac767-c135-4343-87ba-7c670761ed73}</UniqueIdentifier>
    </Filter>
    <Filter Include="06.RC">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav</Extensions>
    </Filter>
    <Filter Include="05.Impl">
      <UniqueIdentifier>{cf49520d-6fc8-42f5-a6ca-781aad40c8bb}</UniqueIdentifier>
    </Filter>
    <Filter Include="05.Impl\04.RF">
      <UniqueIdentifier>{5ea3937d-bd9c-46b2-9cf3-3eacd1d3434c}</UniqueIdentifier>
    </Filter>
    <Filter Include="05.Impl\02.Base">
      <UniqueIdentifier>{50799a6a-ff4f-4159-ab0b-107aa6a1eda1}</UniqueIdentifier>
    </Filter>
    <Filter Include="05.Impl\03.Common">
      <UniqueIdentifier>{8ed88f0d-ca31-419a-bf4f-62f486c9d437}</UniqueIdentifier>
    </Filter>
    <Filter Include="05.Impl\05.Manager">
      <UniqueIdentifier>{c43bc6a9-db0c-4584-9f0e-e3859e5c93e3}</UniqueIdentifier>
    </Filter>
    <Filter Include="05.Impl\01.Observer">
      <UniqueIdentifier>{7936faa1-5a9b-46c5-a538-0921582ef8c3}</UniqueIdentifier>
    </Filter>
    <Filter Include="01.Include">
      <UniqueIdentifier>{f1ab41c0-7ba3-4b53-94e0-11040cd91516}</UniqueIdentifier>
    </Filter>
    <Filter Include="02.Utility">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="02.Utility\01.DevHound">
      <UniqueIdentifier>{3d8442d9-945a-4ee5-b7cb-9e136cf2f547}</UniqueIdentifier>
    </Filter>
    <Filter Include="02.Utility\02.Trace">
      <UniqueIdentifier>{d7d2f9ce-1705-422b-a75a-d35c9478ae84}</UniqueIdentifier>
    </Filter>
    <Filter Include="02.Utility\03.Memory">
      <UniqueIdentifier>{bb7cf110-2fcc-4ad7-9ab3-49e66f92a578}</UniqueIdentifier>
    </Filter>
    <Filter Include="02.Utility\04.CRC">
      <UniqueIdentifier>{d76fa6eb-9189-43de-8ad4-e9cb017e6a5a}</UniqueIdentifier>
    </Filter>
    <Filter Include="02.Utility\05.Thread">
      <UniqueIdentifier>{92b8e1f8-bd4f-45ba-9027-aa7a3f70e23d}</UniqueIdentifier>
    </Filter>
    <Filter Include="02.Utility\06.Version">
      <UniqueIdentifier>{086c5c6b-94ff-42d5-9261-0c8676f8e0c8}</UniqueIdentifier>
    </Filter>
    <Filter Include="02.Utility\07.Lock">
      <UniqueIdentifier>{0a067136-0758-4141-b833-8022f2e96850}</UniqueIdentifier>
    </Filter>
    <Filter Include="02.Utility\08.Endian">
      <UniqueIdentifier>{997beedc-e793-47b2-a8f4-165f31d21176}</UniqueIdentifier>
    </Filter>
    <Filter Include="02.Utility\03.Config">
      <UniqueIdentifier>{4e72ecfe-6e15-4dd7-90d7-536621ccc3e4}</UniqueIdentifier>
    </Filter>
    <Filter Include="02.Utility\09.RF">
      <UniqueIdentifier>{74d945b2-8a7d-4c63-bb0f-08010b9d26e8}</UniqueIdentifier>
    </Filter>
    <Filter Include="01.Include\DiagDef">
      <UniqueIdentifier>{75189dea-4e32-4d6c-a1de-f5fa95b46aae}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="ApiExport.cpp">
      <Filter>04.Interface</Filter>
    </ClCompile>
    <ClCompile Include="stdafx.cpp">
      <Filter>06.RC</Filter>
    </ClCompile>
    <ClCompile Include="Calibration\DMR.cpp">
      <Filter>05.Impl\04.RF</Filter>
    </ClCompile>
    <ClCompile Include="Calibration\GSM.cpp">
      <Filter>05.Impl\04.RF</Filter>
    </ClCompile>
    <ClCompile Include="Calibration\LTE.cpp">
      <Filter>05.Impl\04.RF</Filter>
    </ClCompile>
    <ClCompile Include="Calibration\TD.cpp">
      <Filter>05.Impl\04.RF</Filter>
    </ClCompile>
    <ClCompile Include="Calibration\WCDMA.cpp">
      <Filter>05.Impl\04.RF</Filter>
    </ClCompile>
    <ClCompile Include="DiagBase.cpp">
      <Filter>05.Impl\02.Base</Filter>
    </ClCompile>
    <ClCompile Include="RecvPkgsList.cpp">
      <Filter>05.Impl\02.Base</Filter>
    </ClCompile>
    <ClCompile Include="UeAssert.cpp">
      <Filter>05.Impl\02.Base</Filter>
    </ClCompile>
    <ClCompile Include="AP.cpp">
      <Filter>05.Impl\03.Common</Filter>
    </ClCompile>
    <ClCompile Include="CommCmd.cpp">
      <Filter>05.Impl\03.Common</Filter>
    </ClCompile>
    <ClCompile Include="DevMode.cpp">
      <Filter>05.Impl\03.Common</Filter>
    </ClCompile>
    <ClCompile Include="PhoneManager.cpp">
      <Filter>05.Impl\05.Manager</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Common\UtilFn\LogFile.cpp">
      <Filter>05.Impl\01.Observer</Filter>
    </ClCompile>
    <ClCompile Include="AppSettings.cpp">
      <Filter>02.Utility\03.Config</Filter>
    </ClCompile>
    <ClCompile Include="DevHound\DevHound.cpp">
      <Filter>02.Utility\01.DevHound</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Common\Tr\Tr.cpp">
      <Filter>02.Utility\02.Trace</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Common\dlmalloc\dlmalloc.c">
      <Filter>02.Utility\03.Memory</Filter>
    </ClCompile>
    <ClCompile Include="CRC\crc16.c">
      <Filter>02.Utility\04.CRC</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Common\thread\Thread.cpp">
      <Filter>02.Utility\05.Thread</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Common\UtilFn\AppVer.cpp">
      <Filter>02.Utility\06.Version</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Common\lock\CLocks.cpp">
      <Filter>02.Utility\07.Lock</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Common\lock\CSimpleLock.cpp">
      <Filter>02.Utility\07.Lock</Filter>
    </ClCompile>
    <ClCompile Include="EndianConvert.cpp">
      <Filter>02.Utility\08.Endian</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Common\UtilFn\LteUtility.cpp">
      <Filter>02.Utility\09.RF</Filter>
    </ClCompile>
    <ClCompile Include="ExtendCmd.cpp">
      <Filter>05.Impl\03.Common</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Common\UtilFn\gsmUtility.cpp">
      <Filter>02.Utility\09.RF</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Common\UtilFn\TDUtility.cpp">
      <Filter>02.Utility\09.RF</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Common\UtilFn\wcdmaUtility.cpp">
      <Filter>02.Utility\09.RF</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Common\UtilFn\NrUtility.cpp">
      <Filter>02.Utility\09.RF</Filter>
    </ClCompile>
    <ClCompile Include="Calibration\NR.cpp">
      <Filter>05.Impl\04.RF</Filter>
    </ClCompile>
    <ClCompile Include="LoadPhaseCheckConfig.cpp">
      <Filter>02.Utility\03.Config</Filter>
    </ClCompile>
    <ClCompile Include="Calibration\C2K.cpp">
      <Filter>05.Impl\04.RF</Filter>
    </ClCompile>
    <ClCompile Include="Calibration\Common.cpp">
      <Filter>05.Impl\04.RF</Filter>
    </ClCompile>
    <ClCompile Include="Calibration\UIS891x.cpp">
      <Filter>05.Impl\04.RF</Filter>
    </ClCompile>
    <ClCompile Include="IgnoreSprdVcomDrvInRegister.cpp">
      <Filter>02.Utility\03.Config</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Common\UtilFn\RunMode.cpp">
      <Filter>05.Impl\03.Common</Filter>
    </ClCompile>
    <ClCompile Include="AT.cpp">
      <Filter>05.Impl\02.Base</Filter>
    </ClCompile>
    <ClCompile Include="Audio.cpp">
      <Filter>05.Impl\03.Common</Filter>
    </ClCompile>
    <ClCompile Include="Calibration\UIS8811.cpp">
      <Filter>05.Impl\04.RF</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\..\Common\Include\PhoneCommand.h">
      <Filter>04.Interface</Filter>
    </ClInclude>
    <ClInclude Include="resource.h">
      <Filter>06.RC</Filter>
    </ClInclude>
    <ClInclude Include="stdafx.h">
      <Filter>06.RC</Filter>
    </ClInclude>
    <ClInclude Include="targetver.h">
      <Filter>06.RC</Filter>
    </ClInclude>
    <ClInclude Include="Calibration\CaliCmd.h">
      <Filter>05.Impl\04.RF</Filter>
    </ClInclude>
    <ClInclude Include="DiagBase.h">
      <Filter>05.Impl\02.Base</Filter>
    </ClInclude>
    <ClInclude Include="RecvPkgsList.h">
      <Filter>05.Impl\02.Base</Filter>
    </ClInclude>
    <ClInclude Include="UeAssert.h">
      <Filter>05.Impl\02.Base</Filter>
    </ClInclude>
    <ClInclude Include="CommCmd.h">
      <Filter>05.Impl\03.Common</Filter>
    </ClInclude>
    <ClInclude Include="DevMode.h">
      <Filter>05.Impl\03.Common</Filter>
    </ClInclude>
    <ClInclude Include="DiagPhone.h">
      <Filter>05.Impl\05.Manager</Filter>
    </ClInclude>
    <ClInclude Include="PhoneManager.h">
      <Filter>05.Impl\05.Manager</Filter>
    </ClInclude>
    <ClInclude Include="Include\FileOBS.h">
      <Filter>05.Impl\01.Observer</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Common\UtilFn\LogFile.h">
      <Filter>05.Impl\01.Observer</Filter>
    </ClInclude>
    <ClInclude Include="Include\SocketOBS.h">
      <Filter>05.Impl\01.Observer</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Common\Include\global_def.h">
      <Filter>01.Include</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Common\Include\global_err.h">
      <Filter>01.Include</Filter>
    </ClInclude>
    <ClInclude Include="AppSettings.h">
      <Filter>02.Utility\03.Config</Filter>
    </ClInclude>
    <ClInclude Include="DevHound\DevHound.h">
      <Filter>02.Utility\01.DevHound</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Common\Tr\Tr.h">
      <Filter>02.Utility\02.Trace</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Common\dlmalloc\dlmalloc.h">
      <Filter>02.Utility\03.Memory</Filter>
    </ClInclude>
    <ClInclude Include="CRC\crc16.h">
      <Filter>02.Utility\04.CRC</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Common\thread\Thread.h">
      <Filter>02.Utility\05.Thread</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Common\UtilFn\AppVer.h">
      <Filter>02.Utility\06.Version</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Common\lock\CLocks.h">
      <Filter>02.Utility\07.Lock</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Common\lock\CSimpleLock.h">
      <Filter>02.Utility\07.Lock</Filter>
    </ClInclude>
    <ClInclude Include="EndianConvert.h">
      <Filter>02.Utility\08.Endian</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Common\UtilFn\LteUtility.h">
      <Filter>02.Utility\09.RF</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Common\UtilFn\gsmUtility.h">
      <Filter>02.Utility\09.RF</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Common\UtilFn\TDUtility.h">
      <Filter>02.Utility\09.RF</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Common\UtilFn\wcdmaUtility.h">
      <Filter>02.Utility\09.RF</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Common\Include\PortCapture.h">
      <Filter>01.Include</Filter>
    </ClInclude>
    <ClInclude Include="..\Common\DiagDefine\diagdef.h">
      <Filter>01.Include\DiagDef</Filter>
    </ClInclude>
    <ClInclude Include="..\Common\DiagDefine\CommonDef.h">
      <Filter>01.Include\DiagDef</Filter>
    </ClInclude>
    <ClInclude Include="..\Common\DiagDefine\GsmDef.h">
      <Filter>01.Include\DiagDef</Filter>
    </ClInclude>
    <ClInclude Include="..\Common\DiagDefine\TdDef.h">
      <Filter>01.Include\DiagDef</Filter>
    </ClInclude>
    <ClInclude Include="..\Common\DiagDefine\WcdmaDef.h">
      <Filter>01.Include\DiagDef</Filter>
    </ClInclude>
    <ClInclude Include="..\Common\DiagDefine\LteDef.h">
      <Filter>01.Include\DiagDef</Filter>
    </ClInclude>
    <ClInclude Include="..\Common\DiagDefine\NrDef.h">
      <Filter>01.Include\DiagDef</Filter>
    </ClInclude>
    <ClInclude Include="..\Common\DiagDefine\C2kDef.h">
      <Filter>01.Include\DiagDef</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Common\UtilFn\NrUtility.h">
      <Filter>02.Utility\09.RF</Filter>
    </ClInclude>
    <ClInclude Include="LoadPhaseCheckConfig.h">
      <Filter>02.Utility\03.Config</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Common\UtilFn\RunMode.h">
      <Filter>05.Impl\03.Common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Common\Include\Phonecommand_NR.h">
      <Filter>04.Interface</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Common\Include\Phonecommand_NVM.h">
      <Filter>04.Interface</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="PhoneCommand.rc">
      <Filter>06.RC</Filter>
    </ResourceCompile>
  </ItemGroup>
</Project>