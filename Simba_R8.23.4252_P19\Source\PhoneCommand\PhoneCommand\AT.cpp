﻿#include "StdAfx.h"
#include "DiagBase.h"

//////////////////////////////////////////////////////////////////////////
SPRESULT CDiagBase::SendATCommand(LPCSTR lpszAT, BOOL bWantReply, LPVOID lpBuff, uint32 u32BufSize, uint32* lpu32RecvBytes, uint32 u32TimeOut /* = TIMEOUT_3S */)
{
    if (NULL == lpszAT)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid AT Command (NULL)!", __FUNCTION__);
        return SP_E_PHONE_INVALID_PARAMETER;
    }

    LogFmtStrA(SPLOGLV_INFO, "SendATCommand: %s", lpszAT);

    std::string at = lpszAT;
    if ((at.substr(at.length() - 1) != "\r") && (at.substr(at.length() - 2) != "\r\n"))
    {
        // If the string is not end with "\r" or "\r\n", then append "\r"
        at += "\r\n";
    }

    //  PC --> MS: 7e ... 68 00 cmd-string ... 7e
    //  PC <-- MS: 7e ... d5 00 7e 
    //  PC <-- MS: 7e ... 9c 00 response string 1 ... 7e 
    //  PC <-- MS: 7e ... 9c 00 response string 2 ... 7e 
    //  PC <-- MS: 7e ... 9c 00 response string n ... 7e
    //  PC <-- MS: 7e ... 9c 00 OK \r\n 7e 

    DIAG_HEADER wh;
    wh.sn      = 0; // SPECIAL_SN; Xiaoping.Jing, S/W bug: SN exceed 0xFFFF, it works bad
    wh.len     = 0;
    wh.type    = PPP_PACKET_R;
    wh.subtype = ZERO_SUBTYPE;

    // Bug1235439: Clear Channel & DiagChan buffer before sending AT command
    Clear();

    if (!bWantReply)
    {
        return SendCmd(wh, (const void* )at.c_str(), at.size());
    }

    DIAG_HEADER rh;
    rh.sn      = SPECIAL_SN; // AT+SPDSPOP=0 . AT+ARMLOG=0 SN auto increased
    rh.len     = 0;
    rh.type    = PPP_PACKET_A;
    rh.subtype = ZERO_SUBTYPE;

    CRecvPkgsList recvList;
    recvList.AddCondition(rh);
    SPRESULT res = SendAndRecv(wh, (const void* )at.c_str(), at.size(), recvList, u32TimeOut);
    if (SP_OK == res)
    {
        uint32 offset = 0;
        for (int i=0; i<recvList.GetPkgsCount(); i++)
        {
            uint32 u32Len = 0;
            CHAR   szBuff[MAX_DIAG_BUFF_SIZE] = {0};
            if (UnpackPRT(recvList.GetPackage(i), NULL, (void* )szBuff, sizeof(szBuff), &u32Len))
            {
                uint8* p = (uint8* )((uint8* )lpBuff + offset);
                offset += u32Len;

                if (u32BufSize >= offset)
                {
                    memcpy((void* )p, (const void* )szBuff, u32Len);
                }
                else
                {
                    LogFmtStrA(SPLOGLV_WARN, "SendATCommand(%s): buffer (%d) is too small!", lpszAT, u32BufSize);
                    res = SP_E_PHONE_BUFFER_TOO_SMALL;
                }
            }
            else
            {
                assert(0);
            }
        }

        if (NULL != lpu32RecvBytes)
        {
            *lpu32RecvBytes = offset;
        }
    }

    return res;
}

//////////////////////////////////////////////////////////////////////////
void CDiagBase::WakeupATResponse(PRT_BUFF* lpData)
{
    if (NULL == lpData)
    {
        assert(0);
        return ;
    }
    
    CHAR szBuff[MAX_DIAG_BUFF_SIZE] = {0};
    uint32 u32Size = 0;
    if (UnpackPRT(lpData, NULL, (void* )szBuff, sizeof(szBuff), &u32Size))
    {
        std::string strRsp = szBuff;

        /*
        [2019-10-31 10:03:19:926] --> 29(0x0000001d) Bytes
                                00000000h: 41 54 5E 53 50 53 49 4D 4C 4F 43 4B 44 41 54 41 ; AT^SPSIMLOCKDATA
                                00000010h: 57 52 49 54 45 3D 31 31 32 38 30 0D 0A          ; WRITE=11280..
        [2019-10-31 10:03:20:028] <-- 13(0x0000000d) Bytes
                                00000000h: 0A 00 00 00 0D 00 9C 00 0D 0A 3E 20 0D          ; ..........> .
        [2019-10-31 10:03:20:028] --> 11281(0x00002c11) Bytes
                                00000000h: 30 62 30 30 30 30 30 30 30 33 30 30 30 30 30 30 ; 0b00000003000000
                                00000010h: 30 36 30 30 63 63 30 31 30 30 30 30 30 32 30 30 ; 0600cc0100000200
        */
        if (   (std::string::npos != strRsp.find("\r\nOK\r\n")) 
            || (std::string::npos != strRsp.find("+CME ERROR"))
            || (std::string::npos != strRsp.find("\r\n> \r"))
            )
        {
            WakeUpOnResponse();
        }
    }
}


//////////////////////////////////////////////////////////////////////////
///Bug 1732369 GE3F内存小，CNR的计算放到工具侧
/// GNSS CP侧从硬件Buffer中抓一批数据保存，数据经过FFT计算处理得到CNR值。原本数据保存在AP侧由Libgnss计算输出，因模组没有该模块且CP的iram没有存数空间，改为发送给工具端进行数据保存和计算。
///发送AT + SPGPSTEST = EUT, 2命令后，进入CW测试模式，工具侧直接开始接收数据并处理。上传数据格式如下：
///Diag上报类型 Type = 0x3A，Subtype = 0x5
///第1包数据内容：DATA_CAPTURE_BEGIN
///第2~第5633包数据：每包4个unsigned int数据，共22528个。转换为ASIC格式发送，收到后，合并为unsigned int处理
///最后1包数据：DATA_CAPTURE_END
SPRESULT CDiagBase::ReadModuleCNR(LPCSTR lpszAT, LPVOID lpBuff, uint32 u32BufSize, uint32* lpu32RecvBytes, uint32 u32TimeOut /* = TIMEOUT_5S */)
{
	if (NULL == lpszAT || IsBadWritePtr(lpBuff, u32BufSize))
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid AT Command (NULL)!", __FUNCTION__);
		return SP_E_PHONE_INVALID_PARAMETER;
	}

	LogFmtStrA(SPLOGLV_INFO, "SendATCommand: %s", lpszAT);

	std::string at = lpszAT;
	if ((at.substr(at.length() - 1) != "\r") && (at.substr(at.length() - 2) != "\r\n"))
	{
		// If the string is not end with "\r" or "\r\n", then append "\r"
		at += "\r\n";
	}

	//  PC --> MS: 7e ... 68 00 cmd-string ... 7e
	//  PC <-- MS: 7e ... d5 00 7e 
	//  PC <-- MS: 7e ... 9c 00 response string 1 ... 7e 
	//  PC <-- MS: 7e ... 9c 00 response string 2 ... 7e 
	//  PC <-- MS: 7e ... 9c 00 response string n ... 7e
	//  PC <-- MS: 7e ... 9c 00 OK \r\n 7e 

	DIAG_HEADER wh;
	wh.sn = 0; // SPECIAL_SN; Xiaoping.Jing, S/W bug: SN exceed 0xFFFF, it works bad
	wh.len = 0;
	wh.type = PPP_PACKET_R;
	wh.subtype = ZERO_SUBTYPE;

	// Bug1235439: Clear Channel & DiagChan buffer before sending AT command
	Clear();

	DIAG_HEADER rh;
	rh.sn = SPECIAL_SN; // AT+SPDSPOP=0 . AT+ARMLOG=0 SN auto increased
	rh.len = 0;
	rh.type = 0x3A;
	rh.subtype = 0x05;

    /// <summary>
	/// Diag上报类型 Type = 0x3A，Subtype = 0x5
	/// 第1包数据内容：DATA_CAPTURE_BEGIN
	///	第2~第5633包数据：每包4个unsigned int数据，共22528个。转换为ASIC格式发送，收到后，合并为unsigned int处理
	///	最后1包数据：DATA_CAPTURE_END
    /// </summary>
 //   const uint32 MAX_CNR_DATA_PACKAGE_COUNT = 5634;//1+ 5632 +1

    m_bStartModuleCNRRecving = FALSE;
	CRecvPkgsList recvList;
	recvList.AddCondition(rh);
	SPRESULT res = SendAndRecv(wh, (const void*)at.c_str(), at.size(), recvList, u32TimeOut);
	if (SP_OK == res)
	{

/*  数据包不固定      if (MAX_CNR_DATA_PACKAGE_COUNT != recvList.GetPkgsCount())
        {
			LogFmtStrA(SPLOGLV_WARN, "GetPkgsCount(%d) Not equal to(%d)!", recvList.GetPkgsCount(), MAX_CNR_DATA_PACKAGE_COUNT);
			res = SP_E_PHONE_INVALID_PARAMETER;
        }*/
		uint32 offset = 0;
		for (int i = 0; i < recvList.GetPkgsCount(); i++)
		{
			uint32 u32Len = 0;
            uint8  szBuff[MAX_DIAG_BUFF_SIZE] = { 0 };
			if (UnpackPRT(recvList.GetPackage(i), NULL, (void*)szBuff, sizeof(szBuff), &u32Len))
			{
				uint8* p = (uint8*)((uint8*)lpBuff + offset);
				offset += u32Len;

				if (u32BufSize >= offset)
				{
					memcpy((void*)p, (const void*)szBuff, u32Len);
				}
				else
				{
					LogFmtStrA(SPLOGLV_WARN, "SendATCommand(%s): buffer (%d) is too small!", lpszAT, u32BufSize);
					res = SP_E_PHONE_BUFFER_TOO_SMALL;
				}
			}
			else
			{
				assert(0);
			}
		}

		if (NULL != lpu32RecvBytes)
		{
			*lpu32RecvBytes = offset;
		}
	}

	return res;
}

//Bug 1732369 GE3F内存小，CNR的计算放到工具侧	
void CDiagBase::WakeupModuleCNRResponse(PRT_BUFF* lpData)
{
    if (NULL == lpData)
    {
        assert(0);
        return;
    }

    uint8 szBuff[MAX_DIAG_BUFF_SIZE] = { 0 };
    uint32 u32Size = 0;
    if (UnpackPRT(lpData, NULL, (void*)szBuff, sizeof(szBuff), &u32Size))
    {
        std::string strRsp = (char*)szBuff;

        /*
        CNR测试方法如下：
        由于模组上电后自动运行，所以，测试CNR时，需要先把GPS关闭，然后再打开CW测试模式。

        AT+SPGPSTEST=EUT?
        if != 0
	        AT+SPGPSTEST=EUT,0
	        delay 200ms

        AT+SPGPSTEST=EUT,2   //打开CW测试
        //工具开始接收数据并计算处理

        发送AT+SPGPSTEST=EUT,2命令后，进入CW测试模式，工具侧直接准备接收数据并处理数据。

        上传的数据格式如下：CW数据上报Type = 0x3A，Subtype = 0X05
        数据内容如下：
        第一包数据内容：DATA_CAPTURE_BEGIN
        第2～n包数据内容：4个unsigned int 数据，转换为ASIC格式发送。收到后，合并为unsigned int处理
        最后一包数据内容：DATA_CAPTURE_END
        以上三类数据收集完整，进行一系列FFT计算。
        以上三类数据会一直发送，知道发送停止指令：AT+SPGPSTEST=EUT,0
        */

		if (std::string::npos != strRsp.find("DATA_CAPTURE_BEGIN\r\n"))
		{
			m_bStartModuleCNRRecving = TRUE;
		}


        if (std::string::npos != strRsp.find("DATA_CAPTURE_END\r\n"))
        {
            WakeUpOnResponse();
        }
    }
}



