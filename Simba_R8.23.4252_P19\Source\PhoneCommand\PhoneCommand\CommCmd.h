#pragma once
#include "DevMode.h"
#include "CommonDef.h"
//////////////////////////////////////////////////////////////////////////
/// 
class CCommCmd : public CDevMode
{
public:
    CCommCmd(void);
    virtual ~CCommCmd(void);

    ///
    /// MISC
    ///
    SPRESULT GetSWVer(PC_SWVER_TYPE eType, LPVOID lpBuff, uint32 u32BufSize);
    SPRESULT GetUID(uint8 UID[20]);
    SPRESULT GetUID_V2(LPVOID lpBuff, UINT32 u32BufLen);
    SPRESULT EnableArmLog(BOOL bEnable);
    SPRESULT PressKeyboard(uint32 u32Code);
    SPRESULT VibrateOn(BOOL bOn);
    SPRESULT LedOn(BOOL bOn);
    SPRESULT GetVoltage(uint32* lpu32mV);
    //////////////////////////////////////////////////////////////////////////
    /// ULA(Unisoc License Authorization)
    SPRESULT ULACheckLicenseNBD(void *lpModel, uint32 u32ModelLen, void *lpRecvBuff, uint32 u32RecvBuffLen);
	SPRESULT ULACheckLicense(void *lpRecvBuff, uint32 u32RecvBuffLen);

    SPRESULT ULAReadUIDNBD(LPBYTE lpSendBuff, uint32 u32SendBuffLen, UINT *pUidBitLen, LPBYTE lpRecvUidBuffer, uint32 u32RecvLen, uint32 *pu32UidBufLen);
	SPRESULT ULAReadUID(LPBYTE lpSendBuff, uint32 u32SendBuffLen, UINT *pUidBitLen, LPBYTE lpRecvUidBuffer, uint32 u32RecvLen, uint32 *pu32UidBufLen);

    SPRESULT ULAWriteLicense(void *lpBuff, uint32 u32BuffLen);

    ///
    SPRESULT AssertUE(void);

    ///
    /// Register & Memory
    ///
    SPRESULT LoadRegister(PC_ACCESS_MODE_E eMode, uint32 u32Addr, uint32 u32Count, LPVOID  lpData);
    SPRESULT SaveRegister(PC_ACCESS_MODE_E eMode, uint32 u32Addr, uint32 u32Count, LPCVOID lpData);

	//read write nv
	SPRESULT ReadNV (uint16 u16NvID, LPVOID lpData, uint32 u32BytesToRead, uint32 *lpu32RecvLen);
	SPRESULT WriteNV(uint16 u16NvID, LPCVOID lpData, uint32 u32BytesToWrite);
    SPRESULT SyncNV(uint32 u32TimeOut);
    
    ///
    /// R/W product data
    ///
    SPRESULT LoadProductData(LPPC_PRODUCT_DATA  lpData, uint32 dwTimeOut = TIMEOUT_3S);
    SPRESULT SaveProductData(LPCPC_PRODUCT_DATA lpData, uint32 dwTimeOut = TIMEOUT_3S);

    /// 
    /// R/W GEID (IMEI & MEID)
    ///
    SPRESULT LoadGEID(PC_GEID_T* lpGEID, uint32 u32TimeOut);
    SPRESULT SaveGEID(const PC_GEID_T* lpGEID, uint32 u32TimeOut);

    ///
    /// R/W Product Info. Area. (PhaseCheck)
    ///
    SPRESULT LoadProductInfo(SPPH_MAGIC eMagic, LPVOID  lpBuff, uint32 u32BytesToRead, uint32 u32TimeOut);
    SPRESULT SaveProductInfo(SPPH_MAGIC eMagic, LPCVOID lpBuff, uint32 u32BytesToWrite, uint32 u32TimeOut);

    SPRESULT LoadProductInfo(LPVOID  lpBuff, uint32 u32BytesToRead, uint32 u32TimeOut);
    SPRESULT SaveProductInfo(LPCVOID lpBuff, uint32 u32BytesToWrite, uint32 u32TimeOut);

    /// R/W Customer data of miscdata
    /*
        0 - 4K              PhaseCheck
        4K - 8K             root recorder£¨ÊÇ·ñroot¹ý£©
        8K~8K+512Byte       Verified Boot¹¦ÄÜ
        512K~516K           ADCÊý¾Ý´æ´¢
        517K~517K+1K        ±£´æpacµÄ´´½¨Ê±¼ä
        768K- 1024K         ¿Í»§×Ô¶¨ÒåÇø                   <-- R/W
    */
    SPRESULT LoadMiscData(uint32 u32Offset, LPVOID  lpBuff, uint32 u32BytesToRead,  uint32 u32Timeout);
    SPRESULT SaveMiscData(uint32 u32Offset, LPCVOID lpBuff, uint32 u32BytesToWrite, uint32 u32TimeOut);

	SPRESULT Active_LDO_VDDWIFIPA(BOOL bActive);

    SPRESULT LoadSN(int nID, LPSTR  lpSN, uint32 u32SNLength);
    SPRESULT SaveSN(int nID, LPCSTR lpSN, uint32 u32SNLength);

    ///
    /// MMI CIT
    ///
    SPRESULT BBAutoTest(int nCmd, LPCVOID lpData, uint32 u32BytesToWrite, LPVOID lpBuff, uint32 u32BytesToRead, uint32 u32TimeOut);
  
    ///
    /// FM
    ///
    SPRESULT fmOpen(BOOL bOpen);
    SPRESULT fmSetVolume(int nVolume);
    SPRESULT fmGetTuneInfo( uint32 u32freq, LPFM_SIGNAL_PARAM_T lpInfo);
    SPRESULT fmSeekChannel( uint32 u32freq, BOOL bDirection, LPFM_SIGNAL_PARAM_T lpInfo);
    SPRESULT fmLoadRegister(uint32 u32Addr, uint32 u32Count, uint32* lpRegData);
    SPRESULT fmSaveRegister(uint32 u32Addr, uint32 u32Count, const uint32* lpRegData);

    ///
    /// DeepSleep & PowerOff
    ///
    SPRESULT DeepSleep(void);
    SPRESULT PowerOff (void);

    ///
    /// ADC
    ///
    SPRESULT GetADC(LPPC_ADC_T lpADC);

    ///
    /// SimLock
    ///
    SPRESULT simlockHashSign(LPBYTE lpSendBuffer, UINT32 nSendLen);
    SPRESULT simlockGetHash(LPBYTE lpRecvBuffer, UINT32 *nRecvLen);

    ///
    /// AP
    ///
    SPRESULT apADC(int nCmd, const uint32 input[MAX_AP_ADC_DATA_NUM], uint32 output[MAX_AP_ADC_DATA_NUM]);

    /// Charge
    SPRESULT apChargeCmd(PC_CHARGE_T* lpCharge);
    
	// TEE
	SPRESULT apTEE_SendDeployCmd(
        LPCVOID lpSendBuffer, UINT32 u32SendLen, 
        LPVOID  lpRecvBuffer, UINT32 u32RecvBufLen, UINT32 *pu32RecvLen
        );
    SPRESULT apTEE_LoadDeployState(UINT32* pu32State);

    // V1ÈÛË¿£ºENGPC·½Ê½ÈÛË¿£¬ÊÊÓÃÓÚSC7731C¡¢SC9820AµÈ
    SPRESULT SecureBootProgramV1(void);
    SPRESULT LoadSecureBootState(UINT32* pu32State);

    // V2ÈÛË¿: UBOOT·½Ê½
    virtual SPRESULT apSetUbootSecureBoot(void);
    virtual SPRESULT apExitProgramKey(short nParam);

    /// TSX data
    SPRESULT apSaveTsxData(const TSX_DATA* pTsxData);
    SPRESULT apLoadTsxData(TSX_DATA* pTsxData);
	SPRESULT apSaveTsxDataV2(const TSX_DATA_V2* pTsxData);
	SPRESULT apLoadTsxDataV2(TSX_DATA_V2* pTsxData);

	SPRESULT apSaveRawWcnData(LPCVOID pData, uint32 ulDataLen);
	SPRESULT apLoadRawWcnData(LPVOID pData, uint32 ulDataLen);

    /// MMI FLAG
    SPRESULT apReadMMI(PC_MMI_CIT_T* pMMICIT);
    SPRESULT apWriteMMI(const PC_MMI_CIT_T* pMMICIT);

	SPRESULT apSetBackLight(uint32 nTime);
	SPRESULT apSetPwrMode(uint32 nMode);
	SPRESULT apGetModuleInfo(LPSTR lpModule);
	SPRESULT apGetLCS(int32 *nRet);
	SPRESULT apSetRMA();
	SPRESULT apGetSocId(uint8* lpSocId, uint32 *nLen);

    // Bug896218
    SPRESULT apGetBatteryCapacity(uint32* pu32Capacity);

    // Bug963345
    SPRESULT GetWidevineKeyboxDeviceID(CHAR* lpBuff, UINT32 u32BufSize);

	//Bug 1205207 (SPCSS00634949) - [SL8541E][XUNRUI][L712_S1]ËÑ¹·¿Í»§ÐèÇóÍ¨¹ý¹¤¾ßÐ´Èëµ±Ç°Ê±¼ä£¬Ê¹²úÆ·³ö³§Ê±²»ÐèÒªÊ±¼äÍ¬²½Ê±¼ä¾ÍÊÇ×¼µÄ£¬ÇëÎÊ¿ÉÒÔÈçºÎÊµÏÖ 
	SPRESULT SetRTC(const tm& t);
	SPRESULT GetRTC(tm* p);

	//Bug 1203797 [TRANSSION]
	SPRESULT SP_API EnableDisplayLogo(BOOL bEnable);

	//Bug 1217688,²éÑ¯·ÖÇø(ÖÁÉÙÖ§³Ömiscdata/prodnv)´óÐ¡¡¢¶ÁÈ¡·ÖÇøÄÚÈÝ¡¢Ð´Èë·ÖÇøÊý¾Ý
	SPRESULT ReadBackupNvList(LPVOID lpBuff, uint32 *lpu32RecvLen);
	SPRESULT Read_l_fixnv(LPVOID lpBuff, uint32 *lpu32RecvLen, uint32 u32TimeOut = 0);
	SPRESULT MatchfixnvEx(uint8 *pBuff, uint32 u32_1fixnvLen, NVDATA_R_T *pNvdata);
	SPRESULT fixnvDumpEx(NVDATA_R_T *pNvdata);
	SPRESULT fixnvFullDumpEx(NVDATA_R_T *pNvdata);
	SPRESULT CountValidNvID(uint8 *pu8Buff, uint32 u32RecvLen, uint32 *u32nvCount);
	SPRESULT QueryPartitionSize(LPCSTR lpszPartitionName,	uint32 *lpu32RecvLen);
	SPRESULT ReadPartition(LPCSTR lpszPartitionName, LPVOID lpBuff, uint32 u32BytesToRead, uint32 u32TimeOut);
	SPRESULT WritePartition(LPCSTR lpszPartitionName,	LPCVOID lpBuff,	uint32 u32BytesToWrite,	uint32 u32TimeOut);
	SPRESULT ClearNvVerFlag(void);
	SPRESULT ClearAllCaliCrc(void);
    /// Extended Diag. Command: DIAG_EXTEND_CMD (93 = 0x5D)
    SPRESULT fixnvDump(LPCWSTR lpszDumpFile);   // Bug652794
    SPRESULT fixnvSync(LPCWSTR lpszSyncFile);   // Bug1107084
    SPRESULT apLogDump(uint32 u32type, LPCWSTR lpszDumpFile);

    // Bug1189339
    SPRESULT apSetupSSID(LPCSTR lpszSSID, LPCSTR lpszPSK);
    SPRESULT apSetupConnMode(INT nMode);

	///only support tinno
	SPRESULT apGetFTMResult(LPVOID lpBuff, uint32 ulBuffLen);
	SPRESULT apGetCustomerVersion(LPVOID lpBuff, uint32 ulBuffLen);
	SPRESULT apGetInternalVersion(LPVOID lpBuff, uint32 ulBuffLen);

	//////////////////////////////////////////////////////////////////////////
	///2730 afc
	SPRESULT apSetPowerMode(uint16 mode);
	SPRESULT apSetPwmMode(uint16 mode);
	SPRESULT apSetCDAC(uint16 CDAC);
	SPRESULT apAMPSelfCalibration(uint16& Amp_26M);
	SPRESULT apLoadPMICAFCData(PMIC_AFC_CALI_DATA_T &data);
	SPRESULT apSavePIMICAFCData(const PMIC_AFC_CALI_DATA_T data);
	SPRESULT apReadTSXTemperature(PMIC_AFC_TSX_TEMP_T &data);
	SPRESULT apRestorePMIC();
	///2730 afc
	//////////////////////////////////////////////////////////////////////////

	// audio
	SPRESULT audioStartRecord(SP_AUDIO_REC_T* pConfig);
	SPRESULT audioSaveRecordFile(LPCWSTR lpFilePath, DWORD dwTimeOut);

    SPRESULT Security_Read_SimlockNVData(uint8& pstrBuff, uint32& nBuffSizen);

	//LTE Modem V3
	SPRESULT ModemV3_Nv_Read( const PC_MODEM_RF_V3_DATA_REQ_CMD_T* nv, PC_MODEM_RF_V3_DATA_PARAM_T* pNvRlst );
	SPRESULT ModemV3_Nv_Write( const PC_MODEM_RF_V3_DATA_REQ_CMD_T* pNvReq, PC_MODEM_RF_V3_DATA_PARAM_T* pNvData );
	SPRESULT ModemV3_Nv_Save2Flash( const PC_MODEM_RF_V3_NV_SAVE_REQ_CMD_T* nv, PC_MODEM_RF_V3_NV_SAVE_RSP_RLST_T* pNvRlst );

	SPRESULT ModemV3_Common_Save2Flash();
	SPRESULT ModemV3_lte_Save2Flash();

    SPRESULT ModemV4_Save_AU_CompRSRP(NST_LTE_AU_RSRP_REQ_T* req);

    SPRESULT ModemV4_Set_RSRPCalStatus(uint16 status);

protected:
    virtual void WakeupAPResponse(PRT_BUFF* lpBuff);
    virtual void WakeupAudioRecordResponse(PRT_BUFF* lpBuff);
	
private:
    /// SP05 & SP09 R/W production area
    SPRESULT SP05_LoadProdInfo(void* lpBuff, uint32 u32BytesToRead, uint32 u32Offset, uint32 u32TimeOut);
    SPRESULT SP05_SaveProdInfo(const void* lpBuff, uint32 u32BytesToWrite, uint32 u32Offset, uint32 u32TimeOut);
    SPRESULT SP09_LoadProdInfo(void* lpBuff, uint32 u32BytesToRead, uint32 u32TimeOut);
    SPRESULT SP09_SaveProdInfo(const void* lpBuff, uint32 u32BytesToWrite, uint32 u32TimeOut);

    /// IMEI & BT & WIFI string/NV convert 
    void     IMEI_Str2NV(const BYTE szImei[MAX_IMEI_STR_LENGTH], BYTE nvImei[MAX_IMEI_NV_LENGTH]);
    SPRESULT IMEI_NV2Str(const BYTE nvImei[MAX_IMEI_NV_LENGTH],  BYTE szImei[MAX_IMEI_STR_LENGTH]);
    void     BTAddr_Str2NV  (const BYTE szAddr[MAX_BT_ADDR_STR_LENGTH],   BYTE nvAddr[MAX_BT_ADDR_NV_LENGTH]);
    void     WIFIAddr_Str2NV(const BYTE szAddr[MAX_WIFI_ADDR_STR_LENGTH], BYTE nvAddr[MAX_WIFI_ADDR_NV_LENGTH]);

    /// MEID
    void     MEID_NV2STR(const BYTE nvValue[MAX_GEID_NV_LENGTH],  CHAR szValue[MAX_GEID_STR_LENGTH]);
    void     MEID_STR2NV(const CHAR szValue[MAX_GEID_STR_LENGTH], BYTE nvValue[MAX_GEID_NV_LENGTH]);
    void     MEID_NV2STR14digits(const BYTE nvValue[MAX_GEID_NV_LENGTH], CHAR szValue[MAX_GEID_STR_LENGTH]);
    SPRESULT exCmdDumpDUTFile(uint8 cmd, const DIAG_FILE_DUMP_REQ_T& req, LPCWSTR lpszDumpFile, UINT32 u32TimeOut = 0);
    // Verify l_fixnv1 and add the padding FF FF ...  
    SPRESULT VerifyNvItemFile(LPCWSTR lpszDumpFile, BOOL bCheckID = FALSE, BOOL bPaddingTail = TRUE);

    // ENGPC Secure boot 
    SPRESULT engpcSecureBootProgramHash(void);
    SPRESULT engpcSecureBootEnable(void);

    SPRESULT InitPhaseCheck(LPCSTR lpszSN1, LPCSTR lpszSN2);

    SPRESULT apTEEConvertReturnCode(UINT nCode);
};
