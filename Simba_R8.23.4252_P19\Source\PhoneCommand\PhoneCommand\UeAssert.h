#pragma once
#include "global_def.h"
#include "LogFile.h"
#include "Utility.h"
class CDiagBase;

//////////////////////////////////////////////////////////////////////////
class CUeAssert
{
public:
    CUeAssert(CDiagBase* pDiagBase);
    virtual ~CUeAssert(void);

    /*
        Reset to normal state
    */
    void Reset(void);

    /*
        Return DUT is asserted or not
    */
    BOOL IsAsserted(void)const;


    /*
        Terminal dump process 
    */
    void TerminalDump(INT nDumpErrCode);

    
    /*
        Wait for memory dump process finish
    */
    BOOL WaitForDumpFinish(UINT32 u32TimeOut);

    /*
        Assert package is received
    */
    virtual void OnAssertReceived(LPCVOID lpData, UINT32 u32Size);

private:
    LPCTSTR SaveAssInfoIntoFile(LPCSTR lpString, UINT32 nSize);

    /*
        Stop dumping 
    */
    void StopDump(void);
    


private:
    CDiagBase*       m_pDiagBase;

    BOOL             m_bAsserted;
    DUT_ASSERT_STATE m_eState;
    HANDLE           m_hDumpFinishEvent;
    std::string      m_strAssInfo;
    CLogFile         m_DumpFile;

    CSPTimer         m_DumpTimer;
};

inline BOOL CUeAssert::IsAsserted(void)const
{
    return m_bAsserted;
}

inline void CUeAssert::StopDump(void)
{
    if (m_bAsserted && NULL != m_hDumpFinishEvent)
    {
        SetEvent(m_hDumpFinishEvent);
    }
}