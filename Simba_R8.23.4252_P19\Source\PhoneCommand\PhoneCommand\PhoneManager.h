#pragma once
#include "DiagPhone.h"
#include "DevHound\DevHound.h"
#include "CSimpleLock.h"
#include <map>
#define PRINTFUNCTIONNAME    SP_PhoneCommand_LogFmtStrA(hDiagPhone, SPLOG_LEVEL::SPLOGLV_INFO, "%s", __FUNCTION__);
typedef std::map<SP_HANDLE, CDiagPhone* > CMapPhone;
//////////////////////////////////////////////////////////////////////////
/// 
class CPhoneManager
{
public:
     CPhoneManager(void);
    ~CPhoneManager(void);

    SP_HANDLE   Create(LPVOID  lpLogUtil);
    void        Free(SP_HANDLE hDiagPhone);   
    CDiagPhone* Find(SP_HANDLE hDiagPhone);

private:
    CMapPhone   m_mapPhone;
    CDevHound   m_DevHound;
    long        m_refCount;
    CSimpleLock     m_Lock;
};
