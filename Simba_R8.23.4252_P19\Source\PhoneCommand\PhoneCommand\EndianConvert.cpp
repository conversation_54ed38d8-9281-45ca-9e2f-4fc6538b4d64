#include "StdAfx.h"
#include "EndianConvert.h"
#include <assert.h>

//////////////////////////////////////////////////////////////////////////
CEnianConvert::CEnianConvert(void)
: m_eEndianType(SP_LITTLE_ENDIAN)
{
}

CEnianConvert::~CEnianConvert(void)
{
}

uint8* CEnianConvert::Conv16(uint8* lpData, uint32 u32Size)
{
    if (SP_LITTLE_ENDIAN == m_eEndianType)
    {
        return lpData;
    }

    if ((NULL == lpData) || (0 == u32Size) || (0 != u32Size % 2))
    {
        assert(0);
        return NULL;
    }

    uint8 tmp = 0;
    for (uint32 i=0; i<u32Size; i+=2)
    {
        tmp         = lpData[i];
        lpData[i]   = lpData[i+1];
        lpData[i+1] = tmp;
    } 

    return lpData;
}

uint8* CEnianConvert::Conv32(uint8* lpData, uint32 u32Size)
{
    if (SP_LITTLE_ENDIAN == m_eEndianType)
    {
        return lpData;
    }

    if ((NULL == lpData) || (0 == u32Size) || (0 != u32Size % 4))
    {
        assert(0);
        return NULL;
    }

    uint8 tmp = 0;
    for (uint32 i=0; i<u32Size; i+=4)
    {
        tmp         = lpData[i];
        lpData[i]   = lpData[i+3];
        lpData[i+3] = tmp;

        tmp         = lpData[i+1];
        lpData[i+1] = lpData[i+2];
        lpData[i+2] = tmp; 
    } 

    return lpData;
}
