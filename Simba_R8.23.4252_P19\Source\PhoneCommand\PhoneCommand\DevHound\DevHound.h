#pragma once

#include <vector>
#include <map>
#include "IDevMonitor.h"
#include "OBSList.h"
#include "Thread.h"
#include "ISpLogExport.h"
#include "Tr.h"

//////////////////////////////////////////////////////////////////////////
/// 
class CDevHound : public COBSList<IDevMonitor>
                , public CThread
                , public CTr
{
public:
    CDevHound(void);
    virtual ~CDevHound(void);

    BOOL  Start(UINT nLogLv);
    void  Stop(void);
    
    virtual void run(void);

private:
    bool  CheckPortInfo(SPDBT_INFO& epi);
    bool  InsertPortInfo(SPDBT_INFO& epi ,bool bInit);	
    SPDBT_TYPE CheckDeviceClass(WCHAR* lpszClass);
    void  SendToUplayer(SPDBT_INFO& epi);
    void  ClearState(void);
	BOOL GetUsbInfo(DWORD DevInst, WCHAR szLocationPath[SPDBT_NAME_MAX_LEN]);

private:
    std::vector<SPDBT_INFO>  m_vPortInfo;
    std::map<unsigned int, SPDBT_INFO> m_mPortInfo;
    volatile BOOL   m_bStopWork;
    static LPCWSTR  PORT_CLASS_NAME;
    static LPCWSTR  MODEM_CLASS_NAME;    
};
