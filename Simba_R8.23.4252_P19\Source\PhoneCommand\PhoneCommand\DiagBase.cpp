﻿#include "StdAfx.h"
#include "DiagBase.h"
#include "phdef.h"
#include "FileOBS.h"
#include "SocketOBS.h"
#include "OBSList.h"
#include "AppSettings.h"
#include "AppVer.h"
#include "CLocks.h"
#include <Shlwapi.h>

extern std::wstring GetAppPath(void);
//////////////////////////////////////////////////////////////////////////
#define IsDeviceOpen() \
    { \
        if (!m_bDeviceOpen) \
        { \
            LogRawStrA(SPLOGLV_ERROR, "Device is not opened.");  \
            return SP_E_PHONE_DEVICE_NOT_OPEN; \
        } \
    }

///
void CALLBACK PrintSendBufData(LPCVOID lpData, UINT uSize, LONG reserved)
{
	CDiagBase* lpThis = (CDiagBase*)reserved;
	if (NULL != lpThis && NULL != lpData && uSize > 0)
	{
		lpThis->LogBufData(SPLOGLV_DATA, lpData, uSize, LOG_WRITE, NULL);
	}
}

void CALLBACK PrintChannelErrWarnLog(LPCSTR lpszTrace, UINT32 u32Lv, LPVOID lParam)
{
	CDiagBase* lpThis = (CDiagBase*)lParam;
	if (NULL != lpThis && NULL != lpszTrace && (u32Lv == SPLOGLV_ERROR || u32Lv == SPLOGLV_WARN))
	{
		lpThis->GetISpLogObject()->LogRawStrA(u32Lv, lpszTrace, "Channel");
	}
}

//////////////////////////////////////////////////////////////////////////
CCallBackMonitor::CCallBackMonitor()
{
	m_cbDevChange = NULL;
	m_cbDevAssert = NULL;
	m_lpDevChangeUser = NULL;
	m_lpDevAssertUser = NULL;
}

CCallBackMonitor::~CCallBackMonitor()
{
}

void CCallBackMonitor::OnDevChange(INT nEvent, LPCVOID lpData)
{
	if (NULL != m_cbDevChange)
	{
		m_cbDevChange(nEvent, lpData, m_lpDevChangeUser);
	}
}

BOOL CCallBackMonitor::OnDevAssert(DUT_ASSERT_STATE eState, LPCVOID lpData)
{
	if (NULL != m_cbDevAssert)
	{
		return m_cbDevAssert(eState, lpData, m_lpDevAssertUser);
	}
	return TRUE;
}

//////////////////////////////////////////////////////////////////////////
CDiagBase::CDiagBase(void)
	: m_lpDiagChannel(NULL)
	, m_bDeviceOpen(FALSE)
	, m_dwTimeOut(TIMEOUT_3S)
	, m_dwMagicNumber(SP09_SPPH_MAGIC_NUMBER)
	, m_bStartAysnRecving(FALSE)
	, m_hWakeUpEvent(NULL)
	, m_hDumpStopEvent(NULL)
	, m_hBootUpEvent(NULL)
	, m_lpAssert(NULL)
	, m_lpMonitor(NULL)
	, m_bSaveLogel(FALSE)
	, m_pContainer(NULL)
	, m_nLogPort(0)
	, m_hCapture(NULL)
	, m_bLogUsingOtherPort(FALSE)
	, m_bStartModuleCNRRecving(FALSE)
	, m_bAuthMode(FALSE)
{
	ZeroMemory((void*)m_diagBuff, sizeof(m_diagBuff));
	ZeroMemory((void*)& m_ca, sizeof(m_ca));
	m_lpMonitor = &m_CallBackMonitor;
}

CDiagBase::~CDiagBase(void)
{
	if (NULL != m_hCapture)
	{
		LogFmtStrA(SPLOGLV_VERBOSE, "ReleasePortCapture");
		ReleasePortCapture(m_hCapture);
	}
}

int CDiagBase::OnChannelData(LPVOID lpData, ULONG /*reserved*/)
{
	PRT_BUFF* lpBuff = static_cast<PRT_BUFF*>(lpData);
	if (NULL == lpBuff)
	{
		return 0;
	}

	DIAG_HEADER* lpHD = reinterpret_cast<DIAG_HEADER*>(lpBuff->lpData);
	if (NULL != lpHD)
	{
		if (RM_COMMAND_T == lpHD->type && RM_RUNMODE_REQ_COMMAND == lpHD->subtype)
		{
			/// 7E 00 00 00 00 08 00 FE 01 7E
			LogRawStrA(SPLOGLV_INFO, "UART BOOT-UP package is received!");
			SetEvent(m_hBootUpEvent);
		}
		else if (MSG_TYPE_ASSERT == lpHD->type)
		{
			if (NULL != m_lpAssert)
			{
				// +++ SPCSS00386666，Bug685084 严格判定ASSERT头几个包，但有可能会漏检测
				if (!m_lpAssert->IsAsserted())
				{
					/*
						Bug1190220
						subtype不止是0，也有可能是0～8，
						目前len是因为历史原因填充了8，但也有可能被修正成正确的长度，所以此处也要判断len为正确的值也是assert，subtype可以判断一个范围[0,8].
					*/
					// if (!(0x00 == lpHD->subtype && 0x08 == lpHD->len))
					if (lpHD->subtype > 0x08)
					{
						lpBuff->free_prt(lpBuff);
						return 1;
					}
				}
				// ---

				m_lpAssert->OnAssertReceived(lpBuff->lpData, lpBuff->size);
			}
		}
		else
		{
			if (m_bStartAysnRecving)
			{
				Dispatching(lpHD, lpBuff);
			}
		}
	}

	lpBuff->free_prt(lpBuff);
	return 1;
}

void CDiagBase::Dispatching(LPCDIAG_HEADER lpHead, PRT_BUFF* lpBuff)
{
	int nCondCount = m_recvPkgList.GetCondCount();
	for (int i = 0; i < nCondCount; i++)
	{
		LPCDIAG_HEADER lpCond = m_recvPkgList.GetCondition(i);
		if (NULL == lpCond)
		{
			assert(0);
			continue;
		}

		if (CompareCond(lpCond, lpHead))
		{
			if (0x3A == lpCond->type && 0x05 == lpCond->subtype)
			{
				///Bug 1732369 GE3F内存小，CNR的计算放到工具侧	
				///Diag上报类型 Type = 0x3A，Subtype = 0x5
				///第1包数据内容：DATA_CAPTURE_BEGIN
				///第2~第5633包数据：每包4个unsigned int数据，共22528个。转换为ASIC格式发送，收到后，合并为unsigned int处理
				///最后1包数据：DATA_CAPTURE_END
				/// 				
				WakeupModuleCNRResponse(lpBuff);
				if (m_bStartModuleCNRRecving)
				{
					LogBufData(SPLOGLV_DATA, lpBuff->lpData, lpBuff->size, LOG_ASYNC_READ);

					m_recvPkgList.AddPackage(lpBuff);
				}
			}
			else
			{
				LogBufData(SPLOGLV_DATA, lpBuff->lpData, lpBuff->size, LOG_ASYNC_READ);

				m_recvPkgList.AddPackage(lpBuff);

				if (PPP_PACKET_A == lpCond->type && ZERO_SUBTYPE == lpCond->subtype)
				{
					WakeupATResponse(lpBuff);
				}
				else if (lpHead->type == DIAG_DEVICE_AUTOTEST_F)
				{
					WakeUpOnResponse();
				}
				else if (DIAG_AP_F == lpCond->type && ZERO_SUBTYPE == lpCond->subtype)
				{
					// 只有当读文件时候才一发多收，在__apLoadFile函数内设置CRecvPkgsList(bRecvMultiPkgs = TRUE)
					if (m_recvPkgList.IsRecvMultiPkgs())
					{
						WakeupAPResponse(lpBuff);
					}
					else
					{
						WakeUpOnResponse();
					}
				}
				else if (DIAG_AUDIO_F == lpCond->type && DIAG_AUDIO_PULL_RECORD_DATA == lpCond->subtype)
				{
					WakeupAudioRecordResponse(lpBuff);
				}
				else
				{
					if (m_recvPkgList.GetPkgsCount() >= nCondCount)
					{
						WakeUpOnResponse();
					}
				}
			}

			break; /// OK, we found the belonging
		}
	}
}

int CDiagBase::OnChannelEvent(unsigned int event, void* lpEventData)
{
	if (PP_EVENT_BIG_ENDIAN == event)
	{
		m_EndianConvert.SetEndian(SP_BIG_ENDIAN);
		LogRawStrA(SPLOGLV_INFO, "Big endian event received!");
	}
	else if (PP_EVENT_LITTLE_ENDIAN == event)
	{
		m_EndianConvert.SetEndian(SP_LITTLE_ENDIAN);
		LogRawStrA(SPLOGLV_INFO, "Little endian event received!");
	}

	PRT_BUFF* lpBuff = static_cast<PRT_BUFF*>(lpEventData);
	if (NULL != lpBuff)
	{
		lpBuff->free_prt(lpBuff);
	}

	return 1;
}

BOOL CDiagBase::Startup(ISpLog* lpTr)
{
	Cleanup();

	/// Trace
	UINT nTrLv = SPLOGLV_ERROR;
	CAppSettings::GetInstance().GetValue(CAppSettings::LogLevel, (LPVOID)& nTrLv);
	trInit(lpTr, "DUT", nTrLv, TRUE);

	/*
	extern HMODULE g_hModule;
	CAppVer VerObj;
	VerObj.Init(g_hModule);
	LogFmtStrW(SPLOGLV_INFO, L"%s,\t Version:%s, Vendor: %s", VerObj.GetFileName(), VerObj.GetVersion(), VerObj.GetVendor());
	*/

	/// TimeOut
	CAppSettings::GetInstance().GetValue(CAppSettings::TimeOut, (LPVOID)& m_dwTimeOut);
	CAppSettings::GetInstance().GetValue(CAppSettings::MaxTimeout, (LPVOID)& m_MaxTimeout);
	CAppSettings::GetInstance().GetValue(CAppSettings::MinTimeout, (LPVOID)& m_MinTimeout);
	CAppSettings::GetInstance().GetValue(CAppSettings::SaveLogelData, (LPVOID)& m_bSaveLogel);
	CAppSettings::GetInstance().GetValue(CAppSettings::AuthMode, (LPVOID)& m_bAuthMode);
	CAppSettings::GetInstance().GetValue(CAppSettings::WaitTimeLogPort, (UINT*)& m_nWaitTimeLogPort);

	if (m_dwTimeOut <= 0 || m_MaxTimeout <= 0)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid Timeout", __FUNCTION__);
		return FALSE;
	}

	if (m_MaxTimeout < m_MinTimeout)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: MaxTimeout < MinTimeout", __FUNCTION__);
		m_MaxTimeout = m_MinTimeout;
	}

	m_hWakeUpEvent = CreateEvent(NULL, TRUE, FALSE, NULL);
	if (NULL == m_hWakeUpEvent)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: CreateEvent(m_hWakeUpEvent) failed, WinErr = %d", __FUNCTION__, ::GetLastError());
		return FALSE;
	}

	m_hDumpStopEvent = CreateEvent(NULL, TRUE, FALSE, NULL);
	if (NULL == m_hDumpStopEvent)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: CreateEvent(m_hDumpStopEvent) failed, WinErr = %d", __FUNCTION__, ::GetLastError());
		return FALSE;
	}
	m_hBootUpEvent = CreateEvent(NULL, TRUE, FALSE, NULL);
	if (NULL == m_hBootUpEvent)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: CreateEvent(m_hBootUpEvent) failed, WinErr = %d", __FUNCTION__, ::GetLastError());
		return FALSE;
	}

	/// Create DIAG. channel object 
	if (!CreateDiagChannel(&m_lpDiagChannel)
		|| NULL == m_lpDiagChannel
		)
	{
		LogRawStrA(SPLOGLV_ERROR, "Create DIAG. channel object failed!");
		return FALSE;
	}

	m_lpDiagChannel->SetProperty((LONG)this, PPI_WRITEDATA_CALLBACK, (LPCVOID)PrintSendBufData);

	UINT nEndian = SP_LITTLE_ENDIAN;
	CAppSettings::GetInstance().GetValue(CAppSettings::Endian, (LPVOID)& nEndian);
	SetProperty(SP_ATTR_ENDIAN, 0, (LPVOID)nEndian);

	try
	{
		m_lpAssert = new CUeAssert(this);
	}
	catch (const std::bad_alloc& /*e*/)
	{
		m_lpAssert = NULL;
	}
	if (NULL == m_lpAssert)
	{
		LogRawStrA(SPLOGLV_ERROR, "Create assert object failed!");
		return FALSE;
	}

	return RegisterObservers();
}

void CDiagBase::Cleanup(void)
{
	if (NULL != m_lpDiagChannel)
	{
		CleanUpObservers();

		ReleaseDiagChannel(m_lpDiagChannel);
		m_lpDiagChannel = NULL;
	}

	if (NULL != m_hWakeUpEvent)
	{
		CloseHandle(m_hWakeUpEvent);
		m_hWakeUpEvent = NULL;
	}

	if (NULL != m_hDumpStopEvent)
	{
		CloseHandle(m_hDumpStopEvent);
		m_hDumpStopEvent = NULL;
	}

	if (NULL != m_hBootUpEvent)
	{
		CloseHandle(m_hBootUpEvent);
		m_hBootUpEvent = NULL;
	}

	if (NULL != m_lpAssert)
	{
		delete  m_lpAssert;
		m_lpAssert = NULL;
	}

	LogRawStrA(SPLOGLV_VERBOSE, "DiagPhone is cleaned up.");

	trFree();
}

SPRESULT CDiagBase::SetProperty(INT nOption, INT /*nFlag*/, LPCVOID lpValue)
{
	switch (nOption)
	{
	case SP_ATTR_TIMEOUT:
	{
		m_dwTimeOut = (DWORD)lpValue;
		LogFmtStrA(SPLOGLV_INFO, "SetTimeOut = %d ms", m_dwTimeOut);
	}
	break;

	case SP_ATTR_ENDIAN:
	{
		SP_ENDIAN_TYPE eType = SP_ENDIAN_TYPE((DWORD)lpValue);
		m_EndianConvert.SetEndian(eType);

		if (SP_LITTLE_ENDIAN == eType)
		{
			m_lpDiagChannel->SetProperty(0, PPI_Endian, (void*)((PP_LITTLE_ENDIAN << 8) | PP_LITTLE_ENDIAN));
		}
		else
		{
			m_lpDiagChannel->SetProperty(0, PPI_Endian, (void*)((PP_LITTLE_ENDIAN << 8) | PP_BIG_ENDIAN));
		}

		LogFmtStrA(SPLOGLV_INFO, "Set Endian = %d", eType);
	}
	break;

	case SP_ATTR_MAGIC_NUMBER:
	{
		m_dwMagicNumber = (DWORD)lpValue;
	}
	break;

	case SP_ATTR_LOGEL_FILE:
	{
		m_bSaveLogel = (BOOL)lpValue;
	}
	break;

	case SP_ATTR_SOCKET_SETTING:
	{
		SOCKET_SETTING cs = *((SOCKET_SETTING*)lpValue);
		if (cs == m_SocketSetting)
		{
			return SP_OK;
		}

		CSocketObserver* pObs = (CSocketObserver*)m_obsList[OBS_SOCKET].obs;
		if (NULL == pObs)
		{
			LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid socket observer!", __FUNCTION__);
			assert(0);
			return SP_E_PHONE_POINTER;
		}

		if (cs.bConnect)
		{
			if (!pObs->Start(cs.szIP, cs.dwPort))
			{
				LogFmtStrA(SPLOGLV_ERROR, "Connect IP <%s : %d> failed!", cs.szIP, cs.dwPort);
				return SP_E_PHONE_OBSERVER_START;
			}
		}
		else
		{
			pObs->Stop();
		}

		m_SocketSetting = cs;
	}
	break;
	case SP_ATTR_CONTAINER:
		m_pContainer = (IContainer*)lpValue;
		break;

	case SP_ATTR_TRACE_LEVEL:
	{
		ISpLog* pLogUtil = GetISpLogObject();
		if (NULL != pLogUtil)
		{
			pLogUtil->SetProperty(LogProp_Level, 0, lpValue);
		}
	}
	break;

	default:
		break;
	}

	return SP_OK;
}

SPRESULT CDiagBase::GetProperty(INT nOption, INT /*nFlag*/, LPVOID lpValue)
{
	switch (nOption)
	{
	case SP_ATTR_TIMEOUT:
		*((DWORD*)lpValue) = m_dwTimeOut;
		break;

	case SP_ATTR_ENDIAN:
		*((DWORD*)lpValue) = m_EndianConvert.GetEndian();
		break;

	case SP_ATTR_MAGIC_NUMBER:
		*((DWORD*)lpValue) = m_dwMagicNumber;
		break;

	case SP_ATTR_DBT_INFO:
		*((SPDBT_INFO*)lpValue) = m_DBT;
		break;

	case SP_ATTR_CHANNEL_ATTR:
		*((CHANNEL_ATTRIBUTE*)lpValue) = m_ca;
		break;

	default:
		break;
	}

	return SP_OK;
}

SPRESULT CDiagBase::Open(PCCHANNEL_ATTRIBUTE lpArg)
{
	if (NULL == lpArg)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid parameters!", __FUNCTION__);
		assert(0);
		return SP_E_PHONE_INVALID_PARAMETER;
	}
	else
	{
		m_ca = *lpArg;
		m_lpAssert->Reset();
	}

	switch (lpArg->ChannelType)
	{
	case CHANNEL_TYPE_USBMON:
	{
		return SP_OK;
	}
	break;

	case CHANNEL_TYPE_COM:
	{
		LogFmtStrA(SPLOGLV_INFO, "Open UART: port = %d, %d bps.", m_ca.Com.dwPortNum, m_ca.Com.dwBaudRate);
	}
	break;
	case CHANNEL_TYPE_SOCKET:
	{
		LogFmtStrA(SPLOGLV_INFO, "Open Socket: IP = %d, Port = %d.", m_ca.Socket.dwIP, m_ca.Socket.dwPort);
	}
	break;

	default:
		LogFmtStrA(SPLOGLV_ERROR, "UnSupported channel %d!", lpArg->ChannelType);
		return SP_E_PHONE_INVALID_PARAMETER;
		break;
	}

	if (m_bSaveLogel)
	{
		WCHAR szLogelFile[MAX_PATH] = { 0 };
		if (NULL != GetISpLogObject())
		{
			WCHAR szLogPath[MAX_PATH] = { 0 };
			GetISpLogObject()->GetProperty(LogProp_LogFilePath, MAX_PATH, (LPVOID)szLogPath);
			PathRemoveFileSpec(szLogPath);
			wcscpy_s(szLogelFile, szLogPath);
		}
		else
		{
			wcsncpy_s(szLogelFile, GetAppPath().c_str(), MAX_PATH - 1);
		}

		wcscat_s(szLogelFile, L"\\Arm.Logel");

		if (m_pContainer != NULL)
		{
			m_pContainer->GetValue(InternalReservedShareMemory_LogUsingOtherPort, (LPVOID)& m_bLogUsingOtherPort, sizeof(m_bLogUsingOtherPort));
		}

		if (!m_bLogUsingOtherPort)
		{
#if defined (UNICODE) ||  defined (_UNICODE)
			((CFileObserver*)m_obsList[OBS_FILE].obs)->Start(szLogelFile);
#else
			CHAR szFile[MAX_PATH] = { 0 };
			WideCharToMultiByte(CP_ACP, 0, szLogelFile, -1, szFile, MAX_PATH, 0, 0);
			((CFileObserver*)m_obsList[OBS_FILE].obs)->Start(szFile);
#endif
		}

	}

	m_bDeviceOpen = m_lpDiagChannel->Open(&m_ca);
	if (!m_bDeviceOpen)
	{
		LogRawStrA(SPLOGLV_ERROR, "Port open failed!");
		return SP_E_PHONE_OPEN_DEVICE;
	}

	ICommChannel* pLowChannel = GetLowChannel(FALSE);
	if (NULL != pLowChannel)  // Bug865535 
	{
		TRACECALLBACK_T cb;
		cb.lpFunc = PrintChannelErrWarnLog;
		cb.lParam = this;
		pLowChannel->SetProperty(0, CH_PROP_TRACE_CALLBACK, (LPCVOID)& cb);
	}
	return SP_OK;
}

void CDiagBase::Close(void)
{
	LogFmtStrA(SPLOGLV_VERBOSE, "Closing port, type = %d", m_ca.ChannelType);
	CLocks Lock(m_Lock);    // CDevMode::OnDevChange will invoke Close function 

	if (!m_bDeviceOpen)
	{
		LogRawStrA(SPLOGLV_INFO, "Close: device is not open");
		return;
	}

	if (NULL != m_lpAssert && m_lpAssert->IsAsserted())
	{
		LogRawStrA(SPLOGLV_INFO, "Close: device is crashed, waiting dump finished.");
		m_lpAssert->WaitForDumpFinish(INFINITE);
	}

	if (CHANNEL_TYPE_USBMON != m_ca.ChannelType)
	{
		Clear();
		m_lpDiagChannel->Close();
	}

	m_bDeviceOpen = FALSE;

	if (m_bSaveLogel)
	{
		if (m_bLogUsingOtherPort && NULL != m_hCapture)
		{
			StopCapturePort(m_hCapture);
			/* ReleasePortCapture(m_hCapture);
			 m_hCapture = NULL;*/
		}
		else
		{
			((CFileObserver*)m_obsList[OBS_FILE].obs)->Stop();
		}

	}
	LogRawStrA(SPLOGLV_VERBOSE, "Closed");
}

uint32 CDiagBase::Write(LPCVOID lpData, uint32 u32BytesToSend, BOOL bCheckAssert /*= TRUE*/)
{
	IsDeviceOpen();

	if (bCheckAssert && m_lpAssert->IsAsserted())
	{
		m_lpAssert->WaitForDumpFinish(INFINITE);
		return SP_E_PHONE_ASSERTED;
	}

	if (NULL == lpData || 0 == u32BytesToSend)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid parameters (NULL)!", __FUNCTION__);
		assert(0);
		return SP_E_PHONE_INVALID_PARAMETER;
	}

	ICommChannel* lpChannel = GetLowChannel(FALSE);
	if (NULL == lpChannel)
	{
		LogRawStrA(SPLOGLV_ERROR, "Invalid lower physical channel object!");
		return SP_E_PHONE_POINTER;
	}

	/// Write raw data 
	LogBufData(SPLOGLV_DATA, lpData, (DWORD)u32BytesToSend, LOG_WRITE);
	return (uint32)lpChannel->Write((LPVOID)lpData, (DWORD)u32BytesToSend);
}

uint32 CDiagBase::Read(LPVOID lpBuff, uint32 u32BytesToRead, uint32 u32TimeOut)
{
	IsDeviceOpen();

	if (m_lpAssert->IsAsserted())
	{
		m_lpAssert->WaitForDumpFinish(INFINITE);
		return SP_E_PHONE_ASSERTED;
	}

	if (NULL == lpBuff || 0 == u32BytesToRead)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid parameters (NULL)!", __FUNCTION__);
		assert(0);
		return SP_E_PHONE_INVALID_PARAMETER;
	}

	ICommChannel* lpChannel = GetLowChannel(FALSE);
	if (NULL == lpChannel)
	{
		LogRawStrA(SPLOGLV_ERROR, "Invalid lower physical channel object!");
		return SP_E_PHONE_POINTER;
	}

	/// Read raw data 
	DWORD dwBytesRead = lpChannel->Read(lpBuff, (DWORD)u32BytesToRead, (DWORD)u32TimeOut);
	if (dwBytesRead > 0)
	{
		LogBufData(SPLOGLV_DATA, (const void*)lpBuff, dwBytesRead, LOG_READ, &u32BytesToRead);
	}
	else
	{
		LogRawStrA(SPLOGLV_INFO, "0 bytes read.");
	}

	return (uint32)dwBytesRead;
}

SPRESULT CDiagBase::SendAndRecvRawPackage(
	const void* lpSendBuf, uint32 u32BytesToSend,
	void* lpBuff, uint32 u32BufSize, uint32* lpu32RecvBytes,
	DWORD dwTimeOut /* = TIMEOUT_3S */)
{

	if (NULL == lpSendBuf || 0 == u32BytesToSend)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid parameters (NULL)!", __FUNCTION__);
		assert(0);
		return SP_E_PHONE_INVALID_PARAMETER;
	}

	ICommChannel* lpChannel = GetLowChannel(FALSE);
	if (NULL == lpChannel)
	{
		LogRawStrA(SPLOGLV_ERROR, "Invalid lower physical channel object!");
		return SP_E_PHONE_POINTER;
	}

	lpChannel->SetReceiver(0, FALSE, NULL);
	Sleep(200);
	//lpChannel->Clear();

	/// Write raw data 
	LogBufData(SPLOGLV_DATA, lpSendBuf, (DWORD)u32BytesToSend, LOG_WRITE);
	DWORD dwBytesRead = lpChannel->Write((LPVOID)lpSendBuf, (DWORD)u32BytesToSend);
	if (dwBytesRead != (DWORD)u32BytesToSend)
	{
		LogRawStrA(SPLOGLV_ERROR, "Invalid lower physical channel object!");
		return SP_E_PHONE_SEND_DATA;
	}

	if (NULL == lpBuff || 0 == u32BufSize)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid parameters (NULL)!", __FUNCTION__);
		assert(0);
		return SP_E_PHONE_INVALID_PARAMETER;
	}

	/// Read raw data 
	int nLoop = 0;
	do 
	{
		DWORD dwBytesRead = lpChannel->Read(lpBuff, (DWORD)u32BufSize, (DWORD)dwTimeOut);
		if (dwBytesRead > 0)
		{
			*lpu32RecvBytes = dwBytesRead;
			LogBufData(SPLOGLV_DATA, (const void*)lpBuff, dwBytesRead, LOG_READ, &u32BufSize);
			break;
		}
		else
		{
			*lpu32RecvBytes = 0;
			LogFmtStrA(SPLOGLV_INFO, "0 bytes read loop %d.", nLoop + 1);
		}
		nLoop++;
	} while (nLoop < 3);

	return SP_OK;
}


BOOL CDiagBase::UnpackPRT(LPCPRT_BUFF lpOrg, DIAG_HEADER* lpHD, void* lpBuff, uint32 u32BufSize, uint32* lpUnpackedSize)
{
	if (NULL == lpOrg)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid parameters!", __FUNCTION__);
		assert(0);
		return FALSE;
	}

	if (NULL != lpBuff && u32BufSize > 0)
	{
		memset(lpBuff, 0, u32BufSize);
	}

	DIAG_PACKAGE* lpPackage = reinterpret_cast<DIAG_PACKAGE*>(lpOrg->lpData);
	assert(NULL != lpPackage);

	/// Header
	if (NULL != lpHD)
	{
		*lpHD = lpPackage->header;
	}

	if (lpOrg->size < sizeof(DIAG_HEADER))
	{
		// Invalid DIAG package, this cannot happen, because DiagChan has guaranteed. 
		LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid response package, Length = %d", __FUNCTION__, lpOrg->size);
		assert(0);
		return FALSE;
	}

	/// Data 
	uint32 u32pkgSize = lpOrg->size - sizeof(DIAG_HEADER);
	if (NULL != lpUnpackedSize)
	{
		*lpUnpackedSize = u32pkgSize;
	}

	if (u32pkgSize > 0 && NULL != lpBuff)
	{
		if (u32BufSize >= u32pkgSize)
		{
			memcpy(lpBuff, &lpPackage->data, u32pkgSize);
			return TRUE;
		}
		else
		{
			LogFmtStrA(SPLOGLV_WARN, "%s: buffer too small %d < %d!", __FUNCTION__, u32BufSize, u32pkgSize);
			memcpy(lpBuff, &lpPackage->data, u32BufSize);
			return FALSE;
		}
	}
	else
	{
		return TRUE;
	}
}

SPRESULT CDiagBase::SendCmd(const DIAG_HEADER& hd, const void* lpData /*= NULL*/, uint32 u32BytesToSend /*= 0*/, BOOL bCheckAssert /*= TRUE*/)
{
	IsDeviceOpen();

	if (bCheckAssert && m_lpAssert->IsAsserted())
	{
		m_lpAssert->WaitForDumpFinish(INFINITE);
		return SP_E_PHONE_ASSERTED;
	}

	DIAG_PACKAGE dp;
	DIAG_HEADER  wh = hd;
	wh.len = static_cast<unsigned short>(u32BytesToSend);
	dp.header = wh;
	dp.data = const_cast<void*>(lpData);

	PRT_WRITE_T pwt;
	pwt.action = PRT_WRITE_no_respond;
	pwt.nCond = -1;
	pwt.lpProtocolData = &dp;

	DWORD dwBytesSent = m_lpDiagChannel->Write((LPVOID)& pwt, 1);
	if (dwBytesSent >= MIN_PKT_SIZE(u32BytesToSend))
	{
		return SP_OK;
	}
	else
	{
		LogFmtStrA(SPLOGLV_ERROR, "%d bytes has been written, but target is %d bytes.", dwBytesSent, MIN_PKT_SIZE(u32BytesToSend));
		return SP_E_PHONE_SEND_DATA;
	}
}

SPRESULT CDiagBase::SendCmd(const DIAG_HEADER& hd, const void* lpData, uint32 u32BytesToSend, const CRecvPkgsList& recvCond)
{
	m_recvPkgList = recvCond;
	StartAysnRecving(TRUE);

	SPRESULT res = SendCmd(hd, lpData, u32BytesToSend);
	if (SP_OK != res)
	{
		StartAysnRecving(FALSE);
	}

	return res;
}

SPRESULT CDiagBase::RecvCmd(CRecvPkgsList& recvList, DWORD dwTimeOut)
{
	dwTimeOut = min(dwTimeOut, m_MaxTimeout);
	dwTimeOut = max(dwTimeOut, m_MinTimeout);

	if (WaitForMultipleEvent(dwTimeOut))
	{
		recvList = m_recvPkgList;
		return SP_OK;
	}
	else
	{
		StartAysnRecving(FALSE);

		// Assert之后等待Dump完成，否则上层有可能会因为命令TIMEOUT返回关闭电源，导致DUMP不完整
		if (m_lpAssert->IsAsserted())
		{
			m_lpAssert->WaitForDumpFinish(INFINITE);
			return SP_E_PHONE_ASSERTED;
		}

		LogFmtStrA(SPLOGLV_ERROR, "Receive response timeOut %d!", dwTimeOut);
		return SP_E_PHONE_TIMEOUT;
	}
}

SPRESULT CDiagBase::RecvCmd(void* lpBuff, uint32 u32BufSize, uint32* lpu32RecvBytes, DWORD dwTimeOut /* = TIMEOUT_3S */)
{
	CRecvPkgsList recvList;
	SPRESULT res = RecvCmd(recvList, dwTimeOut);
	if (SP_OK != res)
	{
		return res;
	}

	if (NULL != lpBuff && u32BufSize > 0)
	{
		if (!UnpackPRT(recvList.GetPackage(0), NULL, lpBuff, u32BufSize, lpu32RecvBytes))
		{
			return SP_E_PHONE_BUFFER_TOO_SMALL;
		}
	}

	/// CompareCond function will make sure the hd & rd are same
	/// So here we don't need to compare again, just return success.
	return SP_OK;
}

SPRESULT CDiagBase::SendAndRecv(
	const DIAG_HEADER& hd, const void* lpSendBuf, uint32 u32BytesToSend,
	CRecvPkgsList& recvList,
	DWORD dwTimeOut /*= TIMEOUT_3S*/
)
{
	ResetEvent(m_hWakeUpEvent);

	SPRESULT res = SendCmd(hd, lpSendBuf, u32BytesToSend, recvList);
	if (SP_OK == res)
	{
		res = RecvCmd(recvList, dwTimeOut);
	}

	return res;
}

SPRESULT CDiagBase::SendAndRecv(
	const DIAG_HEADER& wh, const void* lpSendBuf, uint32 u32BytesToSend,
	DIAG_HEADER& rh, void* lpBuff, uint32 u32BufSize, uint32* lpu32RecvBytes,
	DWORD dwTimeOut /* = TIMEOUT_3S */
)
{
	CRecvPkgsList recvList;
	recvList.AddCondition(rh);

	SPRESULT res = SendAndRecv(wh, lpSendBuf, u32BytesToSend, recvList, dwTimeOut);
	if (SP_OK != res)
	{
		return res;
	}

	if (!UnpackPRT(recvList.GetPackage(0), &rh, lpBuff, u32BufSize, lpu32RecvBytes))
	{
		return SP_E_PHONE_BUFFER_TOO_SMALL;
	}

	/// CompareCond function will make sure the hd & rd are same
	/// So here we don't need to compare again, just return success.
	return SP_OK;
}

BOOL CDiagBase::RegisterObservers(void)
{
	try
	{
		IProtocolObserver* lpObserver = NULL;
		for (int obs = OBS_SYSTEM; obs < OBS_MAX; obs++)
		{
			if (OBS_SYSTEM == obs)
			{
				lpObserver = (IProtocolObserver*)this;
			}
			else if (OBS_FILE == obs)
			{
				lpObserver = (IProtocolObserver*)new CFileObserver();
			}
			else if (OBS_SOCKET == obs)
			{
				CAppSettings::GetInstance().GetValue(CAppSettings::Socket, (LPVOID)& m_SocketSetting);

				lpObserver = (IProtocolObserver*)new CSocketObserver();
				if (m_SocketSetting.bConnect)
				{
					if (!((CSocketObserver*)lpObserver)->Start(m_SocketSetting.szIP, m_SocketSetting.dwPort))
					{
						delete lpObserver;
						lpObserver = NULL;

						LogFmtStrA(SPLOGLV_ERROR, "Connect IP <%s : %d> failed!", m_SocketSetting.szIP, m_SocketSetting.dwPort);
						goto __FailCleanObservers;
					}
				}
			}
			else
			{
				assert(0);
				continue;
			}

			m_obsList[obs].obs = lpObserver;
			m_obsList[obs].id = m_lpDiagChannel->AddObserver(m_obsList[obs].obs);
			if (INVALID_OBSERVER_ID == m_obsList[obs].id)
			{
				LogFmtStrA(SPLOGLV_ERROR, "Register observer %d failed!", obs);
				goto __FailCleanObservers;
			}
		}

		return TRUE;
	}
	catch (const std::bad_alloc& /*e*/)
	{

	}

__FailCleanObservers:
	CleanUpObservers();
	return FALSE;
}

void CDiagBase::CleanUpObservers(void)
{
	for (int obs = OBS_SYSTEM; obs < OBS_MAX; obs++)
	{
		if (INVALID_OBSERVER_ID != m_obsList[obs].id)
		{
			m_lpDiagChannel->RemoveObserver(m_obsList[obs].id);
			m_obsList[obs].id = INVALID_OBSERVER_ID;
		}

		if (NULL != m_obsList[obs].obs)
		{
			if (OBS_FILE == obs)
			{
				CFileObserver* lpOBS = static_cast<CFileObserver*>(m_obsList[obs].obs);
				lpOBS->Stop();
				delete lpOBS;
			}
			else if (OBS_SOCKET == obs)
			{
				CSocketObserver* lpOBS = static_cast<CSocketObserver*>(m_obsList[obs].obs);
				lpOBS->Stop();
				delete lpOBS;
			}

			m_obsList[obs].obs = NULL;
		}
	}
}

BOOL CDiagBase::Opend()
{
	return m_bDeviceOpen;
}

void CDiagBase::StartCaptureLog()
{
	LogFmtStrA(SPLOGLV_VERBOSE, "%s: %d", __FUNCTION__, m_nLogPort);

	if (m_bSaveLogel && m_bLogUsingOtherPort)
	{
		WCHAR szLogelFile[MAX_PATH] = { 0 };
		if (NULL != GetISpLogObject())
		{
			WCHAR szLogPath[MAX_PATH] = { 0 };
			GetISpLogObject()->GetProperty(LogProp_LogFilePath, MAX_PATH, (LPVOID)szLogPath);
			PathRemoveFileSpec(szLogPath);
			wcscpy_s(szLogelFile, szLogPath);
		}
		else
		{
			wcsncpy_s(szLogelFile, GetAppPath().c_str(), MAX_PATH - 1);
		}

		wcscat_s(szLogelFile, L"\\Arm.Log");

		if (NULL == m_hCapture)
		{
			ISpLog* pLogUtil = GetISpLogObject();
			m_hCapture = CreatePortCapture(pLogUtil);

			if (INVALID_HANDLE_VALUE == m_hCapture)
			{
				m_hCapture = NULL;
				LogRawStrA(SPLOGLV_ERROR, "CreatePortCapture failed!");
				return;
			}
		}

		CHANNEL_ATTRIBUTE ca;
		ca.ChannelType = CHANNEL_TYPE_COM;
		ca.Com.dwPortNum = m_nLogPort;
		ca.Com.dwBaudRate = 115200;
		if (!StartCapturePort(m_hCapture, &ca, szLogelFile))
		{
			LogRawStrA(SPLOGLV_ERROR, "Start capture log failed!");
			return;
		}
	}
}

void CDiagBase::WakeUpOnDumpStop()
{
	SetEvent(m_hDumpStopEvent);
}
