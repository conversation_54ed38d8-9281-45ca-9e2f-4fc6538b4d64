#include "StdAfx.h"
#include "DevHound.h"
#include <objbase.h>
#include <initguid.h>
#include <setupapi.h>
#pragma comment(lib, "setupapi.lib")

//
DEFINE_GUID(GUID_CLASS_PORT, 0x86e0d1e0L, 0x8089, 0x11d0, 0x9c, 0xe4, 0x08, 0x00, 0x3e, 0x30, 0x1f, 0x73);

//////////////////////////////////////////////////////////////////////////
LPCWSTR CDevHound::PORT_CLASS_NAME = L"Ports";
LPCWSTR CDevHound::MODEM_CLASS_NAME = L"Modem";

//
CDevHound::CDevHound(void)
    : m_bStopWork(TRUE)
{
}

CDevHound::~CDevHound(void)
{
}

BOOL CDevHound::Start(UINT nLogLv)
{
    trInit(NULL, "DevHound", nLogLv, FALSE, splog::typeText | splog::modeCreate | splog::modeNoTruncate);
    LogRawStrA(SPLOGLV_INFO, "=================================================");
    LogRawStrA(SPLOGLV_INFO, "DevHound started ...");

    m_bStopWork = FALSE;
    if (!CThread::start())
    {
        trFree(FALSE);
        return FALSE;
    }
    else
    {
        return TRUE;
    }
}

void CDevHound::Stop(void)
{
    m_bStopWork = TRUE;
    CThread::join();

    LogFmtStrA(SPLOGLV_INFO, "DevHound is stopped!");
    trFree(TRUE);
}

void CDevHound::run(void)
{
    LogFmtStrA(SPLOGLV_VERBOSE, "Monitor is running...");

    GUID* guidDev = (GUID*)& GUID_CLASS_PORT;
    HDEVINFO hDevInfo = INVALID_HANDLE_VALUE;

    // Get Port class set
    // Note:We use DIGCF_PRESENT flag,so maybe you can see
    // some ports on the device manager,but they are not 
    // enumerated by SetupDiEnumDeviceInterfaces in the do-while
    // loop,because their driver are disabled,no application 
    // can open and use them.

    hDevInfo = SetupDiGetClassDevsW(guidDev,
        NULL,
        NULL,
        DIGCF_PRESENT | DIGCF_DEVICEINTERFACE
    );

    if (INVALID_HANDLE_VALUE == hDevInfo)
    {
        LogFmtStrA(SPLOGLV_ERROR, "SetupDiGetClassDevsW() failed, WinErr = 0x%X", ::GetLastError());
        return;
    }

    SP_DEVICE_INTERFACE_DATA ifcData;
    ifcData.cbSize = sizeof(SP_DEVICE_INTERFACE_DATA);

    DWORD dwIndex = 0;

    SPDBT_INFO epi;

    bool bInit = true;

    // Enumerate port and modem class device interfaces
    do
    {
        memset(&epi, 0, sizeof(epi));

        if (SetupDiEnumDeviceInterfaces(hDevInfo, NULL, guidDev, dwIndex, &ifcData))
        {
            SP_DEVINFO_DATA devData;
            devData.cbSize = sizeof(SP_DEVINFO_DATA);

            if (!SetupDiGetDeviceInterfaceDetailW(hDevInfo, &ifcData, NULL, 0, NULL, &devData))
            {
                if (ERROR_INSUFFICIENT_BUFFER != GetLastError())
                {
                    // Can not get detail interface info
                    continue;
                }
            }

			WCHAR szHardwareID[SPDBT_NAME_MAX_LEN] = { 0 };

			// Get Friendly name from registry
			SetupDiGetDeviceRegistryPropertyW(hDevInfo, &devData, SPDRP_FRIENDLYNAME, NULL, (PBYTE)epi.szFriendlyName, SPDBT_NAME_MAX_LEN * sizeof(WCHAR), NULL);
			// Get port description from registry
			SetupDiGetDeviceRegistryPropertyW(hDevInfo, &devData, SPDRP_DEVICEDESC, NULL, (PBYTE)epi.szDescription, SPDBT_NAME_MAX_LEN * sizeof(WCHAR), NULL);
			// Get location path from registry
			std::vector<byte> arrLocationPaths(2000);
			epi.bGetUsbInfo = SetupDiGetDeviceRegistryPropertyW(hDevInfo, &devData, SPDRP_LOCATION_PATHS, NULL, (PBYTE)arrLocationPaths.data(), arrLocationPaths.size(), NULL);
			memcpy(epi.szLocationPath, arrLocationPaths.data(), sizeof(epi.szLocationPath));
			epi.szLocationPath[SPDBT_NAME_MAX_LEN - 1] = L'\0';
			// Get Hardware ID from registry
			SetupDiGetDeviceRegistryPropertyW(hDevInfo, &devData, SPDRP_HARDWAREID, NULL, (PBYTE)szHardwareID, SPDBT_NAME_MAX_LEN * sizeof(WCHAR), NULL);

			HKEY hDevKey = SetupDiOpenDevRegKey(hDevInfo, &devData, DICS_FLAG_GLOBAL, 0, DIREG_DEV, KEY_READ);

			LogFmtStrW(SPLOGLV_VERBOSE, L"Friendly Name:%s; location:%s; HardwareID: %s", epi.szFriendlyName, epi.szLocationPath, szHardwareID);

            if (INVALID_HANDLE_VALUE != hDevKey)
            {
                // Get port name
                DWORD dwCount = SPDBT_NAME_MAX_LEN;
                RegQueryValueExW(hDevKey, L"PortName", NULL, NULL, (BYTE*)epi.szPortName, &dwCount);
                RegCloseKey(hDevKey);
            }

            // Insert to port info array
            InsertPortInfo(epi, bInit);

            dwIndex++;
        }
        else
        {
            dwIndex = 0;
            bInit = false;
            ClearState();
            Sleep(100);
            /*
            if( ERROR_NO_MORE_ITEMS == GetLastError() )
            {
            // No more devices, do the do-while loop gain
            dwIndex = 0;
            bInit = false;
            ClearState();
            Sleep(10);
            }
            */

            SetupDiDestroyDeviceInfoList(hDevInfo);
            hDevInfo = INVALID_HANDLE_VALUE;
            hDevInfo = SetupDiGetClassDevsW(guidDev,
                NULL,
                NULL,
                DIGCF_PRESENT | DIGCF_DEVICEINTERFACE
            );

            memset(&ifcData, 0, sizeof(SP_DEVICE_INTERFACE_DATA));
            ifcData.cbSize = sizeof(SP_DEVICE_INTERFACE_DATA);
        }

    } while (!m_bStopWork && (INVALID_HANDLE_VALUE != hDevInfo));

    if (INVALID_HANDLE_VALUE != hDevInfo)
    {
        SetupDiDestroyDeviceInfoList(hDevInfo);
        hDevInfo = INVALID_HANDLE_VALUE;
    }
}

bool CDevHound::CheckPortInfo(SPDBT_INFO& epi)
{
    int nFriendlyName = wcslen(epi.szFriendlyName) * sizeof(wchar_t);
    int nPortName = wcslen(epi.szPortName) * sizeof(wchar_t);

    if (nPortName)
    {
        // Get port number from port name
        swscanf_s(epi.szPortName, L"COM%d", &epi.nPortNum);
        if (0 == epi.nPortNum)
        {
            // Not a valid port name
            nPortName = 0;
        }
    }

    if (!nPortName && nFriendlyName && SPDBT_PORT == epi.eType)
    {
        // Try to get port number from friendly name
        swscanf_s(epi.szFriendlyName, L"(COM%d)", &epi.nPortNum);
        if (0 == epi.nPortNum)
        {
            return false;
        }
    }

    if (0 == epi.nPortNum)
    {
        // Can not get port number
        return false;
    }

    if (!nPortName)
    {
        // compose a valid port name
        nPortName = swprintf_s(epi.szPortName, L"COM%d", epi.nPortNum) * sizeof(wchar_t);
    }

    if (!nFriendlyName)
    {
        // compose a void friendly name
        nFriendlyName = swprintf_s(epi.szFriendlyName, L"COM%d", epi.nPortNum) * sizeof(wchar_t);
    }
    else
    {
        if (SPDBT_MODEM == epi.eType)
        {
            // Because the friendly name of the modem has no com port information,
            // so we append it to the end of the friendly name,if the name is too
            // long,it will be changed to "part_of_friendly_name...(COMX)"
            bool bCat = true;
            if (nPortName + nFriendlyName + 2 * sizeof(wchar_t) > SPDBT_NAME_MAX_LEN - sizeof(wchar_t))
            {
                nFriendlyName = SPDBT_NAME_MAX_LEN - sizeof(wchar_t) - nPortName - 5 * sizeof(wchar_t);
                if (nFriendlyName > 0)
                {
                    epi.szFriendlyName[nFriendlyName] = L'\0';
                    wcscat_s(epi.szFriendlyName, L"...");
                }
                else
                {
                    bCat = false;
                }
            }

            if (bCat)
            {
                wcscat_s(epi.szFriendlyName, L"(");
                wcscat_s(epi.szFriendlyName, epi.szPortName);
                wcscat_s(epi.szFriendlyName, L")");
            }
        }
    }

    return true;
}

SPDBT_TYPE CDevHound::CheckDeviceClass(wchar_t* lpszClass)
{
    if (!wcscmp(lpszClass, PORT_CLASS_NAME))
    {
        return SPDBT_PORT;
    }
    else if (!wcscmp(lpszClass, MODEM_CLASS_NAME))
    {
        return SPDBT_MODEM;
    }
    else
    {
        return SPDBT_UNKNOWN;
    }
}

bool CDevHound::InsertPortInfo(SPDBT_INFO& epi, bool bInit)
{
    if (!CheckPortInfo(epi) || 0 == epi.nPortNum)
    {
        return false;
    }

    m_mPortInfo[epi.nPortNum] = epi;
    int nCount = m_vPortInfo.size();
    int i = 0;
    for ( i = 0; i < nCount; i++ )
    {
        if ( m_vPortInfo[i].nPortNum == epi.nPortNum )
        {
            // Bug 2326559
            // When location information is not obtained,
            // USB insertion is invalid until location information is obtained;
            if ( m_vPortInfo[i].bGetUsbInfo )
            {
                m_vPortInfo[i].bExist = TRUE;
                return false;
            }
            else
            {
                // Bug 2326559
                m_vPortInfo.erase( m_vPortInfo.begin() + i );
                break;
            }
        }
    }

    epi.bExist = TRUE;
    m_vPortInfo.push_back( epi );

    if ( !bInit && epi.bGetUsbInfo /*Bug 2326559*/ )
    {
        SendToUplayer( epi );
    }

    return true;
}

void CDevHound::SendToUplayer(SPDBT_INFO& epi)
{
    LogFmtStrA(SPLOGLV_INFO, "SendToUpLayer port = %d %s", epi.nPortNum, 1 == epi.bExist ? "PlugIn" : "Removed");

    int nPOS = -1;
    IDevMonitor* lpOBS = COBSList::GetNext(nPOS);
    while (NULL != lpOBS)
    {
        lpOBS->OnDevChange(epi.bExist ? SPDBT_DEVICEARRIVAL : SPDBT_DEVICEREMOVE, (LPCVOID)& epi);
        lpOBS = COBSList::GetNext(nPOS);
    }
}

void CDevHound::ClearState(void)
{
    int nCount = m_vPortInfo.size();
    int i = 0;
    for (i = 0; i < nCount; i++)
    {
        if (m_vPortInfo[i].bExist)
        {
            m_vPortInfo[i].bExist = FALSE;
        }
        else
        {
            SendToUplayer(m_vPortInfo[i]);
            m_vPortInfo.erase(m_vPortInfo.begin() + i);
            nCount--;
            i--;
        }
    }
}
