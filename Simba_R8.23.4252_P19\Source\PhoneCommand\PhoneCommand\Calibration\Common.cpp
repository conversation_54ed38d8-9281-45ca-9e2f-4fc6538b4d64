#include "StdAfx.h"
#include "CaliCmd.h"

SPRESULT CCaliCmd::rfbDebugCmd_Common(const PC_RFB_DEBUG_CMD_REQ_T* pReq, PC_RFB_DEBUG_CMD_RLT_T* pRlt)
{
    return rfbDebugCmd(RF_COM_DEBUG_CMD_REQ, pReq, pRlt);
}
SPRESULT CCaliCmd::rfbCaptureDataLen_Common(const PC_RFB_CAPTURE_DATA_REQ_T* pReq, PC_RFB_CAPTURE_DATA_RLT_T* pRlt)
{
    return rfbCaptureDataLen(RF_COM_CAPTURE_DATA_REQ, pReq, pRlt);
}
SPRESULT CCaliCmd::rfbReadData_Common(const PC_RFB_CAPTURE_DATA_READ_REQ_T* pReq, PC_RFB_CAPTURE_DATA_READ_RLT_T* pRlt)
{
    return rfbReadData(RF_COM_READ_CAPTURED_DATA_REQ, pReq, pRlt);
}

SPRESULT CCaliCmd::rfbDebugCmd( SUBCMD_CODE_E SubCmd, const PC_RFB_DEBUG_CMD_REQ_T* pReq, PC_RFB_DEBUG_CMD_RLT_T* pRlt)
{
    CheckValidPointer(pReq);
    CheckValidPointer(pRlt);

    LogFmtStrA(SPLOGLV_INFO, "Debug command: type = %d, param = %d,%d,%d,%d,%d", pReq->type, pReq->param[0],pReq->param[1],
        pReq->param[2],pReq->param[3],pReq->param[4]);

    L1_RFB_COM_DEBUG_CMD_REQ_T L1;
    ZeroMemory((void* )&L1, sizeof(L1));

    L1.head.SubCmdCode = Convert16((uint16)SubCmd);
    L1.head.SubCmdSize = Convert16(sizeof(L1_RFB_COM_DEBUG_CMD_REQ_T));

    L1.type = pReq->type;
    CopyMemory(L1.param, pReq->param, sizeof(L1.param));

    DeclareDiagHeader(hd, DIAG_BOCA_F, ZERO_SUBTYPE); 

    SPRESULT    res = SP_OK;
    uint32 recvSize = 0;
    res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvSize, m_dwTimeOut);

    if (SP_OK == res)
    {
        L1_RFB_COM_DEBUG_CMD_RLT_T* pTempRlt = (L1_RFB_COM_DEBUG_CMD_RLT_T* )m_diagBuff;
        LogFmtStrA(SPLOGLV_INFO, "status:%d, result:%d,%d,%d,%d,%d", pTempRlt->status, 
            pTempRlt->result[0], pTempRlt->result[1],pTempRlt->result[2], pTempRlt->result[3],pTempRlt->result[4]);        
        pRlt->status = pTempRlt->status;
        pRlt->type = pTempRlt->type;
        CopyMemory(pRlt->result, pTempRlt->result, sizeof(pRlt->result));
        res = pRlt->status;
    }

    return res;
}

SPRESULT CCaliCmd::rfbCaptureDataLen( SUBCMD_CODE_E SubCmd, const PC_RFB_CAPTURE_DATA_REQ_T* pReq, PC_RFB_CAPTURE_DATA_RLT_T* pRlt)
{
    CheckValidPointer(pReq);
    CheckValidPointer(pRlt);

    LogFmtStrA(SPLOGLV_INFO, "Capture data: type = %d, param = %d,%d,%d,%d,%d", pReq->type, pReq->param[0],pReq->param[1],
        pReq->param[2],pReq->param[3],pReq->param[4]);

    L1_RFB_COM_CAPTURE_DATA_REQ_T L1;
    ZeroMemory((void* )&L1, sizeof(L1));

    L1.head.SubCmdCode = Convert16((uint16)SubCmd);
    L1.head.SubCmdSize = Convert16(sizeof(L1_RFB_COM_CAPTURE_DATA_REQ_T));

    L1.type = pReq->type;
    CopyMemory(L1.param, pReq->param, sizeof(L1.param));

    DeclareDiagHeader(hd, DIAG_BOCA_F, ZERO_SUBTYPE); 

    SPRESULT    res = SP_OK;
    uint32 recvSize = 0;
    res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvSize, m_dwTimeOut);

    if (SP_OK == res)
    {
        L1_RFB_COM_CAPTURE_DATA_RLT_T* pTempRlt = (L1_RFB_COM_CAPTURE_DATA_RLT_T* )m_diagBuff;
        LogFmtStrA(SPLOGLV_INFO, "status:%d, len:%d, result:%d,%d,%d,%d,%d", pTempRlt->status, pTempRlt->len,
            pTempRlt->result[0], pTempRlt->result[1],pTempRlt->result[2], pTempRlt->result[3],pTempRlt->result[4]);        
        pRlt->status = pTempRlt->status;
        pRlt->len = pTempRlt->len;
        CopyMemory(pRlt->result, pTempRlt->result, sizeof(pRlt->result));
        res = pRlt->status;
    }

    return res;
}

SPRESULT CCaliCmd::rfbReadData( SUBCMD_CODE_E SubCmd, const PC_RFB_CAPTURE_DATA_READ_REQ_T* pReq, PC_RFB_CAPTURE_DATA_READ_RLT_T* pRlt)
{
    CheckValidPointer(pReq);
    CheckValidPointer(pRlt);

    LogFmtStrA(SPLOGLV_INFO, "Capture data: len = %d, offset = %d", pReq->len, pReq->offset);

    L1_RFB_COM_CAPTURE_DATA_READ_REQ_T L1;
    ZeroMemory((void* )&L1, sizeof(L1));

    L1.head.SubCmdCode = Convert16((uint16)SubCmd);
    L1.head.SubCmdSize = Convert16(sizeof(L1_RFB_COM_CAPTURE_DATA_READ_REQ_T));

    L1.len = pReq->len;
    L1.offset = pReq->offset;

    DeclareDiagHeader(hd, DIAG_BOCA_F, ZERO_SUBTYPE); 

    SPRESULT    res = SP_OK;
    uint32 recvSize = 0;
    res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvSize, m_dwTimeOut);

    if (SP_OK == res)
    {
        L1_RFB_COM_CAPTURE_DATA_READ_RLT_T* pTempRlt = (L1_RFB_COM_CAPTURE_DATA_READ_RLT_T* )m_diagBuff;
        LogFmtStrA(SPLOGLV_INFO, "status:%d, len:%d", pTempRlt->status, pTempRlt->len);        
        pRlt->status = pTempRlt->status;
        pRlt->len = pTempRlt->len;
        CopyMemory(pRlt->data, pTempRlt->data, sizeof(pRlt->data));
        res = pRlt->status;
    }

    return res;
}


SPRESULT CCaliCmd::ModemV3_ReadNvParam( RF_MODE_E Mode,
    NV_PARAM_OP_TYPE Type,
    uint32 uBandIndex,
    int nBuffSize,
    uint8 *pBuff,
    int &nLength )
{
    CheckValidPointer(pBuff);

    SUBCMD_CODE_E Code = NVM_RF_READ_PARAMS_REQ;
    if (Mode == RF_MODE_NR)
    {
        Code = NVM_NR_READ_PARAMS_REQ;
    }

    LogFmtStrA(SPLOGLV_INFO, "ReadNvParam: type = %d, Mode = %d, Band = %d", Type, Mode, uBandIndex);

    L1_READ_NV_PARAM_REQ L1;
    ZeroMemory((void* )&L1, sizeof(L1));

    L1.head.SubCmdCode = (uint16)Code;
    L1.head.SubCmdSize = sizeof(L1);
    L1.Config.usType = (uint16)Type;
    L1.Config.BandIndex = (uint8)uBandIndex;
    L1.Config.Mode = (uint8)Mode;

    DeclareDiagHeader(hd, DIAG_MODEM_RF_E, ZERO_SUBTYPE); 

    SPRESULT    res = SP_OK;
    uint32 recvSize = 0;
    res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvSize, m_dwTimeOut);

    if (SP_OK == res)
    {
        L1_READ_NV_PARAM_RLT* pRlt = (L1_READ_NV_PARAM_RLT* )m_diagBuff;
        CHKRESULT(DetermineStatus(Code, m_diagBuff, recvSize));

        nLength = recvSize - 8;
        if (nLength > nBuffSize)
        {
            LogFmtStrA(SPLOGLV_ERROR, "%s: BuffSize is too small", __FUNCTION__);
            return SP_E_PHONE_INVALID_LENGTH;
        }
   
        CopyMemory(pBuff, pRlt->data, nLength);
    }

    return res;
}

SPRESULT CCaliCmd::ModemV3_ReadAntMap( RF_MODE_E Mode, int nBand, READ_NV_PARAM_RLT_ANT_MAP *pMap )
{
    CheckValidPointer(pMap);

    std::vector<uint8> arrData;
    arrData.resize(300);

    int nLength = 0;
    CHKRESULT(ModemV3_ReadNvParam(Mode, NV_PARAM_ANT_MAP, nBand, 300, arrData.data(), nLength));

    if (nLength < sizeof READ_NV_PARAM_RLT_ANT_MAP)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: nLength is too small", __FUNCTION__);
        return SP_E_PHONE_INVALID_LENGTH;
    }

    CopyMemory(pMap, arrData.data(), sizeof(READ_NV_PARAM_RLT_ANT_MAP));

    return SP_OK;
}
