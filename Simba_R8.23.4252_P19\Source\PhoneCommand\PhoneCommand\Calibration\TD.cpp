#include "StdAfx.h"
#include "CaliCmd.h"
#include "TDUtility.h"


SPRESULT CCaliCmd::tdActive(BOOL bActive)
{
    LogFmtStrA(SPLOGLV_INFO, "%s", bActive ? "tdActive" : "tdDeActive");

    if (bActive)
    {
        L1_TOOL_MPH_ACTIVE_REQ L1;
        ZeroMemory((void* )&L1, sizeof(L1));
        L1.SignalCode  = Convert16((uint16)TOOL_MPH_ACTIVE_REQ);
        L1.SignalSize  = Convert16((uint16)sizeof(L1));

        DeclareDiagHeader(hd, DIAG_RF_F, ZERO_SUBTYPE); 
        return SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, NULL, 0, NULL, m_dwTimeOut);
    }
    else
    {
        L1_TOOL_MPH_DEACTIVE_REQ L1;
        ZeroMemory((void* )&L1, sizeof(L1));
        L1.SignalCode  = Convert16((uint16)TOOL_MPH_DEACTIVE_REQ);
        L1.SignalSize  = Convert16((uint16)sizeof(L1));

        DeclareDiagHeader(hd, DIAG_RF_F, ZERO_SUBTYPE); 
        return SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, NULL, 0, NULL, m_dwTimeOut);
    }
}

SPRESULT CCaliCmd::tdLoadNV(PC_TD_NV_T* lpNV)
{
    if (NULL == lpNV)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid parameters!", __FUNCTION__);
        return SP_E_PHONE_INVALID_PARAMETER;
    }
    
    LogFmtStrA(SPLOGLV_INFO, "%s: nv = %d, size = %d", __FUNCTION__, lpNV->eNvType, lpNV->nDataNum);

    L1_TD_NV_DATA_READ_T req;
    req.eNvType = Convert32((uint32)lpNV->eNvType);
    req.nDataNum= Convert16((uint16)lpNV->nDataNum);    

    DeclareDiagHeader(hd, DIAG_TD_NV, TD_NV_READ); 

    uint32 recvLen = 0;
    SPRESULT   res = SendAndRecv(hd, (const void *)&req, sizeof(req), hd, (void* )&m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);
    if (SP_OK == res)
    {
        if (recvLen < sizeof(L1_TD_NV_DATA_READ_T))
        {
            LogFmtStrA(SPLOGLV_ERROR, "Invalid response length %d.", recvLen);
            return SP_E_PHONE_INVALID_LENGTH;
        }

        // TD_NV_DATA_READ_T + data  = [TD_NV_DATA_WRITE_T]
        L1_TD_NV_DATA_WRITE_T* pCnf = (L1_TD_NV_DATA_WRITE_T* )m_diagBuff;
        lpNV->nDataNum = Convert16(pCnf->nDataNum);
        if (lpNV->nDataNum > TD_MAX_NV_DATA_NUM)
        {
            LogFmtStrA(SPLOGLV_ERROR, "Invalid TD NV data number %d.", lpNV->nDataNum);
            return SP_E_PHONE_INVALID_LENGTH;
        }

        uint32 nDataLen = sizeof(pCnf->nData[0])*(lpNV->nDataNum);
        uint32 nExpSize = sizeof(L1_TD_NV_DATA_READ_T) + nDataLen;
        if (recvLen < nExpSize)
        {
            LogFmtStrA(SPLOGLV_ERROR, "Invalid data length %d.", recvLen);
            return SP_E_PHONE_INVALID_LENGTH;
        }

        CopyMemory((void* )&lpNV->nData[0], (const void* )&pCnf->nData[0], nDataLen);
        Convert16((uint8* )&lpNV->nData[0], nDataLen);
    }

    return res;
}

SPRESULT CCaliCmd::tdSaveNV(const PC_TD_NV_T* lpNV)
{
    if (NULL == lpNV)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid parameters!", __FUNCTION__);
        return SP_E_PHONE_INVALID_PARAMETER;
    }

    LogFmtStrA(SPLOGLV_INFO, "%s: nv = %d, size = %d", __FUNCTION__, lpNV->eNvType, lpNV->nDataNum);

    L1_TD_NV_DATA_WRITE_T req;
    req.eNvType     = Convert32((uint32)lpNV->eNvType);
    req.nDataNum    = Convert16((uint16)lpNV->nDataNum);   
    uint32 nDataLen = (lpNV->nDataNum) * sizeof(lpNV->nData[0]);
    CopyMemory((void* )&req.nData[0], (const void *)&lpNV->nData[0], nDataLen);
    Convert16((uint8 *)&req.nData[0], nDataLen);
    uint32 nSendLen = sizeof(req.eNvType) + sizeof(req.nDataNum) + nDataLen;

    DeclareDiagHeader(hd, DIAG_TD_NV, TD_NV_WRITE); 

    SPRESULT ret =  SendAndRecv(hd, (const void *)&req, nSendLen, hd, NULL, 0, NULL, m_dwTimeOut);

    if (ret == SP_OK && m_pContainer != NULL)
    {
        std::wstring strKey = CTDUtility::GetShareKey(lpNV->eNvType);
        m_pContainer->SetValue(strKey.c_str(), lpNV->nData, lpNV->nDataNum * sizeof(lpNV->nData[0]));
    }

    return ret;
}

SPRESULT CCaliCmd::tdSaveToFlash(uint32 u32TimeOut)
{
    LogFmtStrA(SPLOGLV_INFO, "%s: timeout = %d", __FUNCTION__, u32TimeOut);

    DeclareDiagHeader(hd, DIAG_TD_NV, TD_NV_SAVETOFLASH); 

    uint32 recvLen = 0;
    SPRESULT   res = SendAndRecv(hd, NULL, 0, hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvLen, u32TimeOut);
    if (SP_OK == res)
    {
        if (recvLen > 0)
        {
            int8 nState = *(int8 *)&m_diagBuff[0];
            if (1 != nState)
            {
                return SP_E_PHONE_INVALID_STATE;
            }
        }
    }

    return  res;
}

SPRESULT CCaliCmd::tdFDTTX(const PC_TD_FDT_TX *lpParam)
{
	if (NULL == lpParam)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid parameters!", __FUNCTION__);
		return SP_E_PHONE_INVALID_PARAMETER;
	}
	LogFmtStrA(SPLOGLV_INFO, "%s:nTxFlag = %d, chanNum = %d",__FUNCTION__, lpParam->nTxFlag,
		lpParam->txFDTpara.nChanelNum);  

	L1_TD_FDT_TX_REQ req;    
	ZeroMemory(&req, sizeof(L1_TD_FDT_TX_REQ));   
	req.SignalSize = Convert16((uint16)sizeof(req));
	req.SignalCode = Convert16(TOOL_MPH_FDT_TX_V2_REQ);

	req.nTrigNum  = Convert16(lpParam->nTrigNum);  
	req.nTxFlag  =Convert16(lpParam->nTxFlag);
	req.txFDTpara.nChanelNum  =	Convert32(lpParam->txFDTpara.nChanelNum );
	for(unsigned int i = 0; i <lpParam->txFDTpara.nChanelNum; i ++)
	{
		req.txFDTpara.Arfcn[i].nArfcn= Convert16(lpParam->txFDTpara.Arfcn[i].nArfcn);	
		req.txFDTpara.Arfcn[i].nApcArrNum= Convert16(lpParam->txFDTpara.Arfcn[i].nApcArrNum);
		LogFmtStrA(SPLOGLV_INFO, "%s:i = %d, nArfcn = %d, nApcArrNum= %d",__FUNCTION__, i,
			lpParam->txFDTpara.Arfcn[i].nArfcn, lpParam->txFDTpara.Arfcn[i].nApcArrNum);  
		for(int j=0; j<MAX_MODE_NUMBER; j++)
		{
			req.txFDTpara.Arfcn[i].ApcTxArr[j].nStartFactor= Convert16(lpParam->txFDTpara.Arfcn[i].ApcTxArr[j].nStartFactor);
			req.txFDTpara.Arfcn[i].ApcTxArr[j].nFram = lpParam->txFDTpara.Arfcn[i].ApcTxArr[j].nFram;
			req.txFDTpara.Arfcn[i].ApcTxArr[j].cal_apc_step = lpParam->txFDTpara.Arfcn[i].ApcTxArr[j].cal_apc_step;
			LogFmtStrA(SPLOGLV_INFO, "%s:j = %d, nStartFactor = %d, nFram= %d, cal_apc_step= %d ",__FUNCTION__, j,
				lpParam->txFDTpara.Arfcn[i].ApcTxArr[j].nStartFactor, lpParam->txFDTpara.Arfcn[i].ApcTxArr[j].nFram,
				lpParam->txFDTpara.Arfcn[i].ApcTxArr[j].cal_apc_step); 
		}
	}  
	DeclareDiagHeader(hd, DIAG_RF_F, ZERO_SUBTYPE); 
	hd.sn = 0;
	ZeroMemory((void *)&m_diagBuff, sizeof(m_diagBuff));
	CRecvPkgsList recvList;
	recvList.AddCondition(hd);
	recvList.AddCondition(hd);
	SPRESULT res = SendAndRecv(hd, (const void* )&req, sizeof(req), recvList, m_dwTimeOut); 
	if (SP_OK != res)
	{
		return res;
	}
	uint32 recvLen = 0;
	for(int i=0; i<recvList.GetPkgsCount(); i++)
	{
		if (!UnpackPRT(recvList.GetPackage(i), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
		{
			assert(0);
			return SP_E_PHONE_INVALID_LENGTH;
		}
		if (recvLen >= sizeof(L1_TD_FDT_TX_CNF))
		{
			break;
		}
	}
	if (recvLen < sizeof(L1_TD_FDT_TX_CNF))
	{
		LogFmtStrA(SPLOGLV_ERROR, "[%s] Invalid response length %d!",__FUNCTION__, recvLen);
		return SP_E_PHONE_INVALID_LENGTH;
	}
	L1_TD_FDT_TX_CNF *pCnf = (L1_TD_FDT_TX_CNF *)m_diagBuff;
	uint16 nSuccess   = Convert16(pCnf->nSuccess);
	pCnf->nSuccess = nSuccess;
	if(nSuccess != 1)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s fail: state = %d!", __FUNCTION__,nSuccess);
		return SP_E_PHONE_INVALID_STATE;
	}
	return res;
}


SPRESULT CCaliCmd::tdFDTRX(const PC_TD_FDT_RX *lpParam,PC_TD_FDT_RX_CNF *lpAck)
{
	if (NULL == lpParam || NULL == lpAck )
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid parameters!", __FUNCTION__);
		return SP_E_PHONE_INVALID_PARAMETER;
	}
	LogFmtStrA(SPLOGLV_INFO, "tdFDTRX_V2: TrigNum = %d, rxGainNum = %d, chanNum = %d", lpParam->nTrigNum, lpParam->rxFDTpara.nRxGainNum, lpParam->rxFDTpara.nChanelNum);  
	unsigned int i = 0;
	L1_TD_FDT_RX_REQ req;    
	ZeroMemory(&req, sizeof(L1_TD_FDT_RX_REQ));   
	req.SignalSize = Convert16((uint16)sizeof(L1_TD_FDT_RX_REQ));
	req.SignalCode = Convert16(TOOL_MPH_FDT_RX_V2_REQ);

	req.nTrigNum  = Convert16(lpParam->nTrigNum); 
	req.rxFDTpara.nChanelNum = Convert16(lpParam->rxFDTpara.nChanelNum); 
	req.rxFDTpara.nRxGainNum = Convert16(lpParam->rxFDTpara.nRxGainNum); 
	for(i = 0; i <lpParam->rxFDTpara.nChanelNum; i ++)
	{
		req.rxFDTpara.Arfcn[i] = Convert16(lpParam->rxFDTpara.Arfcn[i]);
	}
	for(i = 0; i <lpParam->rxFDTpara.nRxGainNum; i ++)
	{
		req.rxFDTpara.nRxGainData[i] = Convert16(lpParam->rxFDTpara.nRxGainData[i]);
	}

	DeclareDiagHeader(hd, DIAG_RF_F, ZERO_SUBTYPE); 
	hd.sn = 0;
	ZeroMemory((void *)&m_diagBuff, sizeof(m_diagBuff));

	CRecvPkgsList recvList;
	recvList.AddCondition(hd);
	recvList.AddCondition(hd);
	SPRESULT res = SendAndRecv(hd, (const void* )&req, sizeof(req), recvList, m_dwTimeOut); 
	if (SP_OK != res)
	{
		return res;
	}
	uint32 recvLen = 0;
	for(int i=0; i<recvList.GetPkgsCount(); i++)
	{
		if (!UnpackPRT(recvList.GetPackage(i), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
		{
			assert(0);
			return SP_E_PHONE_INVALID_LENGTH;
		}
		if (recvLen >= 20)
		{
			break;
		}
	}
	if (recvLen < 20)
	{
		LogFmtStrA(SPLOGLV_ERROR, "[%s] Invalid response length %d!", __FUNCTION__, recvLen);
		return SP_E_PHONE_INVALID_LENGTH;
	}

	L1_TD_FDT_RX_CNF *pCnf = (L1_TD_FDT_RX_CNF *)m_diagBuff;
	uint16 nSuccess   = Convert16(pCnf->nSuccess);
	pCnf->nSuccess = nSuccess;
	if (nSuccess != 1)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s fail: state = %d!", __FUNCTION__,nSuccess);
		return SP_E_PHONE_INVALID_STATE;
	}
	lpAck->nReultNum = Convert16(pCnf->nReultNum);
	if (recvLen < (uint32)pCnf->nReultNum*6+20)
	{
		LogFmtStrA(SPLOGLV_ERROR, "[%s] Invalid response length %d!", __FUNCTION__, recvLen);
		return SP_E_PHONE_INVALID_LENGTH;
	}
	for (int x=0; x<lpAck->nReultNum; x++)
	{
		lpAck->cali_FDT_AGC_result_arr[x].nArfcn = Convert16(pCnf->cali_FDT_AGC_result_arr[x].nArfcn);
		lpAck->cali_FDT_AGC_result_arr[x].nRxGain = Convert16(pCnf->cali_FDT_AGC_result_arr[x].nRxGain);
		lpAck->cali_FDT_AGC_result_arr[x].nRssi  = Convert16(pCnf->cali_FDT_AGC_result_arr[x].nRssi);
		LogFmtStrA(SPLOGLV_INFO, "nArfcn=%d, nRxGain=0x%X,nRssi=0x%X", lpAck->cali_FDT_AGC_result_arr[x].nArfcn,
			lpAck->cali_FDT_AGC_result_arr[x].nRxGain, lpAck->cali_FDT_AGC_result_arr[x].nRssi);
	}
	return res;
}

SPRESULT CCaliCmd::tdAgcStart(bool bAgcStart, const PC_AGC_REQ_T *pReq, PC_AGC_VALUE_CNF_T *pAck)
{
   	if (bAgcStart)
	{
		if (NULL == pReq || NULL == pAck)
		{
			LogFmtStrA(SPLOGLV_ERROR, "[CCaliCmd::tdAgcStart]Invalid parameters!");
			return SP_E_PHONE_INVALID_PARAMETER;
		}
		LogFmtStrA(SPLOGLV_INFO, "tdAgcStart(1): arfcn = %d, gain = %d", pReq->uArfcn, pReq->uGainValue);     
		
		L1_TOOL_MPH_DSP_TXRX_REQ req;
		ZeroMemory((void *)&req, sizeof(req));
		req.SignalCode  = Convert16((uint16)TOOL_MPH_DSP_TXRX_REQ);
		req.SignalSize  = Convert16((uint16)sizeof(req));
		
		req.band            = (uint32)Convert32((uint32)pReq->eBand);
		req.arfcn           = Convert16((uint16)pReq->uArfcn);
		req.gain_ind        = Convert16((uint16)pReq->uGainIndex);
		req.gain_val        = Convert16((uint16)pReq->uGainValue);
		req.sample_couter   = Convert16((uint16)DEFAULT_RX_SAMPLE_COUNT);
		req.mode            = Convert16((uint16)0x00);
		
		DeclareDiagHeader(hd, DIAG_RF_F, ZERO_SUBTYPE); 
        hd.sn = 0;
		CRecvPkgsList recvList;
		recvList.AddCondition(hd);
		recvList.AddCondition(hd);
		SPRESULT res = SendAndRecv(hd, (const void* )&req, sizeof(req), recvList, m_dwTimeOut); 
		if (SP_OK != res)
		{
			return res;
		}
		uint32 recvLen = 0;
		for(int i=0; i<recvList.GetPkgsCount(); i++)
		{
			if (!UnpackPRT(recvList.GetPackage(i), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
			{
				assert(0);
				return SP_E_PHONE_INVALID_LENGTH;
			}
			if (recvLen >= sizeof(L1_TOOL_MPH_DSP_RXLEV_CNF))
			{
				break;
			}
		}
		
		if (recvLen < sizeof(L1_TOOL_MPH_DSP_RXLEV_CNF))
		{
			LogFmtStrA(SPLOGLV_ERROR, "[%s] Invalid response length %d!", __FUNCTION__, recvLen);
			return SP_E_PHONE_INVALID_LENGTH;
		}
		ZeroMemory((void *)pAck, sizeof(PC_AGC_VALUE_CNF_T));
		L1_TOOL_MPH_DSP_RXLEV_CNF *pCnf = (L1_TOOL_MPH_DSP_RXLEV_CNF *)m_diagBuff;
		pAck->bValid = (int32)Convert32((uint32)pCnf->is_valid);
		pAck->nRxlev = (uint16)Convert16((uint16)pCnf->rxlev);
		pAck->nRssi  = (uint16)Convert16((uint16)pCnf->rssi);

		if (recvLen > sizeof(L1_TOOL_MPH_DSP_RXLEV_CNF))
		{
			L1_TOOL_MPH_DSP_AGC_RXLEV_CNF *pAgcCnf = (L1_TOOL_MPH_DSP_AGC_RXLEV_CNF *)m_diagBuff;
			pAck->nRssiNum  = (int)Convert32((uint32)pAgcCnf->rssi_number);
			assert(pAck->nRssiNum <= MAX_AGC_RESULT_NUM);
			if (pAck->nRssiNum > MAX_AGC_RESULT_NUM)
			{
				LogFmtStrA(SPLOGLV_ERROR, "[CCaliCmd::tdAgcStart] Invalid number of RSSI %d <= %d!", pAck->nRssiNum, MAX_AGC_RESULT_NUM);
				return SP_E_PHONE_INVALID_DATA;
			}

			for (int x=0; x<pAck->nRssiNum; x++)
			{
				pAck->nRssiState[x] = (uint16)Convert16((uint16)pAgcCnf->rssi_state[x]);
				pAck->nRssiVal[x]   = (uint16)Convert16((uint16)pAgcCnf->rssi_val[x]);
			}

		} 
		return res;

	}
	else
	{
		LogFmtStrA(SPLOGLV_INFO, "tdAgcStart(0): ");     
		
		L1_TOOL_MPH_STOP_DSP_TXRX_REQ req;
		ZeroMemory((void *)&req, sizeof(req));
		req.SignalCode  = Convert16((uint16)TOOL_MPH_STOP_DSP_TXRX_REQ);
		req.SignalSize  = Convert16((uint16)sizeof(req));
		
		DeclareDiagHeader(hd, DIAG_RF_F, ZERO_SUBTYPE); 
		uint32 recvLen = 0;
		return SendAndRecv(hd, NULL, 0, hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut); 
	}
    
}


SPRESULT CCaliCmd::tdTxGroupSweep(const PC_TD_TX_SWEEP_T *pParam)
{
	LogFmtStrA(SPLOGLV_INFO, "tdTxGroupSweep:");

	if (NULL == pParam)
	{
		LogRawStrA(SPLOGLV_ERROR, "Invalid parameters!");
		return SP_E_PHONE_INVALID_PARAMETER;
	}
	LogFmtStrA(SPLOGLV_INFO, "tdTxGroupSweep: freq = %d, step = %d, trig = %d, factor_number = %d", \
		pParam->nFreq, pParam->nFactorStep, pParam->nTriggerLevel, pParam->nFactorNumber);

	L1_TOOL_TD_MPH_APC_GRP_CONTROL_REQ req;
	ZeroMemory((void *)&req, sizeof(req));
	req.SignalCode      = Convert16((uint16)TOOL_MPH_DSP_APC_GRP_CONTROL_REQ);
	req.SignalSize      = Convert16((uint16)sizeof(L1_TOOL_TD_MPH_APC_GRP_CONTROL_REQ));

	req.freq            = (uint32)Convert32((uint32)pParam->nFreq);
	req.factor_step     = (uint16)Convert16((uint16)pParam->nFactorStep);
	req.trigger_level   = (uint16)Convert16((uint16)pParam->nTriggerLevel);
	req.factor_number   = (uint16)Convert16((uint16)pParam->nFactorNumber);

	for (uint16 i=0; i<pParam->nFactorNumber; i++)
	{
		req.factor_arr[i].nTxNumber = (uint16)Convert16((uint16)pParam->FactorArr[i].nTxNumber);
		req.factor_arr[i].nFactor   = (uint16)Convert16((uint16)pParam->FactorArr[i].nFactor);
	}

	DeclareDiagHeader(hd, DIAG_RF_F, ZERO_SUBTYPE); 
	uint32 recvLen = 0;
	return SendAndRecv(hd, (const void *)&req, sizeof(req),	hd, (void *)&m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);
}


SPRESULT CCaliCmd::tdNST(TD_NONSIGNAL_COMMAND_E eCmd, const PC_TD_NONSIGNAL_REQ* lpReq, PC_TD_NONSIGNAL_CNF* lpAck)
{
    LogFmtStrA(SPLOGLV_INFO, "%s: Cmd = %d", __FUNCTION__, eCmd);

    if (NULL == lpReq)
    {
        LogRawStrA(SPLOGLV_ERROR, "Invalid parameters!");
        return SP_E_PHONE_INVALID_PARAMETER;
    }

    uint32 i = 0, j = 0;
    L1_TOOL_MPH_TDNONSIGNAL_PARAM_REQ L1;
    ZeroMemory((void *)&L1, sizeof(L1));
    L1.SignalCode       = Convert16((uint16)TOOL_MPH_NONSIGNAL_TD_TEST_REQ);
    L1.SignalSize       = Convert16((uint16)sizeof(L1));
    
    L1.nonsignal_cmd    = Convert32((uint32)eCmd);
    for (i=0; i<16; i++)
    {
        L1.td_param.ul_chan_code[i] = Convert32((uint32)lpReq->ul_chan_code[i]);
        L1.td_param.dl_chan_code[i] = Convert32((uint32)lpReq->dl_chan_code[i]);
    }
    L1.td_param.arfcn                      = Convert16((uint16)lpReq->arfcn);
    L1.td_param.cell_param_id              = Convert16((uint16)lpReq->cell_param_id);
    L1.td_param.ul_slot                    = Convert16((uint16)lpReq->ul_slot);
    L1.td_param.ul_midamble_alloc_mode     = Convert16((uint16)lpReq->ul_midamble_alloc_mode);
    L1.td_param.ul_midamble                = Convert16((uint16)lpReq->ul_midamble);
    L1.td_param.ul_midamble_shift          = Convert16((uint16)lpReq->ul_midamble_shift);
    L1.td_param.ul_kcell                   = Convert16((uint16)lpReq->ul_kcell);
    L1.td_param.dl_slot                    = Convert16((uint16)lpReq->dl_slot);
    L1.td_param.dl_midamble_alloc_mode     = Convert16((uint16)lpReq->dl_midamble_alloc_mode);
    L1.td_param.dl_midamble                = Convert16((uint16)lpReq->dl_midamble);
    L1.td_param.dl_midamble_shift          = Convert16((uint16)lpReq->dl_midamble_shift);
    L1.td_param.dl_kcell                   = Convert16((uint16)lpReq->dl_kcell);
    L1.td_param.prx_up_dpch_des            = (int8  )lpReq->prx_up_dpch_des;
    L1.td_param.pccpch_tx_power            = (uint8 )lpReq->pccpch_tx_power;
    L1.td_param.ul_chan_code_num           = (uint8 )lpReq->ul_chan_code_num;
    L1.td_param.dl_chan_code_num           = (uint8 )lpReq->dl_chan_code_num;
    for (j=0; j<7; j++)
    {
        L1.td_param.reserved[j] = Convert16((uint16)lpReq->reserved[j]);
    }

    ///
    /// Several commands has 2 responses
    /// 
    uint32 u32ExpAckPkgCount = 1;
    uint32 u32ExpAckSize = 0;
    switch(eCmd)
    {
    case CMD_RXTXLOOP_UPDATE_TDRF_PARAM_DSP:
    case CMD_RXTXLOOP_TDINITIATE:
    case CMD_RXTXLOOP_TDSTARTLOOP:
    case CMD_RXTXLOOP_TDMODE_CHANGE:
        u32ExpAckSize = sizeof(L1_TOOL_MPH_NONSIGNAL_TD_TEST_CNF);
        u32ExpAckPkgCount = 2;
        break;
    case CMD_RXTX_GET_TDBER:
        u32ExpAckSize = sizeof(L1_TOOL_MPH_NONSIGNAL_BER_CNF);
        u32ExpAckPkgCount = 2;
        break;
    case CMD_RX_GET_RSCP:
        u32ExpAckSize = sizeof(L1_TOOL_MPH_NONSIGNAL_RSCP_CNF);
        u32ExpAckPkgCount = 2;
        break;
    default:
        u32ExpAckSize = 0;
        u32ExpAckPkgCount = 1;
        break;
    }

    DeclareDiagHeader(hd, DIAG_RF_F, ZERO_SUBTYPE); 
    hd.sn = 0; // SW bug. SN of response packet is always 0. 
    CRecvPkgsList recvList;
    for (i=0; i<u32ExpAckPkgCount; i++)
    {
        recvList.AddCondition(hd);
    }

    SPRESULT res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), recvList, m_dwTimeOut); 
    if (SP_OK != res)
    {
        return res;
    }

    for (i=0; i<u32ExpAckPkgCount; i++)
    {
        uint32 recvLen = 0;
        if (!UnpackPRT(recvList.GetPackage(i), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
        {
            assert(0);
            continue;
        }

        if (recvLen >= u32ExpAckSize)
        {
            switch(eCmd)
            {
            case CMD_RXTXLOOP_UPDATE_TDRF_PARAM_DSP:
            case CMD_RXTXLOOP_TDINITIATE:
            case CMD_RXTXLOOP_TDSTARTLOOP:
            case CMD_RXTXLOOP_TDMODE_CHANGE:
                {
                    L1_TOOL_MPH_NONSIGNAL_TD_TEST_CNF* pCnf = (L1_TOOL_MPH_NONSIGNAL_TD_TEST_CNF* )m_diagBuff;
                    uint32 iCmd = Convert32(pCnf->nonsignal_cmd);
                    if (iCmd != (uint32)eCmd)
                    {
                        LogFmtStrA(SPLOGLV_ERROR, "Invalid TD NST response command %d <> %d.", iCmd, eCmd);
                        return SP_E_PHONE_INVALID_DATA;
                    }

                    if (0 != pCnf->uRet)   
                    {
                        LogFmtStrA(SPLOGLV_ERROR, "TD NST command %d request failed, state = %d.", eCmd, pCnf->uRet);
                        return SP_E_PHONE_INVALID_STATE;
                    }
                }
                break;

            case CMD_RX_GET_RSCP:
                {
                    L1_TOOL_MPH_NONSIGNAL_RSCP_CNF* pCnf = (L1_TOOL_MPH_NONSIGNAL_RSCP_CNF* )m_diagBuff;
                    lpAck->is_result_valid = Convert16(pCnf->is_result_valid);
                    lpAck->uRet            = Convert16(pCnf->rscp_level);

                    LogFmtStrA(SPLOGLV_INFO, "TD RSCP: is_valid = %d rscp = %d", lpAck->is_result_valid, lpAck->uRet);
                }
                break;

            case CMD_RXTX_GET_TDBER:
                {
                    L1_TOOL_MPH_NONSIGNAL_BER_CNF* pCnf = (L1_TOOL_MPH_NONSIGNAL_BER_CNF* )m_diagBuff;
                    lpAck->is_result_valid = pCnf->nBer_is_ready;
                    lpAck->uRet            = Convert16(pCnf->nBer_bit_error_num);
                    lpAck->reserver[0]     = Convert16(pCnf->nBer_bit_total_num);

                    LogFmtStrA(SPLOGLV_INFO, "TD SE-BER: ready = %d, error = %d, total = %d", lpAck->is_result_valid, lpAck->uRet, lpAck->reserver[0]);
                }
                break;

            default:
                break;
            }

        } /// end if (recvLen >= u32ExpAckSize)

    }


    return res;
}

SPRESULT CCaliCmd::tdCalSwitchTogsmCal(void)
{
    LogFmtStrA(SPLOGLV_INFO, "Switch from TD-SCDMA CAL mode to GSM CAL mode");

    /// Init
    L1_TOOL_MPH_CALI_G2T_REQ req;
    ZeroMemory((void* )&req, sizeof(req));
    req.SignalSize = Convert16((uint16)sizeof(req));
    req.SignalCode = Convert16((uint16)TOOL_MPH_CALI_G2T_REQ);

    DeclareDiagHeader(hd, DIAG_RF_F, ZERO_SUBTYPE); 

    CRecvPkgsList recvList;
    recvList.AddCondition(hd);
    recvList.AddCondition(hd);
    SPRESULT res = SendAndRecv(hd, (const void* )&req, sizeof(req), recvList, m_dwTimeOut); 
    if (SP_OK != res)
    {
        return res;
    }

    for (int i=0; i<recvList.GetPkgsCount(); i++)
    {
        uint32 recvLen = 0;
        if (!UnpackPRT(recvList.GetPackage(i), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
        {
            assert(0);
            continue;
        }

        if (recvLen >= sizeof(L1_TOOL_MPH_CALI_G2T_CNF))
        {
            L1_TOOL_MPH_CALI_G2T_CNF *pRLT = (L1_TOOL_MPH_CALI_G2T_CNF *)m_diagBuff;
            uint16 uType = Convert16(pRLT->SignalCode);
            if (TOOL_MPH_CALI_G2T_CNF != uType)
            {
                LogFmtStrA(SPLOGLV_ERROR, "Invalid response command ID: 0x%X <> 0x%X", uType, TOOL_MPH_CALI_G2T_CNF);
                return SP_E_PHONE_INVALID_DATA;       
            }

            uint32 uState = Convert32(pRLT->is_success);
            if (0 != uState)
            {
                LogFmtStrA(SPLOGLV_ERROR, "Change TD Cal. to GSM Cal. failed, state = %d.", uState);
                return SP_E_PHONE_INVALID_STATE;
            }
        }
    }

    Sleep(500);

    /// Change 
    TOOL_L1_DIAG_CALI_PARAM_T L1;
    L1.is_nv    = Convert16((uint16)0);
    L1.band     = Convert16((uint16)0);
    L1.type     = Convert16((uint16)CALI_MODE_T2G_INIT);
    L1.index    = Convert16((uint16)0);
    L1.length   = Convert16((uint16)0);

    DeclareDiagHeader(rd, DIAG_CALIBRATION, L1_WRITE);
    return SendAndRecv(rd, (const void* )&L1, sizeof(L1), rd, NULL, 0, NULL, m_dwTimeOut);
}

SPRESULT CCaliCmd::ModemV3_TD_COMMOM_GetVersion(TD_VERSION_T* pVersion)
{
	CFnLog fuLog(GetISpLogObject(), L"ModemV3_TD_COMMOM_GetVersion");

	if ( NULL == pVersion )
	{
		LogFmtStrA(SPLOGLV_INFO, "TD version value is empty.");
		return SP_E_PHONE_INVALID_PARAMETER;
	}

	uint32 recvSize = 0;

	L1_RFB_COM_QUERY_CAL_IF_VERSION_REQ_T L1;
	ZeroMemory((void* )&L1, sizeof(L1));
	L1.head.SubCmdCode  = RF_COM_QUERY_CAL_IF_VERSION_REQ;
	L1.head.SubCmdSize  = (uint16)sizeof(L1);

	L1.rf_mode = RF_MODE_TD;

	DeclareDiagHeader(hd, DIAG_MODEM_RF_E, ZERO_SUBTYPE);

	CHKRESULT(SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvSize, m_dwTimeOut));

	if (recvSize < sizeof(L1_RFB_COM_QUERY_CAL_IF_VERSION_RLT_T))
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid response package size.", __FUNCTION__);
		return SP_E_PHONE_INVALID_LENGTH;
	}

	CHKRESULT(DetermineStatus(RF_COM_QUERY_CAL_IF_VERSION_REQ, m_diagBuff, recvSize));

	L1_RFB_COM_QUERY_CAL_IF_VERSION_RLT_T* pRLT = (L1_RFB_COM_QUERY_CAL_IF_VERSION_RLT_T*)m_diagBuff;
	memcpy(pVersion, pRLT->if_version, sizeof(TD_VERSION_T));

	return SP_OK;
}