#include "StdAfx.h"
#include "CaliCmd.h"
#include "NrDef.h"

SPRESULT CCaliCmd::DetermineStatus(SUBCMD_CODE_E cmd, uint8* data, int32 length)
{
    L1_COMMON_RTL_T* pRLT = (L1_COMMON_RTL_T*)data;

    if (pRLT->head.SubCmdCode != cmd)
    {
        LogFmtStrA(SPLOGLV_ERROR, "DetermineStatus: SubCmdCode Inconsistent");
        return 1;
    }

    if (pRLT->status != RF_APP_OP_SUCCESS)
    {
        LogFmtStrA(SPLOGLV_ERROR, "DetermineStatus: Status != RF_APP_OP_SUCCESS");
        LogFmtStrA(SPLOGLV_ERROR, "DetermineStatus: Status == 0x%x", pRLT->status);
        LogFmtStrA(SPLOGLV_ERROR, "DetermineStatus: Status == %d", pRLT->status);

        return 1;
    }

    if (length != pRLT->head.SubCmdSize)
    {
        LogFmtStrA(SPLOGLV_ERROR, "DetermineStatus: Length != pRLT->head.SubCmdSize");

        return 1;
    }

    return SP_OK;
}

SPRESULT CCaliCmd::rfbDebugCmd_NR(const PC_RFB_DEBUG_CMD_REQ_T* pReq, PC_RFB_DEBUG_CMD_RLT_T* pRlt)
{
    return rfbDebugCmd(RF_NR_DEBUG_CMD_REQ, pReq, pRlt);
}
SPRESULT CCaliCmd::rfbCaptureDataLen_NR(const PC_RFB_CAPTURE_DATA_REQ_T* pReq, PC_RFB_CAPTURE_DATA_RLT_T* pRlt)
{
    return rfbCaptureDataLen(RF_NR_CAPTURE_DATA_REQ, pReq, pRlt);
}
SPRESULT CCaliCmd::rfbReadData_NR(const PC_RFB_CAPTURE_DATA_READ_REQ_T* pReq, PC_RFB_CAPTURE_DATA_READ_RLT_T* pRlt)
{
    return rfbReadData(RF_NR_READ_CAPTURED_DATA_REQ, pReq, pRlt);
}

