﻿#include "StdAfx.h"
#include "CaliCmd.h"
#include <vector>
#include "LteUtility.h"
#include "LteDef.h"

//////////////////////////////////////////////////////////////////////////
SPRESULT CCaliCmd::lteActive(BOOL bActive)
{
    LogFmtStrA(SPLOGLV_INFO, "%s", bActive ? "LTE Active" : "LTE DeActive");

    SPRESULT res = SP_OK;

    if (bActive)
    {
        L1_LTE_EMPTY_REQ_T L1;
        ZeroMemory((void* )&L1, sizeof(L1));
        L1.SignalCode  = Convert16(TOOL_MPH_LTE_DSP_ACTIVE);
        L1.SignalSize  = Convert16((uint16)sizeof(L1));

        DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE); 

        CRecvPkgsList recvList;
        recvList.AddCondition(hd);
        recvList.AddCondition(hd);

        res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), recvList, m_dwTimeOut);
    }
    else
    {
        L1_LTE_EMPTY_REQ_T L1;
        ZeroMemory((void* )&L1, sizeof(L1));
        L1.SignalCode  = Convert16(TOOL_MPH_LTE_DSP_DEACTIVE);
        L1.SignalSize  = Convert16((uint16)sizeof(L1));

        DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE); 

        res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, NULL, 0, NULL, m_dwTimeOut);
    }

    if (SP_OK != res)
    {
        LogFmtStrA(SPLOGLV_ERROR, "lteActive Fail!");
    }

    return res;
}

SPRESULT CCaliCmd::lteTxCW(const PC_LTE_AFC_CW_T* req)
{
    CheckValidPointer(req);

    LogFmtStrA(SPLOGLV_INFO, "LTE TX CW: AFC = %d", req->nAfc_value);  

    L1_LTE_AFC_CW_REQ_T L1;    
    ZeroMemory((void* )&L1,  sizeof(L1));   

    L1.SignalCode   = Convert16(TOOL_MPH_LTE_AFC_FDT_REQ);
    L1.SignalSize   = Convert16((uint16)sizeof(L1));
    
    L1.BW           = Convert32(req->eBW);
    L1.nArfcn       = Convert16(req->nArfcn);
    L1.nAfc_value   = Convert16(req->nAfc_value); 
    L1.nApt_value   = Convert16(req->nApt_value);
    L1.nGainFactor  = Convert16(req->nGainFactor);
    L1.nMainOrDiv   = Convert16(req->nMainOrDiv);
    L1.nPa_mode     = Convert16(req->nPa_mode);
    for (int i=0; i<ARRAY_SIZE(L1.reserved); i++)
    {
        L1.reserved[i] = Convert32(req->reserved[i]);	
    }  

    DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE); 

    return SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, NULL, 0, NULL, m_dwTimeOut);
}

SPRESULT CCaliCmd::lteAgc(const PC_LTE_FDT_RX_T* req, PC_LTE_FDT_RX_RESULT_T* rlt)
{
    if (NULL == req || NULL == rlt)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid parameters!", __FUNCTION__);
        return SP_E_PHONE_INVALID_PARAMETER;
    }

    LogRawStrA(SPLOGLV_INFO, "LTE AGC FDT");  
   
    L1_LTE_FDT_RX_REQ_T L1;    
    ZeroMemory((void* )&L1,  sizeof(L1));   

    L1.SignalCode   = Convert16(TOOL_MPH_LTE_AGC_FDT_REQ);
    L1.SignalSize   = Convert16((uint16)sizeof(L1));
    
    L1.nChannelNum    = Convert32(req->nChannelNum);
    L1.BW           = Convert32(req->eBW);
    L1.nTrig        = Convert16(req->nTrig);
    L1.nAfcVal      = Convert16(req->nAfcVal); 
    for (int i = 0; i < MAX_PC_LTE_APC_CHANNEL_NUM; i++)
    {
        L1.AFRCN[i].nArfcn      = Convert16(req->AFRCN[i].nArfcn);
        L1.AFRCN[i].nAgcArrNum  = Convert16(req->AFRCN[i].nAgcArrNum);
        for (int j = 0; j < MAX_PC_LTE_AGC_GAIN_NUM; j++)
        {
            L1.AFRCN[i].nAgcRxArr[j] = Convert16(req->AFRCN[i].nAgcRxArr[j]);	
        }

        L1.BandIndicator[i] = req->BandIndicator[i];
    }

    L1.afc1 = Convert16(req->afc1);
    L1.afc2 = Convert16(req->afc2);

    for (int i = 0; i < ARRAY_SIZE(L1.reserved); i++)
    {
        L1.reserved[i] = Convert32(req->reserved[i]);	
    }  

    DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE);

    CRecvPkgsList recvList;
    recvList.AddCondition(hd);
    recvList.AddCondition(hd);
    SPRESULT res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), recvList, m_dwTimeOut); 
    if (SP_OK != res)
    {
        return res;
    }

    uint32 recvLen = 0;
    //1st packet
    if (!UnpackPRT(recvList.GetPackage(0), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: 1st packet UnpackPRT return False", __FUNCTION__);
        return SP_E_PHONE_INVALID_DATA;
    }
    else if (recvLen != 0)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: 1st packet length != 0", __FUNCTION__);
        return SP_E_PHONE_INVALID_DATA;
    }

    //2nd packet
    if (!UnpackPRT(recvList.GetPackage(1), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: 2nd packet UnpackPRT return False", __FUNCTION__);
        return SP_E_PHONE_INVALID_DATA;
    }

    if (recvLen != sizeof(L1_LTE_FDT_RX_RLT_T))
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: 2nd packet length != sizeof(L1_LTE_FDT_RX_RLT_T), %d != %d", __FUNCTION__, recvLen, sizeof(L1_LTE_FDT_RX_RLT_T));
        return SP_E_PHONE_INVALID_LENGTH;
    }
    else
    {
        L1_LTE_FDT_RX_RLT_T* pRLT = (L1_LTE_FDT_RX_RLT_T* )m_diagBuff;

        rlt->nChannelNum  = Convert32(pRLT->nChannelNum);
        for (int j = 0; j < ARRAY_SIZE(pRLT->RSSI); j++)
        {
            rlt->RSSI[j] = (int32)Convert32(pRLT->RSSI[j]);
        }

        for (int j = 0; j <ARRAY_SIZE(pRLT->FreqDelta); j++)
        {
            rlt->FreqDelta[j] = (int32)Convert32(pRLT->FreqDelta[j]);
        }

        for (int j = 0; j < ARRAY_SIZE(pRLT->reserved); j++)
        {
            rlt->reserved[j]  = (int32)Convert32(pRLT->reserved[j]);
        }

        return SP_OK;
    }
}

SPRESULT CCaliCmd::lteApc(const PC_LTE_FDT_TX_T* req)
{
    CheckValidPointer(req);

    LogRawStrA(SPLOGLV_INFO, "LTE APC FDT");  

    L1_LTE_FDT_TX_REQ_T    L1;    
    ZeroMemory((void* )&L1, sizeof(L1));   

    L1.SignalCode   = Convert16(TOOL_MPH_LTE_APC_FDT_REQ);
    L1.SignalSize   = Convert16((uint16)sizeof(L1));
    
    L1.BW           = Convert32(req->eBW);
    L1.nFactorStep  = Convert16(req->nFactorStep);
    L1.nAfcVal      = Convert16(req->nAfcVal ); 
    L1.nMainOrDiv   = Convert16(req->nMainOrDiv);
    L1.nChanNum     = Convert16(req->nChanNum);
    for (int i = 0; i < MAX_PC_LTE_APC_CHANNEL_NUM; i++)
    {
        L1.ArfcnGroup[i].nArfcn     = Convert16(req->ArfcnGroup[i].nArfcn);
        L1.ArfcnGroup[i].nTxArryNum = Convert16(req->ArfcnGroup[i].nTxArryNum);
        for (int j = 0; j < MAX_PC_LTE_APC_APT_NUM; j++)
        {
            L1.ArfcnGroup[i].TX_config[j].nStartFactor  = Convert16(req->ArfcnGroup[i].TX_config[j].nStartFactor);
            L1.ArfcnGroup[i].TX_config[j].nApt_value    = Convert16(req->ArfcnGroup[i].TX_config[j].nApt_value);
            L1.ArfcnGroup[i].TX_config[j].nPa_mode      = Convert16(req->ArfcnGroup[i].TX_config[j].nPa_mode);
            L1.ArfcnGroup[i].TX_config[j].nTxNum        = Convert16(req->ArfcnGroup[i].TX_config[j].nTxNum);
        }

        L1.BandIndicator[i] = req->BandIndicator[i];
    }

    for (int i = 0; i < ARRAY_SIZE(L1.reserved); i++)
    {
        L1.reserved[i] = Convert32(req->reserved[i]);	
    }  

    DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE); 

    CRecvPkgsList recvList;
    recvList.AddCondition(hd);
    recvList.AddCondition(hd);
    SPRESULT res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), recvList, m_dwTimeOut); 
    //SPRESULT res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, NULL, 0, NULL, m_dwTimeOut);
    if (SP_OK != res)
    {
        return res;
    }

    return SP_OK;
}

SPRESULT CCaliCmd::lteApc_V2( const PC_LTE_APC_V2_T* req )
{
    CheckValidPointer(req);

    LogRawStrA(SPLOGLV_INFO, "LTE APC V2");  

    L1_LTE_EMPTY_REQ_T    L1;    
    ZeroMemory((void* )&L1, sizeof(L1));   

    uint16 usLength =  (uint16)(sizeof(L1) + sizeof(L1_LTE_APC_V2_HEADER_T) + req->Header.ChannelCount * req->Header.ChannelSize + req->Header.AptCount * req->Header.AptSize);

    L1.SignalCode   = Convert16(TOOL_MPH_LTE_APC_V2);
    L1.SignalSize   = Convert16(usLength);

    uint8* pBuff = NULL;
    try
    {
        pBuff = new uint8[usLength];
    }
    catch (const std::bad_alloc& /*e*/)
    {
        pBuff = NULL;
    }
    if (NULL == pBuff)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Memory allocation failed.", __FUNCTION__);
        return SP_E_PHONE_ALLOC_MEMORY;
    }

    uint8* pPos = pBuff;
    uint16 usLen = sizeof(L1);
    memcpy(pPos, &L1, usLen);
    //header
    pPos += usLen;
    usLen = sizeof(L1_LTE_APC_V2_HEADER_T);
    memcpy(pPos, &req->Header, usLen);
    //channel
    pPos += usLen;
    usLen = req->Header.ChannelCount * req->Header.ChannelSize;
    memcpy(pPos, req->pChannel, usLen);
    //apt
    pPos += usLen;
    usLen = req->Header.AptCount * req->Header.AptSize;
    memcpy(pPos, req->pApt, usLen);

    DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE); 
    SPRESULT result = SendAndRecv(hd, (const void* )pBuff, usLength, hd, NULL, 0, NULL, m_dwTimeOut);

    delete[] pBuff;

    return result;
}

SPRESULT CCaliCmd::LtePdt_V2(const PC_LTE_PDT_V2_T *req,PC_LTE_PDT_RESULT_T *rlt)
{
	CheckValidPointer(req);

	LogRawStrA(SPLOGLV_INFO, "LTE Pdt V2");  

	L1_LTE_EMPTY_REQ_T L1;    
	ZeroMemory(&L1, sizeof(L1_LTE_EMPTY_REQ_T));

	unsigned short usLength = (unsigned short)(sizeof(L1)
		+ sizeof(PC_LTE_PDT_V2_HEADER_T)
		+ req->Header.ChannelNumber * req->Header.ChannelSize
		+ req->Header.AptNumber * req->Header.AptSize);

	L1.SignalSize = usLength;
	L1.SignalCode = TOOL_MPH_LTE_PDT_V2;

	uint8* pBuff = NULL;
	try
	{
		pBuff = new uint8[usLength];
	}
	catch (const std::bad_alloc& /*e*/)
	{
		pBuff = NULL;
	}
	if (NULL == pBuff)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Memory allocation failed.", __FUNCTION__);
		return SP_E_PHONE_ALLOC_MEMORY;
	}

	unsigned char *pPos = pBuff;
	uint32 usLen =  sizeof(L1);
	memcpy(pPos, &L1, usLen);
	//Header
	pPos += usLen;
	usLen = sizeof(PC_LTE_PDT_V2_HEADER_T);
	memcpy(pPos, &req->Header, usLen);
	//channel
	pPos += usLen;
	usLen = (unsigned short)(req->Header.ChannelNumber * req->Header.ChannelSize);
	memcpy(pPos, req->Channels, usLen);
	//apt
	pPos += usLen;
	usLen = (unsigned short)(req->Header.AptNumber * req->Header.AptSize);
	memcpy(pPos, req->Apts, usLen);
	DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE); 
 
	CRecvPkgsList recvList;
	recvList.AddCondition(hd);
	recvList.AddCondition(hd);
	SPRESULT res = SendAndRecv(hd, (const void* )pBuff, usLength, recvList, m_dwTimeOut); 
	delete[] pBuff;
	if (SP_OK != res)
	{
		return res;
	}

	uint32 recvLen = 0;
	//1st packet
	if (!UnpackPRT(recvList.GetPackage(0), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: 1st packet UnpackPRT return False");
		return SP_E_PHONE_INVALID_DATA;
	}
	else if (recvLen != 0)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: 1st packet length != 0");
		return SP_E_PHONE_INVALID_DATA;
	}

	//2nd packet
	if (!UnpackPRT(recvList.GetPackage(1), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: 2nd packet UnpackPRT return False", __FUNCTION__);
		return SP_E_PHONE_INVALID_DATA;
	}
	//fetch result
	unsigned short RetNumber = *(unsigned short*)(m_diagBuff + sizeof(L1_LTE_EMPTY_REQ_T));
	L1_LTE_PDT_RLT* pRLT = (L1_LTE_PDT_RLT* )m_diagBuff;
	uint16 uType = Convert16(pRLT->SignalCode);
	if (TOOL_MPH_LTE_PDT_V2 != uType)
	{
		LogFmtStrA(SPLOGLV_ERROR, "Invalid response command ID: 0x%X != 0x%X", uType, TOOL_MPH_LTE_PDT_V2);
		return SP_E_PHONE_INVALID_DATA;       
	}
	rlt->nCount = RetNumber;

	memcpy(rlt->Value, ((char*)m_diagBuff) + sizeof(L1_LTE_EMPTY_REQ_T) + sizeof(unsigned short), RetNumber * sizeof(unsigned short));

	return SP_OK;
}

SPRESULT CCaliCmd::LteAgc_V2(const PC_LTE_AGC_V2_T* pAgcParam, unsigned int* pAgcRet)
{
	if (NULL == pAgcParam || NULL == pAgcRet)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid parameters!", __FUNCTION__);
		return SP_E_PHONE_INVALID_PARAMETER;
	}

	LogRawStrA(SPLOGLV_INFO, "LTE AGC FDT V2");  

	L1_LTE_EMPTY_REQ_T    L1;    
	ZeroMemory((void* )&L1, sizeof(L1));  

	unsigned short usLength = (unsigned short)(sizeof(L1)
		+ sizeof(PC_LTE_AGC_V2_HEADER_T)
		+ sizeof(PC_LTE_AGC_V2_TRIGGER_T)
		+ pAgcParam->Header.ChannelNumber * pAgcParam->Header.ChannelSize
		+ pAgcParam->Header.PointNumber * pAgcParam->Header.PointSize);

	L1.SignalSize = usLength;
	L1.SignalCode = TOOL_MPH_LTE_AGC_V2;

	unsigned char *lpBuf = new unsigned char[usLength];
	if (NULL == lpBuf)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Memory allocation failed.", __FUNCTION__);
		return SP_E_PHONE_ALLOC_MEMORY;
	}
	else
	{
		ZeroMemory((void *)lpBuf, usLength);
	}

	unsigned char *pPos = lpBuf;
	uint16 usLen =  sizeof(L1);
	memcpy(pPos, &L1, usLen);
	//Header
	pPos += usLen;
	usLen = sizeof(PC_LTE_AGC_V2_HEADER_T);
	memcpy(pPos, &pAgcParam->Header, usLen);
	//Trigger
	pPos += usLen;
	usLen = sizeof(PC_LTE_AGC_V2_TRIGGER_T);
	memcpy(pPos, &pAgcParam->Trigger, usLen);
	//channel
	pPos += usLen;
	usLen = (unsigned short)(pAgcParam->Header.ChannelNumber * pAgcParam->Header.ChannelSize);
	memcpy(pPos, pAgcParam->Channels, usLen);
	//apt
	pPos += usLen;
	usLen = (unsigned short)(pAgcParam->Header.PointNumber * pAgcParam->Header.PointSize);
	memcpy(pPos, pAgcParam->Points, usLen);
	DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE); 
	//////////////////////////////////////////////////////////////////////////
	CRecvPkgsList recvList;
	recvList.AddCondition(hd);
	recvList.AddCondition(hd);
	SPRESULT res = SendAndRecv(hd, (const void* )lpBuf, usLength, recvList, m_dwTimeOut); 
	delete[] lpBuf;
	if (SP_OK != res)
	{
		return res;
	}

	uint32 recvLen = 0;
	//1st packet
	if (!UnpackPRT(recvList.GetPackage(0), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: 1st packet UnpackPRT return False", __FUNCTION__);
		return SP_E_PHONE_INVALID_DATA;
	}
	else if (recvLen != 0)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: 1st packet length != 0", __FUNCTION__);
		return SP_E_PHONE_INVALID_DATA;
	}

	//2nd packet
	if (!UnpackPRT(recvList.GetPackage(1), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: 2nd packet UnpackPRT return False", __FUNCTION__);
		return SP_E_PHONE_INVALID_DATA;
	}
	uint32 unRecLen = sizeof(L1_LTE_EMPTY_REQ_T) + sizeof(short) + (pAgcParam->Header.PointNumber * pAgcParam->Header.PointSize)*2;
	if (recvLen != unRecLen)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: 2nd packet length != sizeof(L1_LTE_FDT_RX_RLT_T), %d != %d", __FUNCTION__, recvLen, unRecLen);
		return SP_E_PHONE_INVALID_LENGTH;
	}
	else
	{
		short RssiNumber = *(short*)(m_diagBuff + sizeof(L1_LTE_EMPTY_REQ_T));
		if (RssiNumber != pAgcParam->Header.PointNumber)
		{
			LogFmtStrA(SPLOGLV_ERROR, "[CCaliCmd::LteAgc_V2] RssiNumber != Header.PointNumber, %d != %d!", RssiNumber, pAgcParam->Header.PointNumber);
			return SP_E_PHONE_INVALID_DATA;
		}
		else
		{
			memcpy(pAgcRet, ((char*)m_diagBuff) + sizeof(L1_LTE_EMPTY_REQ_T) + sizeof(short), RssiNumber * sizeof(int));	
		}
		return SP_OK;
	}
}
//////////////////////////////////////////////////////////////////////////
//lte v3
SPRESULT CCaliCmd::lteApc_V3( const PC_LTE_APC_V3_T* req )
{
	CheckValidPointer(req);

	LogRawStrA(SPLOGLV_INFO, "LTE APC V3");  

	L1_LTE_EMPTY_REQ_T    L1;    
	ZeroMemory((void* )&L1, sizeof(L1));   

	uint16 usLength =  (uint16)(sizeof(L1) + sizeof(L1_LTE_APC_V2_HEADER_T) + req->Header.ChannelCount * req->Header.ChannelSize + req->Header.AptCount * req->Header.AptSize);

	L1.SignalCode   = Convert16(TOOL_MPH_LTE_APC_V3);
	L1.SignalSize   = Convert16(usLength);

	uint8* pBuff = NULL;
	try
	{
		pBuff = new uint8[usLength];
	}
	catch (const std::bad_alloc& /*e*/)
	{
		pBuff = NULL;
	}
	if (NULL == pBuff)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Memory allocation failed.", __FUNCTION__);
		return SP_E_PHONE_ALLOC_MEMORY;
	}

	uint8* pPos = pBuff;
	uint16 usLen = sizeof(L1);
	memcpy(pPos, &L1, usLen);
	//header
	pPos += usLen;
	usLen = sizeof(L1_LTE_APC_V2_HEADER_T);
	memcpy(pPos, &req->Header, usLen);
	//channel
	pPos += usLen;
	usLen = req->Header.ChannelCount * req->Header.ChannelSize;
	memcpy(pPos, req->pChannel, usLen);
	//apt
	pPos += usLen;
	usLen = req->Header.AptCount * req->Header.AptSize;
	memcpy(pPos, req->pApt, usLen);

	DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE); 
	SPRESULT result = SendAndRecv(hd, (const void* )pBuff, usLength, hd, NULL, 0, NULL, m_dwTimeOut);

	delete[] pBuff;

	return result;
}

SPRESULT CCaliCmd::LtePdt_V3(const PC_LTE_PDT_V3_T *req,PC_LTE_PDT_RESULT_T *rlt)
{
	CheckValidPointer(req);

	LogRawStrA(SPLOGLV_INFO, "LTE Pdt V3");  

	L1_LTE_EMPTY_REQ_T L1;    
	ZeroMemory(&L1, sizeof(L1_LTE_EMPTY_REQ_T));

	unsigned short usLength = (unsigned short)(sizeof(L1)
		+ sizeof(PC_LTE_PDT_V2_HEADER_T)
		+ req->Header.ChannelNumber * req->Header.ChannelSize
		+ req->Header.AptNumber * req->Header.AptSize);

	L1.SignalSize = usLength;
	L1.SignalCode = TOOL_MPH_LTE_PDT_V3;

	uint8* pBuff = NULL;
	try
	{
		pBuff = new uint8[usLength];
	}
	catch (const std::bad_alloc& /*e*/)
	{
		pBuff = NULL;
	}
	if (NULL == pBuff)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Memory allocation failed.", __FUNCTION__);
		return SP_E_PHONE_ALLOC_MEMORY;
	}

	unsigned char *pPos = pBuff;
	uint32 usLen =  sizeof(L1);
	memcpy(pPos, &L1, usLen);
	//Header
	pPos += usLen;
	usLen = sizeof(PC_LTE_PDT_V2_HEADER_T);
	memcpy(pPos, &req->Header, usLen);
	//channel
	pPos += usLen;
	usLen = (unsigned short)(req->Header.ChannelNumber * req->Header.ChannelSize);
	memcpy(pPos, req->Channels, usLen);
	//apt
	pPos += usLen;
	usLen = (unsigned short)(req->Header.AptNumber * req->Header.AptSize);
	memcpy(pPos, req->Apts, usLen);
	DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE); 

	CRecvPkgsList recvList;
	recvList.AddCondition(hd);
	recvList.AddCondition(hd);
	SPRESULT res = SendAndRecv(hd, (const void* )pBuff, usLength, recvList, m_dwTimeOut); 
	delete[] pBuff;
	if (SP_OK != res)
	{
		return res;
	}

	uint32 recvLen = 0;
	//1st packet
	if (!UnpackPRT(recvList.GetPackage(0), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: 1st packet UnpackPRT return False");
		return SP_E_PHONE_INVALID_DATA;
	}
	else if (recvLen != 0)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: 1st packet length != 0");
		return SP_E_PHONE_INVALID_DATA;
	}

	//2nd packet
	if (!UnpackPRT(recvList.GetPackage(1), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: 2nd packet UnpackPRT return False", __FUNCTION__);
		return SP_E_PHONE_INVALID_DATA;
	}
	//fetch result
	unsigned short RetNumber = *(unsigned short*)(m_diagBuff + sizeof(L1_LTE_EMPTY_REQ_T));
	L1_LTE_PDT_RLT* pRLT = (L1_LTE_PDT_RLT* )m_diagBuff;
	uint16 uType = Convert16(pRLT->SignalCode);
	if (TOOL_MPH_LTE_PDT_V3 != uType)
	{
		LogFmtStrA(SPLOGLV_ERROR, "Invalid response command ID: 0x%X != 0x%X", uType, TOOL_MPH_LTE_PDT_V2);
		return SP_E_PHONE_INVALID_DATA;       
	}
	rlt->nCount = RetNumber;

	memcpy(rlt->Value, ((char*)m_diagBuff) + sizeof(L1_LTE_EMPTY_REQ_T) + sizeof(unsigned short), RetNumber * sizeof(unsigned short));

	return SP_OK;
}

SPRESULT CCaliCmd::LteAgc_V3(const PC_LTE_AGC_V3_T* pAgcParam, unsigned int* pAgcRet)
{
	if (NULL == pAgcParam || NULL == pAgcRet)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid parameters!", __FUNCTION__);
		return SP_E_PHONE_INVALID_PARAMETER;
	}

	LogRawStrA(SPLOGLV_INFO, "LTE AGC FDT V3");  

	L1_LTE_EMPTY_REQ_T    L1;    
	ZeroMemory((void* )&L1, sizeof(L1));  

	unsigned short usLength = (unsigned short)(sizeof(L1)
		+ sizeof(PC_LTE_AGC_V2_HEADER_T)
		+ sizeof(PC_LTE_AGC_V3_TRIGGER_T)
		+ pAgcParam->Header.ChannelNumber * pAgcParam->Header.ChannelSize
		+ pAgcParam->Header.PointNumber * pAgcParam->Header.PointSize);

	L1.SignalSize = usLength;
	L1.SignalCode = TOOL_MPH_LTE_AGC_V3;

	unsigned char *lpBuf = new unsigned char[usLength];
	if (NULL == lpBuf)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Memory allocation failed.", __FUNCTION__);
		return SP_E_PHONE_ALLOC_MEMORY;
	}
	else
	{
		ZeroMemory((void *)lpBuf, usLength);
	}

	unsigned char *pPos = lpBuf;
	uint16 usLen =  sizeof(L1);
	memcpy(pPos, &L1, usLen);
	//Header
	pPos += usLen;
	usLen = sizeof(PC_LTE_AGC_V2_HEADER_T);
	memcpy(pPos, &pAgcParam->Header, usLen);
	//Trigger
	pPos += usLen;
	usLen = sizeof(PC_LTE_AGC_V3_TRIGGER_T);
	memcpy(pPos, &pAgcParam->Trigger, usLen);
	//channel
	pPos += usLen;
	usLen = (unsigned short)(pAgcParam->Header.ChannelNumber * pAgcParam->Header.ChannelSize);
	memcpy(pPos, pAgcParam->Channels, usLen);
	//apt
	pPos += usLen;
	usLen = (unsigned short)(pAgcParam->Header.PointNumber * pAgcParam->Header.PointSize);
	memcpy(pPos, pAgcParam->Points, usLen);
	DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE); 
	//////////////////////////////////////////////////////////////////////////
	CRecvPkgsList recvList;
	recvList.AddCondition(hd);
	recvList.AddCondition(hd);
	SPRESULT res = SendAndRecv(hd, (const void* )lpBuf, usLength, recvList, m_dwTimeOut); 
	delete[] lpBuf;
	if (SP_OK != res)
	{
		return res;
	}

	uint32 recvLen = 0;
	//1st packet
	if (!UnpackPRT(recvList.GetPackage(0), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: 1st packet UnpackPRT return False", __FUNCTION__);
		return SP_E_PHONE_INVALID_DATA;
	}
	else if (recvLen != 0)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: 1st packet length != 0", __FUNCTION__);
		return SP_E_PHONE_INVALID_DATA;
	}

	//2nd packet
	if (!UnpackPRT(recvList.GetPackage(1), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: 2nd packet UnpackPRT return False", __FUNCTION__);
		return SP_E_PHONE_INVALID_DATA;
	}
	uint32 unRecLen = sizeof(L1_LTE_EMPTY_REQ_T) + sizeof(short) + (pAgcParam->Header.PointNumber * pAgcParam->Header.PointSize)*2;
	if (recvLen != unRecLen)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: 2nd packet length != sizeof(L1_LTE_FDT_RX_RLT_T), %d != %d", __FUNCTION__, recvLen, unRecLen);
		return SP_E_PHONE_INVALID_LENGTH;
	}
	else
	{
		short RssiNumber = *(short*)(m_diagBuff + sizeof(L1_LTE_EMPTY_REQ_T));
		if (RssiNumber != pAgcParam->Header.PointNumber)
		{
			LogFmtStrA(SPLOGLV_ERROR, "[CCaliCmd::LteAgc_V2] RssiNumber != Header.PointNumber, %d != %d!", RssiNumber, pAgcParam->Header.PointNumber);
			return SP_E_PHONE_INVALID_DATA;
		}
		else
		{
			memcpy(pAgcRet, ((char*)m_diagBuff) + sizeof(L1_LTE_EMPTY_REQ_T) + sizeof(short), RssiNumber * sizeof(int));	
		}
		return SP_OK;
	}
}
//////////////////////////////////////////////////////////////////////////

SPRESULT CCaliCmd::lteIrrGainImbalance(const PC_LTE_IRR_GAIN_IMBALANCE_T* req, PC_LTE_IRR_GAIN_IMBALANCE_RESULT_T* rlt)
{
    if (NULL == req || NULL == rlt)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid parameters!", __FUNCTION__);
        return SP_E_PHONE_INVALID_PARAMETER;
    }

    LogRawStrA(SPLOGLV_INFO, "LTE IRR gain imbalance:");

    L1_LTE_IRR_GAIN_IMBALANCE_REQ_T L1;
    ZeroMemory((void* )&L1, sizeof(L1));

    L1.SignalCode   = Convert16(TOOL_MPH_LTE_IRR_GAIN_IMBALANCE);
    L1.SignalSize   = Convert16((uint16)sizeof(L1));

    L1.BW           = (uint8)(req->eBW);
    L1.Ant          = (uint8)(req->Ant);
    L1.Arfcn        = Convert16(req->Arfcn);
    L1.RxWord       = Convert16(req->RxWord);
    L1.AfcValue     = Convert16(req->AfcValue);

    DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE);

    CRecvPkgsList recvList;
    recvList.AddCondition(hd);
    recvList.AddCondition(hd);
    SPRESULT res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), recvList, m_dwTimeOut); 
    if (SP_OK != res)
    {
        return res;
    }

    for (int i = 0; i < recvList.GetPkgsCount(); i++)
    {
        uint32 recvLen = 0;
        if (!UnpackPRT(recvList.GetPackage(i), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
        {
            assert(0);
            continue;
        }

        if (recvLen >= sizeof(L1_LTE_IRR_GAIN_IMBALANCE_RLT_T))
        {
            L1_LTE_IRR_GAIN_IMBALANCE_RLT_T* pRLT = (L1_LTE_IRR_GAIN_IMBALANCE_RLT_T* )m_diagBuff;
            uint16 uType = Convert16(pRLT->SignalCode);
            if (TOOL_MPH_LTE_IRR_GAIN_IMBALANCE != uType)
            {
                LogFmtStrA(SPLOGLV_ERROR, "Invalid response command ID: 0x%X <> 0x%X", uType, TOOL_MPH_LTE_IRR_GAIN_IMBALANCE);
                return SP_E_PHONE_INVALID_DATA;       
            }

            rlt->I  = Convert16(pRLT->I);
            rlt->IQ = Convert16(pRLT->IQ);
            LogFmtStrA(SPLOGLV_ERROR, "I = %d IQ = %d",rlt->I, rlt->IQ);

            return SP_OK;
        }
    }

    LogRawStrA(SPLOGLV_ERROR, "Invalid response received!");
    return SP_E_PHONE_INVALID_DATA;
}

SPRESULT CCaliCmd::lteIrrTuning(const PC_LTE_IRR_TUNING_T* req, PC_LTE_IRR_TUNING_RESULT_T* rlt)
{
    if (NULL == req || NULL == rlt)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid parameters!", __FUNCTION__);
        return SP_E_PHONE_INVALID_PARAMETER;
    }

    LogRawStrA(SPLOGLV_INFO, "LTE IRR tuning:");

    L1_LTE_IRR_TUNING_REQ_T L1;
    ZeroMemory((void* )&L1, sizeof(L1));

    L1.SignalCode    = Convert16(TOOL_MPH_LTE_FT_IRR);
    L1.SignalSize    = Convert16((uint16)sizeof(L1));

    L1.BW            = Convert16((uint16)req->eBW);
    L1.RxWordIndex[0] = Convert16(req->RxWordIndex[0]);
    L1.RxWordIndex[1] = Convert16(req->RxWordIndex[1]);
    L1.StepCount     = Convert16(req->StepCount);
    L1.CellPower[0] = Convert16(req->CellPower[0]);
    L1.CellPower[1] = Convert16(req->CellPower[1]);
    L1.Arfcn         = Convert32(req->Arfcn);
	L1.BandFlag		= Convert16(req->Indicator);
	L1.nAnt			= (uint8)req->nAnt;
	L1.nAntFlag		= (uint8)req->nAntFlag;

    DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE);

    CRecvPkgsList recvList;
    recvList.AddCondition(hd);
    recvList.AddCondition(hd);
    SPRESULT res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), recvList, m_dwTimeOut); 
    if (SP_OK != res)
    {
        return res;
    }

    uint32 recvLen = 0;
    //1st packet
    if (!UnpackPRT(recvList.GetPackage(0), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: 1st packet UnpackPRT return False", __FUNCTION__);
        return SP_E_PHONE_INVALID_DATA;
    }
    else if (recvLen != 0)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: 1st packet length != 0", __FUNCTION__);
        return SP_E_PHONE_INVALID_DATA;
    }

    //2nd packet
    if (!UnpackPRT(recvList.GetPackage(1), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: 2nd packet UnpackPRT return False", __FUNCTION__);
        return SP_E_PHONE_INVALID_DATA;
    }

    if (recvLen != (16 + sizeof(L1_LTE_IRR_TUNING_RLT_DATA_T)))
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: 2nd packet length != sizeof(L1_LTE_IRR_TUNING_RLT_T), %d != %d", __FUNCTION__, recvLen, (8 + sizeof(L1_LTE_IRR_TUNING_RLT_DATA_T)));
        return SP_E_PHONE_INVALID_LENGTH;
    }

    L1_LTE_IRR_TUNING_RLT_T* pRLT = (L1_LTE_IRR_TUNING_RLT_T* )m_diagBuff;
    uint16 uType = Convert16(pRLT->SignalCode);
    if (TOOL_MPH_LTE_FT_IRR != uType)
    {
        LogFmtStrA(SPLOGLV_ERROR, "Invalid response command ID: 0x%X <> 0x%X", uType, TOOL_MPH_LTE_FT_IRR);
        return SP_E_PHONE_INVALID_DATA;       
    }

    rlt->StepCount = Convert16(pRLT->Data.AfcCount);
    for (int j = 0; j < ARRAY_SIZE(rlt->ArfcnIrrRst); j++)
    {
        rlt->ArfcnIrrRst[j].iAlphaAmp   = Convert16(pRLT->Data.Arfcn[j].iAlphaAmp);
        rlt->ArfcnIrrRst[j].iShiftPhase = Convert16(pRLT->Data.Arfcn[j].iShiftPhase);
        rlt->ArfcnIrrRst[j].iSlopePhase = Convert16(pRLT->Data.Arfcn[j].iSlopePhase);
    }

    return SP_OK;
}

SPRESULT CCaliCmd::lteFdiqCal(const PC_LTE_FDIQ_M_BAND_REQ *lpFdiqReq, PC_LTE_FDIQ_RET_T *lpFdiqRsp)
{
	if (NULL == lpFdiqReq || NULL == lpFdiqRsp)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid parameters!", __FUNCTION__);
		return SP_E_PHONE_INVALID_PARAMETER;
	}

	LogRawStrA(SPLOGLV_INFO, "LTE Fdiq Cal:");

	L1_LTE_FDIQ_REQ L1;
	ZeroMemory((void* )&L1, sizeof(L1));

	L1.SignalCode    = Convert16(CALI_FDIQ_CALI);
	L1.SignalSize    = Convert16((uint16)sizeof(L1));

	L1.nBandNum = Convert32(lpFdiqReq->nBandNum);

	for (unsigned int nBndIndx = 0; nBndIndx < lpFdiqReq->nBandNum; nBndIndx++)
	{
		L1.RxGainWordBand[nBndIndx].BW = Convert16((uint16)(lpFdiqReq->RxGainWordBand[nBndIndx].BW));
		L1.RxGainWordBand[nBndIndx].nFreqGap = Convert16((uint16)(lpFdiqReq->RxGainWordBand[nBndIndx].nFreqGap));
		L1.RxGainWordBand[nBndIndx].nBandFlag = Convert32((uint16)(lpFdiqReq->RxGainWordBand[nBndIndx].nBandFlag));
		L1.RxGainWordBand[nBndIndx].Arfcn = Convert32((uint16)(lpFdiqReq->RxGainWordBand[nBndIndx].Arfcn));

		for (int nLnaIndx = 0; nLnaIndx < 3; nLnaIndx++)
		{
			L1.RxGainWordBand[nBndIndx].RxGainWord[nLnaIndx].nPriRxGainWord = Convert16((uint16)(lpFdiqReq->RxGainWordBand[nBndIndx].RxGainWord[nLnaIndx].nRxGainWord[0]));
			L1.RxGainWordBand[nBndIndx].RxGainWord[nLnaIndx].nDivRxGainWord = Convert16((uint16)(lpFdiqReq->RxGainWordBand[nBndIndx].RxGainWord[nLnaIndx].nRxGainWord[1]));
		}
	}
	L1.nOffsetTime = Convert32(lpFdiqReq->nOffsetTime);

	DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE);

	CRecvPkgsList recvList;
	recvList.AddCondition(hd);
	recvList.AddCondition(hd);
	SPRESULT res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), recvList, m_dwTimeOut); 
	if (SP_OK != res)
	{
		return res;
	}

	uint32 recvLen = 0;
	//1st packet
	if (!UnpackPRT(recvList.GetPackage(0), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: 1st packet UnpackPRT return False", __FUNCTION__);
		return SP_E_PHONE_INVALID_DATA;
	}
	else if (recvLen != 0)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: 1st packet length != 0", __FUNCTION__);
		return SP_E_PHONE_INVALID_DATA;
	}

	//2nd packet
	if (!UnpackPRT(recvList.GetPackage(1), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: 2nd packet UnpackPRT return False", __FUNCTION__);
		return SP_E_PHONE_INVALID_DATA;
	}

	if (recvLen != (16 + sizeof(L1_LTE_FDIQ_RSP_MBAND_PGK)*10))
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: 2nd packet length != sizeof(L1_LTE_IRR_TUNING_RLT_T), %d != %d", __FUNCTION__, recvLen, (8 + sizeof(L1_LTE_IRR_TUNING_RLT_DATA_T)));
		return SP_E_PHONE_INVALID_LENGTH;
	}

	L1_LTE_FDIQ_RSP* pRLT = (L1_LTE_FDIQ_RSP* )m_diagBuff;
	uint16 uType = Convert16(pRLT->SignalCode);
	if (CALI_FDIQ_CALI != uType)
	{
		LogFmtStrA(SPLOGLV_ERROR, "Invalid response command ID: 0x%X <> 0x%X", uType, CALI_FDIQ_CALI);
		return SP_E_PHONE_INVALID_DATA;       
	}

	memcpy(lpFdiqRsp, &pRLT->stRsp, sizeof(L1_LTE_FDIQ_RSP_MBAND_PGK)*10);

	return SP_OK;
}

SPRESULT CCaliCmd::lteIrrPhase(const PC_LTE_IRR_PHASE_T* req, PC_LTE_IRR_PHASE_RESULT_T* rlt)
{
    if (NULL == req || NULL == rlt)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid parameters!", __FUNCTION__);
        return SP_E_PHONE_INVALID_PARAMETER;
    }

    LogRawStrA(SPLOGLV_INFO, "LTE IRR Phase:");

    L1_LTE_IRR_PHASE_REQ_T L1;
    ZeroMemory((void* )&L1, sizeof(L1));

    L1.SignalCode       = Convert16(TOOL_MPH_LTE_IRR_PHASE);
    L1.SignalSize       = Convert16((uint16)sizeof(L1));

    L1.BW               = (uint8)(req->eBW);
    L1.Ant              = (uint8)(req->Ant);
    L1.Arfcn            = Convert16(req->Arfcn);
    L1.RxWord           = Convert16(req->RxWord);
    L1.SignalPosition   = (uint8)(req->SignalPosition);
    L1.AfcValue         = Convert16(req->AfcValue);
    L1.RegNum           = Convert16(req->RegNum);
    for (int i = 0; i < req->RegNum; i++)
    {
        L1.Reg1[i]      = Convert16(req->Reg1[i]);
        L1.Reg2[i]      = Convert16(req->Reg2[i]);
    }

    DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE); 

    CRecvPkgsList recvList;
    recvList.AddCondition(hd);
    recvList.AddCondition(hd);
    SPRESULT res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), recvList, m_dwTimeOut); 
    if (SP_OK != res)
    {
        return res;
    }

    for (int i = 0; i < recvList.GetPkgsCount(); i++)
    {
        uint32 recvLen = 0;
        if (!UnpackPRT(recvList.GetPackage(i), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
        {
            assert(0);
            continue;
        }

        if (recvLen >= sizeof(L1_LTE_IRR_PHASE_RLT_T))
        {
            L1_LTE_IRR_PHASE_RLT_T* pRLT = (L1_LTE_IRR_PHASE_RLT_T* )m_diagBuff;
            uint16 uType = Convert16(pRLT->SignalCode);
            if (TOOL_MPH_LTE_IRR_PHASE != uType)
            {
                LogFmtStrA(SPLOGLV_ERROR, "Invalid response command ID: 0x%X <> 0x%X", uType, TOOL_MPH_LTE_IRR_PHASE);
                return SP_E_PHONE_INVALID_DATA;       
            }

            rlt->nCount = Convert32(pRLT->nCount);
            for (uint32 j=0; j<rlt->nCount; j++)
            {
                rlt->RSSI[j] = Convert16(pRLT->RSSI[j]);
            }

            return SP_OK;
        }
    }

    LogRawStrA(SPLOGLV_ERROR, "Invalid response received!");
    return SP_E_PHONE_INVALID_DATA;
}

SPRESULT CCaliCmd::ltePDT(const PC_LTE_PDT_REQ_T* req, PC_LTE_PDT_RESULT_T* rlt)
{
    if (NULL == req || NULL == rlt)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid parameters!", __FUNCTION__);
        return SP_E_PHONE_INVALID_PARAMETER;
    }

    LogRawStrA(SPLOGLV_INFO, "LTE Power Detect:");

    L1_LTE_PDT_REQ_T L1;
    ZeroMemory((void* )&L1, sizeof(L1));

    L1.SignalCode       = Convert16(TOOL_MPH_LTE_PDT);
    L1.SignalSize       = Convert16((uint16)sizeof(L1));

    L1.ChannelNumber    = (uint8)req->ChannelNumber;
    L1.BW               = (uint8)req->eBW;
    L1.VctcxoAfc        = Convert16(req->VctcxoAfc);
    L1.Range            = 3;

    for (int i = 0; i < req->ChannelNumber; i++)
    {
        L1.ChannelConfig[i].Arfcn          = Convert16(req->arrChannel[i].Arfcn);
        L1.ChannelConfig[i].PointNumber    = (uint8)req->arrChannel[i].PointNumber;
        L1.ChannelConfig[i].RbNumber       = req->arrChannel[i].RbNumber;
        L1.ChannelConfig[i].RbPosition     = req->arrChannel[i].RbPosition;
        L1.ChannelConfig[i].Apt            = req->arrChannel[i].Apt;
        L1.ChannelConfig[i].PaMode         = req->arrChannel[i].PaMode;

        for (int j = 0; j < req->arrChannel[i].PointNumber; j++)
        {
            L1.ChannelConfig[i].ControlWord[j] = Convert16(req->arrChannel[i].ControlWord[j]);
        }

        L1.BandIndicator[i] = req->BandIndicator[i]; 
    }

    DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE); 

    CRecvPkgsList recvList;
    recvList.AddCondition(hd);
    recvList.AddCondition(hd);
    SPRESULT res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), recvList, m_dwTimeOut); 
    if (SP_OK != res)
    {
        return res;
    }

    uint32 recvLen = 0;
    //1st packet
    if (!UnpackPRT(recvList.GetPackage(0), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: 1st packet UnpackPRT return False");
        return SP_E_PHONE_INVALID_DATA;
    }
    else if (recvLen != 0)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: 1st packet length != 0");
        return SP_E_PHONE_INVALID_DATA;
    }

    //2nd packet
    if (!UnpackPRT(recvList.GetPackage(1), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: 2nd packet UnpackPRT return False", __FUNCTION__);
        return SP_E_PHONE_INVALID_DATA;
    }

    if (recvLen != sizeof(L1_LTE_PDT_RLT))
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: 2nd packet length != sizeof(L1_LTE_PDT_RLT), %d != %d", __FUNCTION__, recvLen, sizeof(L1_LTE_FDT_RX_RLT_T));
        return SP_E_PHONE_INVALID_LENGTH;
    }
    else
    {
        L1_LTE_PDT_RLT* pRLT = (L1_LTE_PDT_RLT* )m_diagBuff;
        uint16 uType = Convert16(pRLT->SignalCode);
        if (TOOL_MPH_LTE_PDT != uType)
        {
            LogFmtStrA(SPLOGLV_ERROR, "Invalid response command ID: 0x%X != 0x%X", uType, TOOL_MPH_LTE_PDT);
            return SP_E_PHONE_INVALID_DATA;       
        }

        rlt->nCount = Convert16(pRLT->RetNumber);
        for (int j = 0; j < rlt->nCount; j++)
        {
            rlt->Value[j] = Convert16(pRLT->Value[j]);
        }
        rlt->reserved = Convert16(pRLT->reserved);

        return SP_OK;
    }
}

SPRESULT CCaliCmd::lteSwitch(const PC_LTE_SWITCH_REQ_T* req, LPCVOID lpData)
{
    if (NULL == req || NULL == lpData)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid parameters!", __FUNCTION__);
        return SP_E_PHONE_INVALID_PARAMETER;
    }

    LogFmtStrA(SPLOGLV_INFO, "LTE Switch: %d", req->type);

    L1_LTE_SWITCH_REQ_T  L1;
    ZeroMemory((void* )&L1, sizeof(L1));

    uint32 u32Size  = sizeof(L1) + req->size;
    L1.SignalCode   = Convert16(TOOL_MPH_LTE_SWITCH);
    L1.SignalSize   = Convert16((uint16)u32Size);

    L1.type         = Convert16((uint16)req->type);
    L1.size         = Convert16((uint16)req->size);

    std::vector<uint8> vec(u32Size, 0x00);
    memcpy((void* )&vec[0], (const void* )&L1, sizeof(L1));
    memcpy((void* )&vec[sizeof(L1)], lpData, req->size);

    DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE);

    CRecvPkgsList recvList;
    recvList.AddCondition(hd);
    recvList.AddCondition(hd);
    SPRESULT res = SendAndRecv(hd, (const void* )&vec[0], u32Size, recvList, m_dwTimeOut); 
    if (SP_OK != res)
    {
        return res;
    }

    for (int i = 0; i < recvList.GetPkgsCount(); i++)
    {
        uint32 recvLen = 0;
        if (!UnpackPRT(recvList.GetPackage(i), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
        {
            assert(0);
            LogFmtStrA(SPLOGLV_ERROR, "%s: UnpackPRT return False", __FUNCTION__);
            return SP_E_PHONE_INVALID_DATA;
        }
    }

    return SP_OK;
}

SPRESULT CCaliCmd::lteTxDC(const PC_LTE_DC_CAL_CONFIG_T* req)
{
    CheckValidPointer(req);

    LogRawStrA(SPLOGLV_INFO, "LTE DC Cal:");

    L1_LTE_TX_DC_REQ_T L1;
    ZeroMemory((void* )&L1, sizeof(L1));

    L1.SignalCode   = Convert16(TOOL_MPH_LTE_DC_CAL);
    L1.SignalSize   = Convert16((uint16)sizeof(L1));

    L1.ChannelNumber  = req->ChannelNumber;
    L1.BW           = (uint8)req->eBW;

    for (int i = 0; i < req->ChannelNumber; i++)
    {
        L1.ChannelConfig[i].ARFCN            = Convert16(req->ChannelConfig[i].ARFCN);
        L1.ChannelConfig[i].BandIndicator    = req->ChannelConfig[i].BandIndicator;
        L1.ChannelConfig[i].StepBegin        = Convert16(req->ChannelConfig[i].StepBegin);
        L1.ChannelConfig[i].StepLength       = Convert16(req->ChannelConfig[i].StepLength);
        L1.ChannelConfig[i].StepNumber       = Convert16(req->ChannelConfig[i].StepNumber);
        L1.ChannelConfig[i].RbNumber         = req->ChannelConfig[i].RbNumber;
        L1.ChannelConfig[i].RbPosition       = req->ChannelConfig[i].RbPosition;
        L1.ChannelConfig[i].LoopItem         = (uint8)req->ChannelConfig[i].LoopItem;
        L1.ChannelConfig[i].FixedItemValue   = Convert16(req->ChannelConfig[i].FixedItemValue);
        L1.ChannelConfig[i].Apt              = req->ChannelConfig[i].Apt;
        L1.ChannelConfig[i].PaMode           = req->ChannelConfig[i].PaMode;
        L1.ChannelConfig[i].ControlWord      = Convert16(req->ChannelConfig[i].ControlWord);
    }

    DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE);

    return SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, NULL, 0, NULL, m_dwTimeOut);
}

SPRESULT CCaliCmd::lteLoadNV(PC_LTE_NV_DATA_T* nv)
{
    CheckValidPointer(nv);

    LogFmtStrA(SPLOGLV_INFO, "%s: nv = %d, Count = %d", __FUNCTION__, nv->eNvType, nv->nDataCount);

    L1_LTE_NV_READ_T L1;
    ZeroMemory((void* )&L1, sizeof(L1));

    L1.eNvType    = Convert32((uint32)nv->eNvType);
    L1.nDataCount = Convert16((uint16)nv->nDataCount);    

    DeclareDiagHeader(hd, DIAG_LTE_NV, LTE_NV_READ); 

    uint32 recvLen = 0;
    SPRESULT   res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);
    if (SP_OK == res)
    {
        if (recvLen > sizeof(L1_LTE_NV_WRITE_T))
        {
            LogFmtStrA(SPLOGLV_ERROR, "Invalid response length %d > %d!", recvLen, sizeof(L1_LTE_NV_WRITE_T));
            return SP_E_PHONE_INVALID_LENGTH;
        }

        L1_LTE_NV_WRITE_T* lpData = (L1_LTE_NV_WRITE_T* )m_diagBuff;
        uint32 dataLen = sizeof(lpData->nData[0]) * (lpData->nDataCount);
        uint32 expLen  = sizeof(L1_LTE_NV_READ_T) + dataLen;
        if (recvLen != expLen)
        {
            LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid response length %d != %d!", __FUNCTION__, recvLen, expLen);
            return SP_E_PHONE_INVALID_LENGTH;
        }
        nv->nDataCount = lpData->nDataCount;

        Convert16((uint8* )&lpData->nData[0], lpData->nDataCount * sizeof(uint16));
        memcpy((void* )&nv->nData[0], (const void* )&lpData->nData[0], lpData->nDataCount * sizeof(uint16));
    }

    return res;
}

SPRESULT CCaliCmd::lteSaveNV(const PC_LTE_NV_DATA_T* nv)
{
    CheckValidPointer(nv);

    LogFmtStrA(SPLOGLV_INFO, "Save LTE NV: nv = %d, Count = %d", nv->eNvType, nv->nDataCount);
    if (nv->nDataCount > MAX_PC_LTE_NV_LEN)
    {
        LogFmtStrA(SPLOGLV_ERROR, "Too many NV data count! < %d", MAX_PC_LTE_NV_LEN);
        return SP_E_PHONE_INVALID_PARAMETER;
    }

    L1_LTE_NV_WRITE_T *pL1 = new L1_LTE_NV_WRITE_T;
    ZeroMemory((void* )pL1, sizeof(L1_LTE_NV_WRITE_T));

    pL1->eNvType    = Convert32((uint32)nv->eNvType);
    pL1->nDataCount = Convert16((uint16)nv->nDataCount); 

    uint32 u32DataSize = sizeof(uint16) * nv->nDataCount;
    memcpy((void* )&pL1->nData[0], (const void* )&nv->nData[0], u32DataSize);
    Convert16((uint8* )&pL1->nData[0], u32DataSize);

    uint32 u32SendSize = sizeof(pL1->eNvType) + sizeof(pL1->nDataCount) + u32DataSize;

    DeclareDiagHeader(hd, DIAG_LTE_NV, LTE_NV_WRITE); 

    SPRESULT ret = SendAndRecv(hd, (const void* )pL1, u32SendSize, hd, NULL, 0, NULL, m_dwTimeOut);

	delete pL1;

    if (ret == SP_OK && m_pContainer != NULL)
    {
        std::wstring strKey= CLteUtility::GetShareKey(CLteUtility::V1, nv->eNvType);
        m_pContainer->SetValue(strKey.c_str(), nv->nData, nv->nDataCount * sizeof(nv->nData[0]));
    }

    return ret;
}

SPRESULT CCaliCmd::lteSaveToFlash(uint32 u32TimeOut)
{
    LogFmtStrA(SPLOGLV_INFO, "lteSaveToFlash: TimeOut = %d", u32TimeOut);

    DeclareDiagHeader(hd, DIAG_LTE_NV, LTE_NV_SAVETOFLASH); 
    return SendAndRecv(hd, NULL, 0, hd, NULL, 0, NULL, u32TimeOut);
}

SPRESULT CCaliCmd::lteLoadCalFlag(uint16* lpflag)
{
    CheckValidPointer(lpflag);

    LogRawStrA(SPLOGLV_INFO, "Load LTE calibration flag:");

    PC_LTE_NV_DATA_T nv;
    ZeroMemory((void* )&nv, sizeof(nv));
    nv.eNvType   = LTE_NV_TYPE_CALI_PARAM_FLAG;
    nv.nDataCount = 1;
    SPRESULT res = lteLoadNV(&nv);
    if (SP_OK == res)
    {
        *lpflag = *((uint16 *)(nv.nData));
        LogFmtStrA(SPLOGLV_INFO, "LTE calibration flag = 0x%X!", *lpflag);
    }

    return res; 
}

SPRESULT CCaliCmd::lteSaveCalFlag(uint16 flag)
{
    LogFmtStrA(SPLOGLV_INFO, "Save LTE calibration flag 0x%X.", flag);

    PC_LTE_NV_DATA_T nv; 
    ZeroMemory((void* )&nv, sizeof(nv));
    nv.eNvType    = LTE_NV_TYPE_CALI_PARAM_FLAG;
    nv.nDataCount  = 1;
    memcpy((void* )&nv.nData, (const void* )&flag, nv.nDataCount);

    return lteSaveNV(&nv);
}

SPRESULT CCaliCmd::lteLoadNV_V2(PC_LTE_NV_V2_DATA_T* nv)
{
    CheckValidPointer(nv);

    LogFmtStrA(SPLOGLV_INFO, "Load LTE NV V2: nv = %d", nv->eNvType);

    L1_LTE_NV_V2_READ_T L1;
    ZeroMemory((void* )&L1, sizeof(L1));

    L1.eNvType  = Convert32(nv->eNvType);
    L1.nBandNum = Convert16(nv->nBandNum);	
    L1.reserve  = Convert16(nv->reserve);

    for (int i = 0; i < nv->nBandNum; i++)
    {
        L1.headinfo[i].isPartion = Convert16(nv->headinfo[i].isPartion);
        L1.headinfo[i].pationNum = Convert16(nv->headinfo[i].pationNum);

        for (int j = 0; j < ARRAY_SIZE(L1.headinfo[i].pationLen); j++)
        {
            L1.headinfo[i].pationLen[j] = Convert16(nv->headinfo[i].pationLen[j]);
        }

        L1.headinfo[i].band = Convert16(nv->headinfo[i].band);
        L1.headinfo[i].BandDataCount = Convert16(nv->headinfo[i].BandDataCount);

        for (int j = 0; j < ARRAY_SIZE(L1.headinfo[i].reserve); j++)
        {
            L1.headinfo[i].reserve[j] = Convert16(nv->headinfo[i].reserve[j]);
        }
    }

    DeclareDiagHeader(hd, DIAG_LTE_NV, LTE_NV_READ_V2); 

    uint32 recvLen = 0;
    SPRESULT   res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);
    if (SP_OK == res)
    {
        if (recvLen > sizeof(L1_LTE_NV_V2_WRITE_T))
        {
            LogFmtStrA(SPLOGLV_ERROR, "Invalid response length %d > %d!", recvLen, sizeof(L1_LTE_NV_V2_WRITE_T));
            return SP_E_PHONE_INVALID_LENGTH;
        }

        L1_LTE_NV_V2_WRITE_T* lpData = (L1_LTE_NV_V2_WRITE_T* )m_diagBuff;
        uint32 u32type = Convert32(lpData->eNvType);
        if ((uint32)nv->eNvType != u32type)
        {
            LogFmtStrA(SPLOGLV_ERROR, "Incorrect NV data received, type = %d", u32type);
            return SP_E_PHONE_INVALID_DATA;       
        }
        
        uint32 uDataLength = 0;
        for (int i = 0; i < nv->nBandNum; i++)
        {
            uDataLength += lpData->headinfo[i].BandDataCount * sizeof(uint16);
        }
        uDataLength += sizeof(L1_LTE_NV_V2_READ_T);

     //   Convert16(((uint8*)lpData), uDataLength);

        if (recvLen != uDataLength)
        {
            LogFmtStrA(SPLOGLV_ERROR, "Invalid response length %d != %d!", recvLen, uDataLength);
            return SP_E_PHONE_INVALID_LENGTH;
        }

        memcpy(nv, lpData, uDataLength);
    }

    return res;
}

SPRESULT CCaliCmd::lteSaveNV_V2(const PC_LTE_NV_V2_DATA_T* nv)
{
    CheckValidPointer(nv);

    LogFmtStrA(SPLOGLV_INFO, "Save LTE NV V2: nv = %d", nv->eNvType);

    L1_LTE_NV_V2_WRITE_T L1;
    ZeroMemory((void* )&L1, sizeof(L1));

    L1.eNvType  = Convert32(nv->eNvType);
    L1.nBandNum = Convert16(nv->nBandNum);	
    L1.reserve  = Convert16(nv->reserve);

    uint32 u32DataCount = 0;
    for (int i = 0; i < nv->nBandNum; i++)
    {
        u32DataCount += nv->headinfo[i].BandDataCount;

        L1.headinfo[i].isPartion = Convert16(nv->headinfo[i].isPartion);
        L1.headinfo[i].pationNum = Convert16(nv->headinfo[i].pationNum);

        for (int j = 0; j < ARRAY_SIZE(L1.headinfo[i].pationLen); j++)
        {
            L1.headinfo[i].pationLen[j] = Convert16(nv->headinfo[i].pationLen[j]);
        }

        L1.headinfo[i].band = Convert16(nv->headinfo[i].band);
        L1.headinfo[i].BandDataCount = Convert16(nv->headinfo[i].BandDataCount);
        
        for (int j = 0; j < ARRAY_SIZE(L1.headinfo[i].reserve); j++)
        {
            L1.headinfo[i].reserve[j] = Convert16(nv->headinfo[i].reserve[j]);
        }
    } 

    if (u32DataCount > MAX_PC_LTE_NV_LEN)
    {
        LogFmtStrA(SPLOGLV_ERROR, "Too many NV data count! %d < %d", u32DataCount, MAX_PC_LTE_NV_LEN);
        return SP_E_PHONE_INVALID_PARAMETER;
    }

    uint32 u32DataSize = sizeof(L1.nData[0]) * u32DataCount;

    memcpy((void* )&L1.nData[0], (const void* )&nv->nData[0], u32DataSize);
    Convert16((uint8* )&L1.nData[0], u32DataSize);

    uint32 u32SendSize = sizeof(L1_LTE_NV_V2_READ_T) + u32DataSize;
    DeclareDiagHeader(hd, DIAG_LTE_NV, LTE_NV_WRITE_V2); 
   
    return SendAndRecv(hd, (const void* )&L1, u32SendSize, hd, (void* )m_diagBuff, sizeof(m_diagBuff), NULL, m_dwTimeOut);
}

SPRESULT CCaliCmd::lteLoadNV_V3( PC_LTE_NV_V3_DATA_T* nv )
{
    CheckValidPointer(nv);

    LogFmtStrA(SPLOGLV_INFO, "Load LTE NV V3: nv = %d", nv->eNvType);

    L1_LTE_NV_V3_READ_REQ_T L1;
    ZeroMemory((void* )&L1, sizeof(L1));

    L1.Type  = Convert32(nv->eNvType);
    L1.Position = Convert16(nv->Position);	
    L1.Band  = nv->Band;
    L1.Indicator  = nv->Indicator;

    DeclareDiagHeader(hd, DIAG_LTE_NV, LTE_NV_READ_V3); 

    uint32 recvLen = 0;
    SPRESULT res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);

    if (SP_OK == res)
    {
        L1_LTE_NV_V3_READ_RLT_T* pRLT = (L1_LTE_NV_V3_READ_RLT_T* )m_diagBuff;

        nv->DataSize = Convert16(pRLT->Size);
        nv->Position = Convert16(pRLT->Position);
    
        if (nv->DataSize > MAX_LTE_L1_NV_LEN_V3)
        {
            LogFmtStrA(SPLOGLV_ERROR, "SP_lteLoadNV_V3 Invalid Data size: %d > Max Size: %d", nv->DataSize, MAX_LTE_L1_NV_LEN_V3);
            return SP_E_PHONE_INVALID_LENGTH;
        }
        else if (recvLen != (nv->DataSize + 4) * sizeof(int16))
        {
            LogFmtStrA(SPLOGLV_ERROR, "SP_lteLoadNV_V3 Invalid response size: %d != (nv->DataSize + 4) * sizeof(int16): %d", nv->DataSize, (nv->DataSize + 4) * sizeof(int16));
            return SP_E_PHONE_INVALID_LENGTH;
        }

        memcpy(&nv->nData[0], &pRLT->nData[0], nv->DataSize * sizeof(int16));
    }

    return res;
}

SPRESULT CCaliCmd::lteSaveNV_V3( const PC_LTE_NV_V3_DATA_T* nv )
{
    CheckValidPointer(nv);

    LogFmtStrA(SPLOGLV_INFO, "Save LTE NV V3: nv = %d", nv->eNvType);

    L1_LTE_NV_V3_WRITE_REQ_T L1;
    ZeroMemory((void* )&L1, sizeof(L1));

    L1.Type  = Convert32(nv->eNvType);
    L1.Position = Convert16(nv->Position);	
    L1.Band  = nv->Band;
    L1.Indicator  = nv->Indicator;

    DeclareDiagHeader(hd, DIAG_LTE_NV, LTE_NV_WRITE_V3); 

    if (nv->DataSize > MAX_LTE_L1_NV_LEN_V3)
    {
        LogFmtStrA(SPLOGLV_ERROR, "SP_lteSaveNV_V3 Invalid Data size: %d > Max Size: %d", nv->DataSize, MAX_LTE_L1_NV_LEN_V3);
        return SP_E_PHONE_INVALID_LENGTH;
    }

    memcpy(&L1.nData[0], &nv->nData[0], sizeof(uint16) * nv->DataSize);

    uint32 usLength = 8 + sizeof(uint16) * nv->DataSize;

    SPRESULT ret = SendAndRecv(hd, (const void* )&L1, usLength, hd, NULL, 0, NULL, m_dwTimeOut);

    if (ret == SP_OK && m_pContainer != NULL)
    {
        std::wstring strKey = CLteUtility::GetShareKey(CLteUtility::V3, nv->eNvType, nv->Band, nv->Indicator);
        m_pContainer->SetValue(strKey.c_str(), nv->nData, nv->DataSize * sizeof(nv->nData[0]));
    }

    return ret;
}

SPRESULT CCaliCmd::lteNST_Init(BOOL bActive)
{
    LogFmtStrA(SPLOGLV_INFO, "LTE NST %s:", bActive ? "Active" : "DeActive");

    SPRESULT res = SP_OK;

    if (bActive)
    {
        L1_LTE_EMPTY_REQ_T L1;
        ZeroMemory((void* )&L1,  sizeof(L1));

        L1.SignalCode  = Convert16(TOOL_MPH_LTE_NST_INIT);
        L1.SignalSize  = Convert16((uint16)sizeof(L1));

        DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE);

        CRecvPkgsList recvList;
        recvList.AddCondition(hd);
        recvList.AddCondition(hd);
        res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), recvList, m_dwTimeOut);
    } 
    else
    {
        L1_LTE_EMPTY_REQ_T L1;
        ZeroMemory((void* )&L1, sizeof(L1));

        L1.SignalCode  = Convert16(TOOL_MPH_LTE_NST_END);
        L1.SignalSize  = Convert16((uint16)sizeof(L1));

        DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE);

        res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, NULL, 0, NULL, m_dwTimeOut);
    }

    if (SP_OK != res)
    {
        LogFmtStrA(SPLOGLV_ERROR, "lteNST_Init Fail!");
    }

    return res;
}

SPRESULT CCaliCmd::lteNST_Sync(const PC_LTE_NST_SYNC_T* req, LTE_NST_STATUS_E* lpStatus)
{
    if (NULL == req || NULL == lpStatus)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid parameters!", __FUNCTION__);
        return SP_E_PHONE_INVALID_PARAMETER;
    }

    LogFmtStrA(SPLOGLV_INFO, "LTE NST SYNC: Cell = %d, arfcn = %d, RB = %d,(%d), BW = %d, MCS = %d, TDD = %d, CellPower = %.1f, RTNI = %d, version = %d, precoding = %d, Ant = %d", \
        req->Cell_id, 
        req->Arfcn, 
        req->RB_num, 
        req->RB_pos, 
        req->BW, 
        req->MCS, 
        req->TDD_frameconfig, 
        req->CellPower, 
        req->RTNI, 
        req->redundancy_version, 
        req->precoding_information, 
        req->Ant
        );

    L1_LTE_NST_SYNC_REQ_T L1;
    ZeroMemory((void* )&L1,  sizeof(L1));
    L1.SignalCode      = Convert16(TOOL_MPH_LTE_NST_SYNC);
    L1.SignalSize      = Convert16((uint16)sizeof(L1));

    L1.Cell_id                  = Convert16(req->Cell_id);
    L1.Arfcn                    = Convert16((uint16)req->Arfcn);
    L1.RB_num                   = (uint8)req->RB_num;
    L1.RB_pos                   = (uint8)req->RB_pos;
    L1.BW                       = (uint8)req->BW;
    L1.MCS                      = (uint8)req->MCS;
    L1.TDD_frameconfig          = (uint8)req->TDD_frameconfig;
    L1.CellPower                = Convert16((uint16)(req->CellPower*10));
    L1.RTNI                     = Convert16(req->RTNI);
    L1.redundancy_version       = Convert16(req->redundancy_version);
    L1.precoding_information    = Convert16(req->precoding_information);
    L1.Ant                      = Convert16(req->Ant);

    DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE); 

    CRecvPkgsList recvList;
    recvList.AddCondition(hd);
    recvList.AddCondition(hd);
    SPRESULT res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), recvList, m_dwTimeOut); 
    if (SP_OK != res)
    {
        return res;
    }

    for (int i = 0; i < recvList.GetPkgsCount(); i++)
    {
        uint32 recvLen = 0;
        if (!UnpackPRT(recvList.GetPackage(i), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
        {
            assert(0);
            continue;
        }

        if (recvLen >= sizeof(L1_LTE_NST_SYNC_RLT_T))
        {
            L1_LTE_NST_SYNC_RLT_T* pRLT = (L1_LTE_NST_SYNC_RLT_T* )m_diagBuff;
            uint16 uType = Convert16(pRLT->SignalCode);
            if (TOOL_MPH_LTE_NST_SYNC != uType)
            {
                LogFmtStrA(SPLOGLV_ERROR, "Invalid response command ID: 0x%X <> 0x%X", uType, TOOL_MPH_LTE_NST_SYNC);
                return SP_E_PHONE_INVALID_DATA;       
            }

            *lpStatus = static_cast<LTE_NST_STATUS_E>(Convert32(pRLT->status));
            LogFmtStrA(SPLOGLV_INFO, "SYNC status = %d", *lpStatus);

            return SP_OK;
        }
    }

    LogRawStrA(SPLOGLV_ERROR, "Invalid response received!");
    return SP_E_PHONE_INVALID_DATA;
}

SPRESULT CCaliCmd::lteNST_Sync_V2(const PC_LTE_NST_SYNC_T* req, LTE_NST_STATUS_E* lpStatus)
{
    if (NULL == req || NULL == lpStatus)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid parameters!", __FUNCTION__);
        return SP_E_PHONE_INVALID_PARAMETER;
    }

    LogFmtStrA(SPLOGLV_INFO, "LTE NST SYNC V2: Cell = %d, arfcn = %d, RB = %d,(%d), BW = %d, MCS = %d, TDD = %d, CellPower = %.1f, RTNI = %d, version = %d, precoding = %d, Ant = %d", \
        req->Cell_id, 
        req->Arfcn, 
        req->RB_num, 
        req->RB_pos, 
        req->BW, 
        req->MCS, 
        req->TDD_frameconfig, 
        req->CellPower, 
        req->RTNI, 
        req->redundancy_version, 
        req->precoding_information, 
        req->Ant
        );

    L1_LTE_NST_SYNC_V2_REQ_T L1;
    ZeroMemory((void* )&L1,  sizeof(L1));
    L1.SignalCode      = Convert16(TOOL_MPH_LTE_NST_SYNC_V2);
    L1.SignalSize      = Convert16((uint16)sizeof(L1));

    L1.Cell_id                  = Convert16(req->Cell_id);
    L1.Arfcn                    = Convert32(req->Arfcn);
    L1.RB_num                   = (uint8)req->RB_num;
    L1.RB_pos                   = (uint8)req->RB_pos;
    L1.BW                       = (uint8)req->BW;
    L1.MCS                      = (uint8)req->MCS;
    L1.TDD_frameconfig          = (uint8)req->TDD_frameconfig;
    L1.CellPower                = Convert16((uint16)(req->CellPower*10));
    L1.RTNI                     = Convert16(req->RTNI);
    L1.redundancy_version       = Convert16(req->redundancy_version);
    L1.precoding_information    = Convert16(req->precoding_information);
    L1.Ant                      = Convert16(req->Ant);

    DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE); 

    CRecvPkgsList recvList;
    recvList.AddCondition(hd);
    recvList.AddCondition(hd);
    SPRESULT res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), recvList, m_dwTimeOut); 
    if (SP_OK != res)
    {
        return res;
    }

    for (int i = 0; i < recvList.GetPkgsCount(); i++)
    {
        uint32 recvLen = 0;
        if (!UnpackPRT(recvList.GetPackage(i), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
        {
            assert(0);
            continue;
        }

        if (recvLen >= sizeof(L1_LTE_NST_SYNC_RLT_T))
        {
            L1_LTE_NST_SYNC_RLT_T* pRLT = (L1_LTE_NST_SYNC_RLT_T* )m_diagBuff;
            uint16 uType = Convert16(pRLT->SignalCode);
            if (TOOL_MPH_LTE_NST_SYNC_V2 != uType)
            {
                LogFmtStrA(SPLOGLV_ERROR, "Invalid response command ID: 0x%X <> 0x%X", uType, TOOL_MPH_LTE_NST_SYNC_V2);
                return SP_E_PHONE_INVALID_DATA;       
            }

            *lpStatus = static_cast<LTE_NST_STATUS_E>(Convert32(pRLT->status));
            LogFmtStrA(SPLOGLV_INFO, "SYNC status = %d", *lpStatus);

            return SP_OK;
        }
    }

    LogRawStrA(SPLOGLV_ERROR, "Invalid response received!");
    return SP_E_PHONE_INVALID_DATA;
}

SPRESULT CCaliCmd::lteNST_Start(const PC_LTE_NST_CONFIG_T* req)
{
    CheckValidPointer(req);

    LogFmtStrA(SPLOGLV_INFO, "LTE NST Start: Arfcn Count = %d, Ant = %d", req->arfcn_num, req->Ant);

    L1_LTE_NST_CONFIG_REQ_T L1;
    ZeroMemory((void* )&L1, sizeof(L1));

    L1.SignalCode   = Convert16(TOOL_MPH_LTE_NST_START_TEST);
    L1.SignalSize   = Convert16((uint16)sizeof(L1));

    L1.arfcn_num    = Convert16(req->arfcn_num);
    L1.Ant          = Convert16(req->Ant);
    for (uint16 i = 0; i < req->arfcn_num; i++)
    {
        L1.arfcn[i].arfcn      = Convert16((uint16)req->arfcn[i].arfcn);
        L1.arfcn[i].frame_num  = Convert16(req->arfcn[i].frame_num);

        // rx
        if (req->arfcn[i].rx.frame_num > req->arfcn[i].frame_num)
        {
            LogFmtStrA(SPLOGLV_ERROR, "Arfcn[%2d] = %d, Invalid rx frame count, %d <= %d", i, req->arfcn[i].arfcn, req->arfcn[i].rx.frame_num, req->arfcn[i].frame_num);
            return SP_E_PHONE_INVALID_PARAMETER;
        }

        L1.arfcn[i].rx.frame_num   = (uint8)req->arfcn[i].rx.frame_num;
        L1.arfcn[i].rx.CellPower   = Convert16((uint16)(req->arfcn[i].rx.CellPower*10));

        // tx
        int   total_tx_frames = 0;
        L1.arfcn[i].tx_count  = req->arfcn[i].tx_count;
        for (uint8 j = 0; j < req->arfcn[i].tx_count; j++)
        {
            total_tx_frames += req->arfcn[i].tx[j].frame_num;

            L1.arfcn[i].tx[j].frame_num        = (uint8)req->arfcn[i].tx[j].frame_num;
            L1.arfcn[i].tx[j].RB_num           = (uint8)req->arfcn[i].tx[j].RB_num;
            L1.arfcn[i].tx[j].RB_pos           = (uint8)req->arfcn[i].tx[j].RB_pos;
            L1.arfcn[i].tx[j].TPC              = (uint8)req->arfcn[i].tx[j].TPC;
            L1.arfcn[i].tx[j].closeloop_power  = (int8 )req->arfcn[i].tx[j].closeloop_power;
            L1.arfcn[i].tx[j].BW               = (uint8)req->arfcn[i].tx[j].BW;
            L1.arfcn[i].tx[j].MCS              = (uint8)req->arfcn[i].tx[j].MCS;
            L1.arfcn[i].tx[j].reserved         = (uint8)req->arfcn[i].tx[j].reserved;
        }

        if (total_tx_frames > req->arfcn[i].frame_num)
        {
            LogFmtStrA(SPLOGLV_ERROR, "Arfcn[%2d] = %d, Invalid total tx frame count, %d <= %d", i, req->arfcn[i].arfcn, total_tx_frames, req->arfcn[i].frame_num);
            return SP_E_PHONE_INVALID_PARAMETER;
        }
    }

    DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE); 

    return SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, NULL, 0, NULL, m_dwTimeOut);
}

SPRESULT CCaliCmd::lteNST_Start_V2(const PC_LTE_NST_CONFIG_T* req)
{
    CheckValidPointer(req);

    LogFmtStrA(SPLOGLV_INFO, "LTE NST Start V2: Arfcn Count = %d, Ant = %d", req->arfcn_num, req->Ant);

    L1_LTE_NST_CONFIG_V2_REQ_T L1;
    ZeroMemory((void* )&L1, sizeof(L1));

    L1.SignalCode   = Convert16(TOOL_MPH_LTE_NST_START_TEST_V2);
    L1.SignalSize   = Convert16((uint16)sizeof(L1));

    L1.arfcn_num    = Convert16(req->arfcn_num);
    L1.Ant          = Convert16(req->Ant);
    for (uint16 i = 0; i < req->arfcn_num; i++)
    {
        L1.arfcn[i].arfcn      = Convert32(req->arfcn[i].arfcn);
        L1.arfcn[i].frame_num  = Convert16(req->arfcn[i].frame_num);

        // rx
        if (req->arfcn[i].rx.frame_num > req->arfcn[i].frame_num)
        {
            LogFmtStrA(SPLOGLV_ERROR, "Arfcn[%2d] = %d, Invalid rx frame count, %d <= %d", i, req->arfcn[i].arfcn, req->arfcn[i].rx.frame_num, req->arfcn[i].frame_num);
            return SP_E_PHONE_INVALID_PARAMETER;
        }

        L1.arfcn[i].rx.frame_num   = (uint8)req->arfcn[i].rx.frame_num;
        L1.arfcn[i].rx.CellPower   = Convert16((uint16)(req->arfcn[i].rx.CellPower*10));

        // tx
        int   total_tx_frames = 0;
        L1.arfcn[i].tx_count  = req->arfcn[i].tx_count;
        for (uint8 j = 0; j < req->arfcn[i].tx_count; j++)
        {
            total_tx_frames += req->arfcn[i].tx[j].frame_num;

            L1.arfcn[i].tx[j].frame_num        = (uint8)req->arfcn[i].tx[j].frame_num;
            L1.arfcn[i].tx[j].RB_num           = (uint8)req->arfcn[i].tx[j].RB_num;
            L1.arfcn[i].tx[j].RB_pos           = (uint8)req->arfcn[i].tx[j].RB_pos;
            L1.arfcn[i].tx[j].TPC              = (uint8)req->arfcn[i].tx[j].TPC;
            L1.arfcn[i].tx[j].closeloop_power  = (int8 )req->arfcn[i].tx[j].closeloop_power;
            L1.arfcn[i].tx[j].BW               = (uint8)req->arfcn[i].tx[j].BW;
            L1.arfcn[i].tx[j].MCS              = (uint8)req->arfcn[i].tx[j].MCS;
            L1.arfcn[i].tx[j].reserved         = (uint8)req->arfcn[i].tx[j].reserved;
        }

        if (total_tx_frames > req->arfcn[i].frame_num)
        {
            LogFmtStrA(SPLOGLV_ERROR, "Arfcn[%2d] = %d, Invalid total tx frame count, %d <= %d", i, req->arfcn[i].arfcn, total_tx_frames, req->arfcn[i].frame_num);
            return SP_E_PHONE_INVALID_PARAMETER;
        }
    }

    DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE); 

    return SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, NULL, 0, NULL, m_dwTimeOut);
}

SPRESULT CCaliCmd::lteNST_GetBLER(PC_LTE_NST_SEBLER_T* BLER)
{
    CheckValidPointer(BLER);

    LogFmtStrA(SPLOGLV_INFO, "LTENST BLER:");

    L1_LTE_EMPTY_REQ_T L1;
    ZeroMemory((void* )&L1, sizeof(L1));
    L1.SignalCode   = Convert16(TOOL_MPH_LTE_NST_GET_BLER);
    L1.SignalSize   = Convert16((uint16)sizeof(L1));

    DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE); 

    CRecvPkgsList recvList;
    recvList.AddCondition(hd);
    recvList.AddCondition(hd);
    SPRESULT res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), recvList, m_dwTimeOut); 
    if (SP_OK != res)
    {
        return res;
    }

    for (int nPktIdx = 0; nPktIdx < recvList.GetPkgsCount(); nPktIdx++)
    {
        uint32 recvLen = 0;
        if (!UnpackPRT(recvList.GetPackage(nPktIdx), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
        {
            assert(0);
            continue;
        }

        if (recvLen >= sizeof(L1_LTE_NST_GET_BLER_RLT_T))
        {
            L1_LTE_NST_GET_BLER_RLT_T* pRLT = (L1_LTE_NST_GET_BLER_RLT_T* )m_diagBuff;
            uint16 uType = Convert16(pRLT->SignalCode);
            if (TOOL_MPH_LTE_NST_GET_BLER != uType)
            {
                LogFmtStrA(SPLOGLV_ERROR, "Invalid response command ID: 0x%X <> 0x%X", uType, TOOL_MPH_LTE_NST_GET_BLER);
                return SP_E_PHONE_INVALID_DATA;       
            }

            BLER->status = Convert16(pRLT->status);

            LogFmtStrA(SPLOGLV_INFO, "BLER status = %d!", BLER->status);


            BLER->arfcn_num = Convert16(pRLT->arfcn_num);
            if (BLER->arfcn_num > MAX_PC_LTE_NST_ARFCN_NUM)
            {
                LogFmtStrA(SPLOGLV_INFO, "Invalid BLER Arfcn count = %d!", BLER->arfcn_num);
                return SP_E_PHONE_INVALID_DATA;
            }

            for (uint16 nArfcnIdx = 0; nArfcnIdx <BLER->arfcn_num; nArfcnIdx++)
            {
                BLER->BLER10[nArfcnIdx] = Convert16(pRLT->BLER10[nArfcnIdx]);
                LogFmtStrA(SPLOGLV_INFO, "BLER Arfcn[%2d] = %d", nArfcnIdx+1, BLER->BLER10[nArfcnIdx]);
            }

            return SP_OK;
        }
    }

    LogRawStrA(SPLOGLV_ERROR, "Invalid response received!");
    return SP_E_PHONE_INVALID_DATA;
}

SPRESULT CCaliCmd::lteNST_GetRSSI(PC_LTE_NST_RSSI_T* RSSI)
{
    CheckValidPointer(RSSI);

    LogRawStrA(SPLOGLV_INFO, "LTE NST RSSI:");

    L1_LTE_EMPTY_REQ_T L1;
    ZeroMemory((void* )&L1, sizeof(L1));
    L1.SignalCode      = Convert16(TOOL_MPH_LTE_NST_GET_RSSI);
    L1.SignalSize      = Convert16((uint16)sizeof(L1));

    DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE); 

    CRecvPkgsList recvList;
    recvList.AddCondition(hd);
    recvList.AddCondition(hd);
    SPRESULT res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), recvList, m_dwTimeOut); 
    if (SP_OK != res)
    {
        return res;
    }

    for (int nPktIdx = 0; nPktIdx < recvList.GetPkgsCount(); nPktIdx++)
    {
        uint32 recvLen = 0;
        if (!UnpackPRT(recvList.GetPackage(nPktIdx), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
        {
            assert(0);
            continue;
        }

        if (recvLen >= sizeof(L1_LTE_NST_GET_RSSI_RLT_T))
        {
            L1_LTE_NST_GET_RSSI_RLT_T* pRLT = (L1_LTE_NST_GET_RSSI_RLT_T* )m_diagBuff;
            uint16 uType = Convert16(pRLT->SignalCode);
            if (TOOL_MPH_LTE_NST_GET_RSSI != uType)
            {
                LogFmtStrA(SPLOGLV_ERROR, "Invalid response command ID: 0x%X <> 0x%X", uType, TOOL_MPH_LTE_NST_GET_RSSI);
                return SP_E_PHONE_INVALID_DATA;       
            }

            RSSI->status = Convert16(pRLT->status);

            LogFmtStrA(SPLOGLV_INFO, "RSSI status = %d!", RSSI->status);

            RSSI->arfcn_num = Convert16(pRLT->arfcn_num);
            if (RSSI->arfcn_num > MAX_PC_LTE_NST_ARFCN_NUM)
            {
                LogFmtStrA(SPLOGLV_INFO, "Invalid RSSI Arfcn count = %d!", RSSI->arfcn_num);
                return SP_E_PHONE_INVALID_DATA;
            }

            for (uint16 nArfcnIdx = 0; nArfcnIdx < RSSI->arfcn_num; nArfcnIdx++)
            {
                RSSI->RSSI[nArfcnIdx] = Convert16(pRLT->RSSI[nArfcnIdx]);
                LogFmtStrA(SPLOGLV_INFO, "RSSI Arfcn[%2d] = %d", nArfcnIdx+1, RSSI->RSSI[nArfcnIdx]);
            }

            return SP_OK;
        }
    }

    LogRawStrA(SPLOGLV_ERROR, "Invalid response received!");
    return SP_E_PHONE_INVALID_DATA;
}

SPRESULT CCaliCmd::LTEContinousTxRx(PC_LTE_CALI_LTE_TRX_ALWAYS_ON * pParam)
{
    LogFmtStrA(SPLOGLV_INFO, "[LTEContinousTxRx]: Arfcn channel = %d  Ant = %d", pParam->Arfcn ,pParam->ant);
    int i = 0;

    L1_LTE_TRX_Always_ON req;
    ZeroMemory((void *)&req,   sizeof(req));
    req.SignalCode  = Convert16((uint16)TOOL_MPH_LTE_TRX);
    req.SignalSize  = Convert16((uint16)sizeof(req));

    req.Arfcn					= Convert16((uint16)pParam->Arfcn);
    req.BandIndicator			= pParam->BandIndicator;
    req.cc						= pParam->cc;
    req.ant						= pParam->ant;
    req.Mode					= pParam->Mode;
    req.Bw						= pParam->Bw;
    req.Tx_Config.Apt			= pParam->Tx_Config.Apt;
    req.Tx_Config.PaMode		= pParam->Tx_Config.PaMode;
    req.Tx_Config.RbNumber		= pParam->Tx_Config.RbNumber;
    req.Tx_Config.RbPosition	= pParam->Tx_Config.RbPosition;
    req.Tx_Config.MODULATIONS	= pParam->Tx_Config.MODULATIONS;
    req.Tx_Config.TxWord		= Convert16(pParam->Tx_Config.TxWord);
    for (i = 0; i < L1_LTE_SUB_FRAME_NUM; i++)
    {
        req.Tx_Config.subframe[i]  = pParam->Tx_Config.subframe[i];
    }

    req.Rx_Config.AGC_index = Convert16(pParam->Rx_Config.AGC_index);

    for (i = 0; i < L1_LTE_SUB_FRAME_NUM; i++)
    {
        req.Rx_Config.subframe[i]  = pParam->Rx_Config.subframe[i];
    }    

    DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE); 
    CRecvPkgsList recvList;
    recvList.AddCondition(hd);
    //recvList.AddCondition(hd);

    return SendAndRecv(hd, (const void* )&req, sizeof(req), recvList, m_dwTimeOut); 
}

SPRESULT CCaliCmd::LteSPIData(PC_LTE_SPI* pParam)
{
    LogFmtStrA(SPLOGLV_INFO, "[LteSPIData]:");

    L1_LTE_SPI req;    
    ZeroMemory((void *)&req,     sizeof(req));
    req.SignalCode			= Convert16((uint16)TOOL_MPH_LTE_READ_SPI);
    req.SignalSize			= Convert16((uint16)sizeof(req));
    // 
    req.Subframe			= Convert16((uint16)pParam->Subframe);
    req.mode				= Convert16((uint16)pParam->mode);
    req.SPI_read.SPI_ADDR	= Convert16((uint16)pParam->address);
    req.SPI_read.read_num	= Convert16(pParam->DateLength);	
    req.SPI_write.SPI_ADDR  = Convert16((uint16)pParam->address);
    req.SPI_write.SPI_DATA  = Convert16((uint16)pParam->data[0]);

    DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE);

    CRecvPkgsList recvList;
    recvList.AddCondition(hd);
    recvList.AddCondition(hd);
    SPRESULT res = SendAndRecv(hd, (const void* )&req, sizeof(req), recvList, m_dwTimeOut); 
    if (SP_OK != res)
    {
        return res;
    }

    if(SP_OK == res && 2 == req.mode)  // there is no response when write data , according to Le.yue 
    {
        return SP_OK;
    }

    //1st packet
    for (int i = 0; i < recvList.GetPkgsCount(); i++)
    {
        uint32 recvLen = 0;
        if (!UnpackPRT(recvList.GetPackage(i), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
        {
            assert(0);
            continue;
        }
        if (recvLen >= sizeof(L1_LTE_SPI_read_resp)-2)  //sizeof(L1_LTE_SPI))
        {
            L1_LTE_SPI_read_resp *pCnf = (L1_LTE_SPI_read_resp *)m_diagBuff;
            uint16 uType = Convert16((uint16)pCnf->SignalCode);
            if (TOOL_MPH_LTE_READ_SPI != uType)
            {
                LogFmtStrA(SPLOGLV_ERROR, "[LteSPIData read] Invalid response command id 0x%X!", uType);
                return SP_E_PHONE_INVALID_DATA;       
            }

            pParam->status = Convert16(pCnf->status);
            LogFmtStrA(SPLOGLV_INFO, "[LteSPIData] status = %d.", pCnf->status);

            memcpy(pParam->data, pCnf->SPI_DATA, sizeof(uint16)*1024); 

            return SP_OK;
        }
    }

    return SP_OK;
}

SPRESULT CCaliCmd::LteUlAfc(PC_LTE_CALI_UL_AFC_T * pParam)
{
    LogFmtStrA(SPLOGLV_INFO, "[LteUlAfc]: Arfcn channel = %d  Ant = %d", pParam->nChannel ,pParam->nMainOrDiv);

    L1_LTE_CALI_UL_AFC_T req;
    ZeroMemory((void *)&req,   sizeof(req));
    req.SignalCode  = Convert16((uint16)TOOL_MPH_LTE_UL_AFC);
    req.SignalSize  = Convert16((uint16)sizeof(req));

    req.nChannel    =  Convert32(pParam->nChannel); 
    req.eBW         =  Convert32(pParam->eBW); 
    req.nMainOrDiv  =  Convert16(pParam->nMainOrDiv); 
    req.nFactorStep = Convert16(pParam->nFactorStep);
    req.nAfcVal     = Convert16(pParam->nAfcVal);    
    req.nPaMode     = Convert16(pParam->nPaMode);  
    req.nApt_value  = Convert16(pParam->nApt_value); 
    req.nCdac       = Convert16(pParam->nCdac);    
    req.Modulation  = Convert16(pParam->Modulation);
    req.RbPos       = Convert16(pParam->RbPos);   
    req.RbNum       = Convert16(pParam->RbNum);
    req.nTxNum      = Convert16(pParam->nTxNum);
   
    for(int i = 0; i < 4; i++)
    {
        req.reserved[i]      = Convert32(pParam->reserved[i]);
    }

    DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE); 
    CRecvPkgsList recvList;
    recvList.AddCondition(hd);
    //recvList.AddCondition(hd);

    return SendAndRecv(hd, (const void* )&req, sizeof(req), recvList, m_dwTimeOut); 
}

SPRESULT CCaliCmd::LTEContinousTxRx_V2(PC_LTE_CALI_LTE_TRX_ALWAYS_ON * pParam)
{
    LogFmtStrA(SPLOGLV_INFO, "[LTEContinousTxRx_V2]: Arfcn channel = %d  Ant = %d", pParam->Arfcn ,pParam->ant);
    int i = 0;

    L1_LTE_TRX_Always_ON_V2 req;
    ZeroMemory((void *)&req,   sizeof(req));
    req.SignalCode  = Convert16((uint16)TOOL_MPH_LTE_TRX_V2);
    req.SignalSize  = Convert16((uint16)sizeof(req));

    req.Arfcn					= Convert32(pParam->Arfcn);
    req.BandIndicator			= pParam->BandIndicator;
    req.cc						= pParam->cc;
    req.ant						= pParam->ant;
    req.Mode					= pParam->Mode;
    req.Bw						= pParam->Bw;
    req.Tx_Config.Apt			= pParam->Tx_Config.Apt;
    req.Tx_Config.PaMode		= pParam->Tx_Config.PaMode;
    req.Tx_Config.RbNumber		= pParam->Tx_Config.RbNumber;
    req.Tx_Config.RbPosition	= pParam->Tx_Config.RbPosition;
    req.Tx_Config.MODULATIONS	= pParam->Tx_Config.MODULATIONS;
    req.Tx_Config.TxWord		= Convert16(pParam->Tx_Config.TxWord);
    for (i = 0; i < L1_LTE_SUB_FRAME_NUM; i++)
    {
        req.Tx_Config.subframe[i]  = pParam->Tx_Config.subframe[i];
    }

    req.Rx_Config.AGC_index = Convert16(pParam->Rx_Config.AGC_index);

    for (i = 0; i < L1_LTE_SUB_FRAME_NUM; i++)
    {
        req.Rx_Config.subframe[i]  = pParam->Rx_Config.subframe[i];
    }    

    DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE); 
    CRecvPkgsList recvList;
    recvList.AddCondition(hd);
    //recvList.AddCondition(hd);

    return SendAndRecv(hd, (const void* )&req, sizeof(req), recvList, m_dwTimeOut); 
}

SPRESULT CCaliCmd::LTEContinousTxRx_GetRSSI(PC_LTE_TRX_RSSI_T *lpRSSI)
{
    LogFmtStrA(SPLOGLV_INFO, "Lte Continous TRX get RSSI:");

    L1_LTE_EMPTY_REQ_T L1;
    ZeroMemory((void* )&L1, sizeof(L1));
    L1.SignalCode      = Convert16(TOOL_MPH_LTE_TRX_GET_RSSI);
    L1.SignalSize      = Convert16((uint16)sizeof(L1));

    DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE); 

    CRecvPkgsList recvList;
    recvList.AddCondition(hd);
    recvList.AddCondition(hd);
    SPRESULT res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), recvList, m_dwTimeOut); 
    if (SP_OK != res)
    {
        return res;
    }

    for (int nPktIdx = 0; nPktIdx < recvList.GetPkgsCount(); nPktIdx++)
    {
        uint32 recvLen = 0;
        if (!UnpackPRT(recvList.GetPackage(nPktIdx), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
        {
            assert(0);
            continue;
        }
        if (recvLen >= sizeof(L1_LTE_CONTINOUSTRX_GET_RSSI_CNF))
        {
            L1_LTE_CONTINOUSTRX_GET_RSSI_CNF *pCnf = (L1_LTE_CONTINOUSTRX_GET_RSSI_CNF *)m_diagBuff;
            uint16 uType = Convert16((uint16)pCnf->SignalCode);
            if (TOOL_MPH_LTE_TRX_GET_RSSI != uType)
            {
                LogFmtStrA(SPLOGLV_ERROR, "[LTE CONTINOUS TRX RSSI] Invalid response command id 0x%X!", uType);
                return SP_E_PHONE_INVALID_DATA;       
            }

            lpRSSI->status = Convert32(pCnf->status);
            LogFmtStrA(SPLOGLV_INFO, "[LTE CONTINOUS TRX RSSI] status = %d.", lpRSSI->status);
            for (int nAntIdx=0; nAntIdx < 2; nAntIdx++)
            {
                lpRSSI->RSSI[nAntIdx] = Convert32((uint32)pCnf->RSSI[nAntIdx]);
                LogFmtStrA(SPLOGLV_ERROR, "[LTE CONTINOUS TRX RSSI] Arfcn[%2d] = %d", nAntIdx, lpRSSI->RSSI[nAntIdx]);
            }

            return SP_OK;
        }
    }

    LogRawStrA(SPLOGLV_ERROR, "Invalid response received!");
    return SP_E_PHONE_INVALID_DATA;
}

SPRESULT CCaliCmd::lteAfc_DL(PC_LTE_AFC_DL_T *pParam, PC_LTE_AFC_DL_RESULT_T* pResult)
{
    LogRawStrA(SPLOGLV_INFO, "Lte DL AFC ");

    L1_LTE_AFC_DL_REQ req;
    ZeroMemory((void *)&req, sizeof(req));
    req.SignalCode               = Convert16(TOOL_MPH_LTE_AFC_DL);
    req.SignalSize               = Convert16((uint16)sizeof(req));
    req.lteAfcDL.Afc0            = Convert16(pParam->Afc0);
    req.lteAfcDL.Afc1            = Convert16(pParam->Afc1);
    req.lteAfcDL.Afc2            = Convert16(pParam->Afc2);
    req.lteAfcDL.afcType         = Convert16(pParam->afcType);
    req.lteAfcDL.CadcMax         = Convert16(pParam->CadcMax);
    req.lteAfcDL.CdacMin         = Convert16(pParam->CdacMin);
    req.lteAfcDL.indicator       = Convert16(pParam->indicator);
    req.lteAfcDL.nArfcn          = Convert16(pParam->nArfcn);
    req.lteAfcDL.nOffset         = Convert16(pParam->nOffset);
    req.lteAfcDL.RxWord          = Convert16(pParam->RxWord);
    req.lteAfcDL.Reserved[0]     = Convert16(pParam->Reserved[0]);
    req.lteAfcDL.Reserved[1]     = Convert16(pParam->Reserved[1]);

    m_diagBuff[0] = 0;
    DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE); 

    CRecvPkgsList recvList;
    recvList.AddCondition(hd);
    recvList.AddCondition(hd);
    SPRESULT res = SendAndRecv(hd, (const void*)&req, sizeof(req), recvList, m_dwTimeOut); 
    if (SP_OK != res)
    {
        return res;
    }

    for (int i = 0; i < recvList.GetPkgsCount(); i++)
    {
        uint32 recvLen = 0;
        if (!UnpackPRT(recvList.GetPackage(i), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
        {
            assert(0);
            continue;
        }
        if (recvLen >= sizeof(L1_LTE_AFC_DL_CNF))
        {
            L1_LTE_AFC_DL_CNF *pCnf = (L1_LTE_AFC_DL_CNF *)m_diagBuff;
            uint16 uType = Convert16((uint16)pCnf->SignalCode);
            if (TOOL_MPH_LTE_AFC_DL != uType)
            {
                LogFmtStrA(SPLOGLV_ERROR, "[LTE DL AFC] Invalid response command id 0x%X!", uType);
                return SP_E_PHONE_INVALID_DATA;       
            }

            pResult->Rssi        =   Convert32(pCnf->Rssi);     
            pResult->Cdac        =   Convert16(pCnf->Cdac);     
            pResult->Cafc        =   Convert16(pCnf->Cafc);     
            pResult->FreqError   =   Convert32(pCnf->FreqError);
            pResult->Slope       =   Convert16(pCnf->Slope);    
            pResult->reserved    =   Convert16(pCnf->reserved); 

            return SP_OK;
        }
    }

    LogRawStrA(SPLOGLV_ERROR, "Invalid response received!");
    return SP_E_PHONE_INVALID_DATA;
}

SPRESULT CCaliCmd::lteAfc_DL_V2(PC_LTE_AFC_DL_V2_T *pParam, PC_LTE_AFC_DL_RESULT_T* pResult)
{
	LogRawStrA(SPLOGLV_INFO, "Lte DL AFC v2 ");
	L1_LTE_AFC_DL_V2_REQ req;
	ZeroMemory((void *)&req, sizeof(req));
	req.SignalCode               = Convert16(TOOL_MPH_LTE_AFC_DL);
	req.SignalSize               = Convert16((uint16)sizeof(req));
	req.lteAfcDL.nArfcn          = Convert16(pParam->nArfcn);
	req.lteAfcDL.CAfc            = Convert16(pParam->CAfc);
	req.lteAfcDL.Cdac            = Convert16(pParam->Cdac);
	req.lteAfcDL.indicator       = Convert16(pParam->indicator);
	req.lteAfcDL.nOffset         = Convert16(pParam->nOffset);
	req.lteAfcDL.RxWord          = Convert16(pParam->RxWord);
	req.lteAfcDL.Reserved1       = Convert16(pParam->Reserved1);
	req.lteAfcDL.Reserved2[0]    = Convert16(pParam->Reserved2);
	req.lteAfcDL.Reserved3[1]    = Convert16(pParam->Reserved3[1]);
	m_diagBuff[0] = 0;
	DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE); 
	CRecvPkgsList recvList;
	recvList.AddCondition(hd);
	recvList.AddCondition(hd);
	SPRESULT res = SendAndRecv(hd, (const void*)&req, sizeof(req), recvList, m_dwTimeOut); 
	if (SP_OK != res)
	{
		return res;
	}
	for (int i = 0; i < recvList.GetPkgsCount(); i++)
	{
		uint32 recvLen = 0;
		if (!UnpackPRT(recvList.GetPackage(i), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
		{
			assert(0);
			continue;
		}
		if (recvLen >= sizeof(L1_LTE_AFC_DL_CNF))
		{
			L1_LTE_AFC_DL_CNF *pCnf = (L1_LTE_AFC_DL_CNF *)m_diagBuff;
			uint16 uType = Convert16((uint16)pCnf->SignalCode);
			if (TOOL_MPH_LTE_AFC_DL != uType)
			{
				LogFmtStrA(SPLOGLV_ERROR, "[LTE DL AFC] Invalid response command id 0x%X!", uType);
				return SP_E_PHONE_INVALID_DATA;       
			}
			pResult->Rssi        =   Convert32(pCnf->Rssi);     
			pResult->Cdac        =   Convert16(pCnf->Cdac);     
			pResult->Cafc        =   Convert16(pCnf->Cafc);     
			pResult->FreqError   =   Convert32(pCnf->FreqError);
			pResult->Slope       =   Convert16(pCnf->Slope);    
			pResult->reserved    =   Convert16(pCnf->reserved); 
			return SP_OK;
		}
	}
	LogRawStrA(SPLOGLV_ERROR, "Invalid response received!");
	return SP_E_PHONE_INVALID_DATA;
}
SPRESULT CCaliCmd::lte_GetTransceiverTemp( TRANSCEIVER_TEMP_T *pTemp )
{
    LogFmtStrA(SPLOGLV_INFO, "LTE GetTransceiver Temp:");

    L1_LTE_EMPTY_REQ_T L1;
    ZeroMemory((void *)&L1,     sizeof(L1));
    L1.SignalCode      = Convert16((uint16)TOOL_MPH_LTE_GET_TRANSCEIVERTEMP);
    L1.SignalSize      = Convert16((uint16)sizeof(L1));

    m_diagBuff[0]   = 0;
    DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE); 

    CRecvPkgsList recvList;
    recvList.AddCondition(hd);
    recvList.AddCondition(hd);
    SPRESULT res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), recvList, m_dwTimeOut); 
    if (SP_OK != res)
    {
        return res;
    }

    for (int i = 0; i < recvList.GetPkgsCount(); i++)
    {
        uint32 recvLen = 0;
        if (!UnpackPRT(recvList.GetPackage(i), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
        {
            assert(0);
            continue;
        }
        if (recvLen >= sizeof(L1_LTE_TRANSCEIVER_TEMP_T))
        {
            L1_LTE_TRANSCEIVER_TEMP_T *pCnf = (L1_LTE_TRANSCEIVER_TEMP_T *)m_diagBuff;
            uint16 uType = Convert16((uint16)pCnf->SignalCode);
            if (TOOL_MPH_LTE_GET_TRANSCEIVERTEMP != uType)
            {
                LogFmtStrA(SPLOGLV_ERROR, "[GetTransceiver Temp] Invalid response command id 0x%X!", uType);
                return SP_E_PHONE_INVALID_DATA;       
            }

            pTemp->adc1 = Convert16(pCnf->adc1);
            pTemp->adc0 = Convert16(pCnf->adc0);
            pTemp->k    = Convert16(pCnf->k);
            pTemp->c0   = Convert16(pCnf->c0);
            pTemp->c1   = Convert16(pCnf->c1);

            return SP_OK;
        }
    }

    LogRawStrA(SPLOGLV_ERROR, "Invalid response received!");
    return SP_E_PHONE_INVALID_DATA;
}

SPRESULT CCaliCmd::mgbLTEEGSet( MGB_LTE_EG_SET_T *pParam )
{
	LogRawStrA(SPLOGLV_INFO, "mgbLTEEGSet :");

	L1_MGB_LTE_EG_SET_T req;
	ZeroMemory((void *)&req, sizeof(req));
	req.SignalCode               = Convert16(TOOL_MPH_MGB_EG_SET);
	req.SignalSize               = Convert16((uint16)sizeof(req));
	req.type					 = Convert16(pParam->type);
	req.nswitch					 = Convert16(pParam->nswitch);
	req.indoor_freq				 = Convert32(pParam->indoor_freq);
	req.outdoor_freq			 = Convert32(pParam->outdoor_freq);
	req.bandwidth				 = Convert32(pParam->bandwidth);
	req.gain					 = Convert32(pParam->gain);
	req.power					 = Convert32(pParam->power);

	m_diagBuff[0] = 0;
	DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE); 

	CRecvPkgsList recvList;
	recvList.AddCondition(hd);
	recvList.AddCondition(hd);
	SPRESULT res = SendAndRecv(hd, (const void*)&req, sizeof(req), recvList, m_dwTimeOut); 
	if (SP_OK != res)
	{
		return res;
	}

	for (int i = 0; i < recvList.GetPkgsCount(); i++)
	{
		uint32 recvLen = 0;
		if (!UnpackPRT(recvList.GetPackage(i), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
		{
			assert(0);
			continue;
		}
		if (recvLen >= sizeof(L1_LTE_EMPTY_RLT_T))
		{
			return SP_OK;
		}
	}

	LogRawStrA(SPLOGLV_ERROR, "Invalid response received!");
	return SP_E_PHONE_INVALID_DATA;
}

SPRESULT CCaliCmd::mgbLTELoCalInit( )
{
	LogRawStrA(SPLOGLV_INFO, "LTE Lo Init:");

	L1_LTE_EMPTY_REQ_T req;
	ZeroMemory((void *)&req, sizeof(req));
	req.SignalCode      = Convert16((uint16)TOOL_MPH_LTE_MGB_LO_INIT);
	req.SignalSize      = Convert16((uint16)sizeof(req));

	DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE); 

	CRecvPkgsList recvList;
	recvList.AddCondition(hd);
	recvList.AddCondition(hd);
	SPRESULT res = SendAndRecv(hd, (const void* )&req, sizeof(req), recvList, m_dwTimeOut); 
	if (SP_OK != res)
	{
		return res;
	}

	for (int i = 0; i < recvList.GetPkgsCount(); i++)
	{
		uint32 recvLen = 0;
		if (!UnpackPRT(recvList.GetPackage(i), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
		{
			assert(0);
			continue;
		}

		if (recvLen >= sizeof(L1_LTE_NST_SYNC_RLT_T))
		{
			L1_MGB_LTE_LO_INIT_RLT_T* pRLT = (L1_MGB_LTE_LO_INIT_RLT_T* )m_diagBuff;
			int nStatus = Convert32(pRLT->status);
			if (nStatus != 1)
			{
				return SP_E_PHONE_INVALID_STATE;
			}
			
			LogFmtStrA(SPLOGLV_INFO, "status = %d", nStatus);

			return SP_OK;
		}
	}

	LogRawStrA(SPLOGLV_ERROR, "Invalid response received!");
	return SP_E_PHONE_INVALID_DATA;
}

SPRESULT CCaliCmd::mgbLTELoCalEnd( )
{
	LogRawStrA(SPLOGLV_INFO, "LTE Lo End:");


	L1_LTE_EMPTY_REQ_T req;
	ZeroMemory((void *)&req, sizeof(req));
	req.SignalCode      = Convert16((uint16)TOOL_MPH_LTE_MGB_LO_END);
	req.SignalSize      = Convert16((uint16)sizeof(req));

	m_diagBuff[0] = 0;
	DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE); 

	CRecvPkgsList recvList;
	recvList.AddCondition(hd);
	recvList.AddCondition(hd);
	SPRESULT res = SendAndRecv(hd, (const void*)&req, sizeof(req), recvList, m_dwTimeOut); 
	if (SP_OK != res)
	{
		return res;
	}

	for (int i = 0; i < recvList.GetPkgsCount(); i++)
	{
		uint32 recvLen = 0;
		if (!UnpackPRT(recvList.GetPackage(i), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
		{
			assert(0);
			continue;
		}
		if (recvLen >= sizeof(L1_LTE_EMPTY_RLT_T))
		{
			return SP_OK;
		}
	}

	LogRawStrA(SPLOGLV_ERROR, "Invalid response received!");
	return SP_E_PHONE_INVALID_DATA;
}

SPRESULT CCaliCmd::mgbLTELoCal( const CALI_LO_PARAM_REQ_T *pLoCalparam )
{
	LogRawStrA(SPLOGLV_INFO, "LTE Lo Cal:");

	L1_MGB_LTE_LO_REQ_T		req;
	ZeroMemory((void *)&req, sizeof(req));
	req.SignalCode      = Convert16((uint16)TOOL_MPH_LTE_MGB_LO);
	req.SignalSize      = Convert16((uint16)sizeof(req));
	req.flag			= Convert16(pLoCalparam->flag);
	req.link_path		= Convert16(pLoCalparam->link_path);
	req.gainIndex_TX	= Convert16(pLoCalparam->gainIndex_TX);
	req.gainIndex_RX	= Convert16(pLoCalparam->gainIndex_RX);
	req.reg_273			= Convert16(pLoCalparam->reg_273);
	req.reg_274			= Convert16(pLoCalparam->reg_274);
	req.LNA				= Convert16(pLoCalparam->LNA);
	req.reserved		= Convert16(pLoCalparam->reserved);


	m_diagBuff[0] = 0;
	DeclareDiagHeader(hd, DIAG_LTE_RF, ZERO_SUBTYPE); 

	CRecvPkgsList recvList;
	recvList.AddCondition(hd);
	recvList.AddCondition(hd);
	SPRESULT res = SendAndRecv(hd, (const void*)&req, sizeof(req), recvList, m_dwTimeOut); 
	if (SP_OK != res)
	{
		return res;
	}

	for (int i = 0; i < recvList.GetPkgsCount(); i++)
	{
		uint32 recvLen = 0;
		if (!UnpackPRT(recvList.GetPackage(i), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
		{
			assert(0);
			continue;
		}
		if (recvLen >= sizeof(L1_LTE_EMPTY_RLT_T))
		{
			return SP_OK;
		}
	}

	LogRawStrA(SPLOGLV_ERROR, "Invalid response received!");
	return SP_E_PHONE_INVALID_DATA;
}

SPRESULT CCaliCmd::ModemV3_LTE_Active(BOOL bActive)
{
	LogFmtStrA(SPLOGLV_INFO, "%s", "ModemV3_lteActive");
    SPRESULT res = SP_OK;

	L1_SUBCMD_HEAD_T L1;
	ZeroMemory((void* )&L1, sizeof(L1));
	if (bActive)
	{
		L1.SubCmdCode  = Convert16(CAL_LTE_ACTIVE_REQ);
	}
	else
	{
		L1.SubCmdCode  = Convert16(CAL_LTE_DEACTIVE_REQ);
	}
	
	L1.SubCmdSize  = Convert16((uint16)sizeof(L1));

	DeclareDiagHeader(hd, DIAG_MODEM_RF_E, ZERO_SUBTYPE); 

	CRecvPkgsList recvList;
	recvList.AddCondition(hd);

    res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), recvList, m_dwTimeOut);

    if (SP_OK != res)
    {
        LogFmtStrA(SPLOGLV_ERROR, "ModemV3_LTE_Active Fail!");
    }

    return res;
}

SPRESULT CCaliCmd::ModemV3_LTE_AfcCal(const PC_MODEM_RF_V3_LTE_AFC_REQ_CMD_T *pAfcReq, PC_MODEM_RF_V3_LTE_AFC_RSP_T* pAfcRlst)
{
	CheckValidPointer(pAfcReq);
	CheckValidPointer(pAfcRlst);

	LogFmtStrA(SPLOGLV_INFO, "[ModemV3_lteAfc]: Afc tuning!");

	L1_MODEM_RF_V3_LTE_AFC_REQ_T req;
	ZeroMemory((void *)&req,   sizeof(req));

	req.CmdHeader.SubCmdCode  = Convert16((uint16)CAL_LTE_AFC_REQ);
	req.CmdHeader.SubCmdSize  = Convert16((uint16)sizeof(req));
	req.CADC = Convert32(pAfcReq->CDAC);
	req.CAFC = Convert32(pAfcReq->CAFC);
    req.AfcFlag = Convert32(pAfcReq->AfcFlag);

	DeclareDiagHeader(hd, DIAG_MODEM_RF_E, ZERO_SUBTYPE); 
	CRecvPkgsList recvList;
	recvList.AddCondition(hd);
	SPRESULT res = SendAndRecv(hd, (const void*)&req, sizeof(req), recvList, m_dwTimeOut); 
	if (SP_OK != res)
	{
		return res;
	}
	uint32 recvLen = 0;
	if (!UnpackPRT(recvList.GetPackage(0), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
	{
		LogFmtStrA(SPLOGLV_ERROR, "[LTE Modem V3 AFC] Unpacket data failed!");
		return SP_E_PHONE_INVALID_DATA;  
	}
	if (recvLen >= sizeof(L1_MODEM_RF_V3_LTE_AFC_RSP_T))
	{
		L1_MODEM_RF_V3_LTE_AFC_RSP_T *pCnf = (L1_MODEM_RF_V3_LTE_AFC_RSP_T *)m_diagBuff;
		uint16 uType = Convert16((uint16)pCnf->CmdHeader.SubCmdCode);
		if (req.CmdHeader.SubCmdCode != uType)
		{
			LogFmtStrA(SPLOGLV_ERROR, "[LTE Modem V3 AFC] Invalid response command id 0x%X!", uType);
			return SP_E_PHONE_INVALID_DATA;       
		}

		pAfcRlst->status = Convert32(pCnf->status);  

		return SP_OK;
	}

	LogRawStrA(SPLOGLV_ERROR, "Invalid response received!");
	return SP_E_PHONE_INVALID_DATA;
}

SPRESULT CCaliCmd::ModemV3_LTE_ApcCal( const PC_MODEM_RF_V3_LTE_APC_REQ_CMD_T *pApcReq, BOOL bPdetEn, PC_MODEM_RF_V3_LTE_APC_RSP_T* pApcRlst, uint32 RspSize )
{
	CheckValidPointer(pApcReq);
	if(bPdetEn)
	{
		CheckValidPointer(pApcRlst);
	}


	LogRawStrA(SPLOGLV_INFO, "ModemV3_lteApc");  

	L1_COMMON_REQ_T    L1;    
	ZeroMemory((void* )&L1, sizeof(L1));  
	uint16 RfChain0ArfcnLen = Convert16(pApcReq->stReqHeader.ApcRfChainCtrl[LTE_RF_CHAIN_CC0].ArfcnNum * pApcReq->stReqHeader.ArfcnSize);
	uint16 RfChain0AptLen = Convert16((uint16)(pApcReq->nAptNum[LTE_RF_CHAIN_CC0] * pApcReq->stReqHeader.AptSize));

	uint16 RfChain1ArfcnLen = 0;
	uint16 RfChain1AptLen = 0;

	if (LTE_NON_CA != pApcReq->stReqHeader.CaFlag)
	{
		RfChain1ArfcnLen = pApcReq->stReqHeader.ApcRfChainCtrl[LTE_RF_CHAIN_CC1].ArfcnNum * pApcReq->stReqHeader.ArfcnSize;
		RfChain1AptLen = Convert16((uint16)(pApcReq->nAptNum[LTE_RF_CHAIN_CC1] * pApcReq->stReqHeader.AptSize));
	}

	uint16 usLength =  (uint16)(sizeof(L1) + sizeof(CALI_APC_CTRL_HEADER_T)) + RfChain0ArfcnLen + RfChain0AptLen + RfChain1ArfcnLen + RfChain1AptLen;

	L1.head.SubCmdCode = Convert16(CAL_LTE_APC_REQ);
	L1.head.SubCmdSize = Convert16(usLength);

	uint8* pBuff = NULL;
	try
	{
		pBuff = new uint8[usLength];
	}
	catch (const std::bad_alloc& /*e*/)
	{
		pBuff = NULL;
	}
	if (NULL == pBuff)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Memory allocation failed.", __FUNCTION__);
		return SP_E_PHONE_ALLOC_MEMORY;
	}

	uint8* pPos = pBuff;
	uint16 usLen = sizeof(L1);
	memcpy(pPos, &L1, usLen);
	//header
	pPos += usLen;
	usLen = sizeof(CALI_APC_CTRL_HEADER_T);
	memcpy(pPos, &pApcReq->stReqHeader, usLen);
	//channel rf chian0
	pPos += usLen;
	usLen = RfChain0ArfcnLen;
	memcpy(pPos, pApcReq->pArfcnConfChian0, usLen);
	//apt rf chain0
	pPos += usLen;
	usLen = RfChain0AptLen;
	memcpy(pPos, pApcReq->pAptConfChain0, usLen);

	//channel rf chian1
	pPos += usLen;
	usLen = RfChain1ArfcnLen;
	memcpy(pPos, pApcReq->pArfcnConfChian1, usLen);
	//apt rf chain1
	pPos += usLen;
	usLen = RfChain1AptLen;
	memcpy(pPos, pApcReq->pAptConfChain1, usLen);
	DeclareDiagHeader(hd, DIAG_MODEM_RF_E, ZERO_SUBTYPE); 

	CRecvPkgsList recvList;
	recvList.AddCondition(hd);
	SPRESULT res = SendAndRecv(hd, (const void* )pBuff, usLength, recvList, m_dwTimeOut); 
	delete[] pBuff;
	if (SP_OK != res)
	{
		return res;
	}

	uint32 recvLen = 0;
	//2nd packet
	if (!UnpackPRT(recvList.GetPackage(0), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: 2nd packet UnpackPRT return False", __FUNCTION__);
		return SP_E_PHONE_INVALID_DATA;
	}

	if (recvLen < sizeof(L1_COMMON_REQ_T)+ sizeof(uint32)*2)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Response packet length %d is less than expected %d", __FUNCTION__, recvLen, sizeof(L1_COMMON_REQ_T)+ sizeof(uint32)*2);
		return SP_E_PHONE_INVALID_DATA;
	}
	//fetch result
	L1_MODEM_RF_V3_LTE_APC_RSP_T* pRLT = (L1_MODEM_RF_V3_LTE_APC_RSP_T* )m_diagBuff;
	uint16 uType = Convert16(pRLT->CmdHeader.SubCmdCode);
	if (CAL_LTE_APC_REQ != uType)
	{
		LogFmtStrA(SPLOGLV_ERROR, "Invalid response command ID: 0x%X != 0x%X", uType, CAL_LTE_APC_REQ);
		return SP_E_PHONE_INVALID_DATA;       
	}

	if (SP_OK != pRLT->status)
	{
		LogFmtStrA(SPLOGLV_ERROR, "The returned opCode is %d", pRLT->status);
		return SP_E_PHONE_INVALID_STATE; 
	}

	if(bPdetEn)
	{
		if (RspSize < (sizeof(uint16)*pRLT->PdtRlstCnt))
		{
			LogFmtStrA(SPLOGLV_ERROR, "Invalid Pdt result count(in bytes), Expected(Input Size) count %d, response count %d", RspSize, pRLT->PdtRlstCnt*2);
			return SP_E_PHONE_INVALID_DATA;  
		}
		pApcRlst->PdtRlstCnt = pRLT->PdtRlstCnt;

		memcpy(pApcRlst->pPdetRlst, ((char*)m_diagBuff) + sizeof(L1_COMMON_REQ_T) + sizeof(uint32) + sizeof(uint16), pRLT->PdtRlstCnt * sizeof(unsigned short));
	}
	
	return res;
}

SPRESULT CCaliCmd::ModemV4_LTE_ApcCal(const PC_MODEM_RF_V4_LTE_APC_REQ_CMD_T* pApcReq, BOOL bPdetEn, PC_MODEM_RF_V3_LTE_APC_RSP_T* pApcRlst, uint32 RspSize)
{
	CheckValidPointer(pApcReq);
	if (bPdetEn)
	{
		CheckValidPointer(pApcRlst);
	}

	LogRawStrA(SPLOGLV_INFO, "ModemV4_lteApc");

	L1_COMMON_REQ_T    L1;
	ZeroMemory((void*)&L1, sizeof(L1));
	uint16 RfChain0ArfcnLen = Convert16(pApcReq->stReqHeader.ApcRfChainCtrl[LTE_RF_CHAIN_CC0].ArfcnNum * pApcReq->stReqHeader.ArfcnSize);
	uint16 RfChain0AptLen = Convert16((uint16)(pApcReq->nAptNum[LTE_RF_CHAIN_CC0] * pApcReq->stReqHeader.AptSize));

	uint16 RfChain1ArfcnLen = 0;
	uint16 RfChain1AptLen = 0;

	if (LTE_NON_CA != pApcReq->stReqHeader.CaFlag)
	{
		RfChain1ArfcnLen = pApcReq->stReqHeader.ApcRfChainCtrl[LTE_RF_CHAIN_CC1].ArfcnNum * pApcReq->stReqHeader.ArfcnSize;
		RfChain1AptLen = Convert16((uint16)(pApcReq->nAptNum[LTE_RF_CHAIN_CC1] * pApcReq->stReqHeader.AptSize));
	}

	uint16 usLength = (uint16)(sizeof(L1) + sizeof(CALI_APC_CTRL_HEADER_T)) + RfChain0ArfcnLen + RfChain0AptLen + RfChain1ArfcnLen + RfChain1AptLen;

	L1.head.SubCmdCode = Convert16(CAL_LTE_APC_REQ_V2);
	L1.head.SubCmdSize = Convert16(usLength);

	uint8* pBuff = NULL;
	try
	{
		pBuff = new uint8[usLength];
	}
	catch (const std::bad_alloc& /*e*/)
	{
		pBuff = NULL;
	}
	if (NULL == pBuff)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Memory allocation failed.", __FUNCTION__);
		return SP_E_PHONE_ALLOC_MEMORY;
	}

	uint8* pPos = pBuff;
	uint16 usLen = sizeof(L1);
	memcpy(pPos, &L1, usLen);
	//header
	pPos += usLen;
	usLen = sizeof(CALI_APC_CTRL_HEADER_T);
	memcpy(pPos, &pApcReq->stReqHeader, usLen);
	//channel rf chian0
	pPos += usLen;
	usLen = RfChain0ArfcnLen;
	memcpy(pPos, pApcReq->pArfcnConfChian0, usLen);
	//apt rf chain0
	pPos += usLen;
	usLen = RfChain0AptLen;
	memcpy(pPos, pApcReq->pAptConfChain0, usLen);

	//channel rf chian1
	pPos += usLen;
	usLen = RfChain1ArfcnLen;
	memcpy(pPos, pApcReq->pArfcnConfChian1, usLen);
	//apt rf chain1
	pPos += usLen;
	usLen = RfChain1AptLen;
	memcpy(pPos, pApcReq->pAptConfChain1, usLen);
	DeclareDiagHeader(hd, DIAG_MODEM_RF_E, ZERO_SUBTYPE);

	CRecvPkgsList recvList;
	recvList.AddCondition(hd);
	SPRESULT res = SendAndRecv(hd, (const void*)pBuff, usLength, recvList, m_dwTimeOut);
	delete[] pBuff;
	if (SP_OK != res)
	{
		return res;
	}

	uint32 recvLen = 0;
	//2nd packet
	if (!UnpackPRT(recvList.GetPackage(0), NULL, (void*)&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: 2nd packet UnpackPRT return False", __FUNCTION__);
		return SP_E_PHONE_INVALID_DATA;
	}

	if (recvLen < sizeof(L1_COMMON_REQ_T) + sizeof(uint32) * 2)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Response packet length %d is less than expected %d", __FUNCTION__, recvLen, sizeof(L1_COMMON_REQ_T) + sizeof(uint32) * 2);
		return SP_E_PHONE_INVALID_DATA;
	}
	//fetch result
	L1_MODEM_RF_V3_LTE_APC_RSP_T* pRLT = (L1_MODEM_RF_V3_LTE_APC_RSP_T*)m_diagBuff;
	uint16 uType = Convert16(pRLT->CmdHeader.SubCmdCode);
	if (CAL_LTE_APC_REQ_V2 != uType)
	{
		LogFmtStrA(SPLOGLV_ERROR, "Invalid response command ID: 0x%X != 0x%X", uType, CAL_LTE_APC_REQ_V2);
		return SP_E_PHONE_INVALID_DATA;
	}

	if (SP_OK != pRLT->status)
	{
		LogFmtStrA(SPLOGLV_ERROR, "The returned opCode is %d", pRLT->status);
		return SP_E_PHONE_INVALID_STATE;
	}

	if (bPdetEn)
	{
		if (RspSize < (sizeof(uint16) * pRLT->PdtRlstCnt))
		{
			LogFmtStrA(SPLOGLV_ERROR, "Invalid Pdt result count(in bytes), Expected(Input Size) count %d, response count %d", RspSize, pRLT->PdtRlstCnt * 2);
			return SP_E_PHONE_INVALID_DATA;
		}
		pApcRlst->PdtRlstCnt = pRLT->PdtRlstCnt;

		memcpy(pApcRlst->pPdetRlst, ((char*)m_diagBuff) + sizeof(L1_COMMON_REQ_T) + sizeof(uint32) + sizeof(uint16), pRLT->PdtRlstCnt * sizeof(unsigned short));
	}

	return res;
}

SPRESULT CCaliCmd::ModemV3_LTE_AgcCal(const PC_MODEM_RF_V3_LTE_AGC_PARAM* pAgcReq, unsigned short* pAgcRlst)
{
	if (NULL == pAgcReq || NULL == pAgcRlst)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid parameters!", __FUNCTION__);
		return SP_E_PHONE_INVALID_PARAMETER;
	}

	LogRawStrA(SPLOGLV_INFO, "ModemV3_lteAgc");  

	L1_COMMON_REQ_T    L1;    
	ZeroMemory((void* )&L1, sizeof(L1));  

	//////////////////////////////////////////////////////////////////////////
	uint16 RfChain0ArfcnLen = pAgcReq->Header.AgcRfChain[LTE_RF_CHAIN_CC0].ArfcnNum * pAgcReq->Header.ArfcnSize;
	uint16 RfChain0AgcLen = pAgcReq->Header.AgcRfChain[LTE_RF_CHAIN_CC0].RxGainNum * pAgcReq->Header.RxGainSize;

	uint16 RfChain1ArfcnLen = 0;
	uint16 RfChain1AgcLen = 0;

	uint16 RfChain2ArfcnLen = 0;
	uint16 RfChain2AgcLen = 0;

	switch(pAgcReq->Header.CaFlag)
	{
	case LTE_NON_CA:
		break;
	case LTE_CA_NON_CONTINUOUS:
	case LTE_CA_CONTINUOUS:
		{
			RfChain1ArfcnLen = pAgcReq->Header.AgcRfChain[LTE_RF_CHAIN_CC1].ArfcnNum * pAgcReq->Header.ArfcnSize;
			RfChain1AgcLen = pAgcReq->Header.AgcRfChain[LTE_RF_CHAIN_CC1].RxGainNum * pAgcReq->Header.RxGainSize;
			break;
		}
	case LTE_CA_3CC:
		{
			RfChain1ArfcnLen = pAgcReq->Header.AgcRfChain[LTE_RF_CHAIN_CC1].ArfcnNum * pAgcReq->Header.ArfcnSize;
			RfChain1AgcLen = pAgcReq->Header.AgcRfChain[LTE_RF_CHAIN_CC1].RxGainNum * pAgcReq->Header.RxGainSize;
			RfChain2ArfcnLen = pAgcReq->Header.AgcRfChain[LTE_RF_CHAIN_CC2].ArfcnNum * pAgcReq->Header.ArfcnSize;
			RfChain2AgcLen = pAgcReq->Header.AgcRfChain[LTE_RF_CHAIN_CC2].RxGainNum * pAgcReq->Header.RxGainSize;
			break;
		}
	default:
		{
			LogFmtStrA(SPLOGLV_ERROR, "CaFlag parameter is out of range(0~3):%d", pAgcReq->Header.CaFlag);
			return SP_E_PHONE_INVALID_PARAMETER;
		}
	}
	//////////////////////////////////////////////////////////////////////////
	unsigned short usLength = (unsigned short)(sizeof(L1)
		+ sizeof(PC_MODEM_RF_V3_LTE_AGC_HEADER_T)
		+ sizeof(PC_MODEM_RF_V3_LTE_AGC_TRIGGER_T)
		+ RfChain0ArfcnLen
		+ RfChain0AgcLen
		+ RfChain1ArfcnLen
		+ RfChain1AgcLen
		+ RfChain2ArfcnLen
		+ RfChain2AgcLen);

	L1.head.SubCmdSize = usLength;
	L1.head.SubCmdCode = CAL_LTE_AGC_REQ;

	unsigned char *lpBuf = new unsigned char[usLength];
	if (NULL == lpBuf)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Memory allocation failed.", __FUNCTION__);
		return SP_E_PHONE_ALLOC_MEMORY;
	}
	else
	{
		ZeroMemory((void *)lpBuf, usLength);
	}

	unsigned char *pPos = lpBuf;
	uint16 usLen =  sizeof(L1);
	memcpy(pPos, &L1, usLen);
	//Header
	pPos += usLen;
	usLen = sizeof(PC_MODEM_RF_V3_LTE_AGC_HEADER_T);
	memcpy(pPos, &pAgcReq->Header, usLen);
	//Trigger
	pPos += usLen;
	usLen = sizeof(PC_MODEM_RF_V3_LTE_AGC_TRIGGER_T);
	memcpy(pPos, &pAgcReq->Trigger, usLen);
	//channel Rf chain0
	pPos += usLen;
	usLen = (unsigned short)(RfChain0ArfcnLen);
	memcpy(pPos, pAgcReq->ChannelsChian0, usLen);
	//apt Rf chain0
	pPos += usLen;
	usLen = (unsigned short)(RfChain0AgcLen);
	memcpy(pPos, pAgcReq->PointsChain0, usLen);
	//channel Rf chain1
	pPos += usLen;
	usLen = (unsigned short)(RfChain1ArfcnLen);
	memcpy(pPos, pAgcReq->ChannelsChian1, usLen);
	//apt Rf chain1
	pPos += usLen;
	usLen = (unsigned short)(RfChain1AgcLen);
	memcpy(pPos, pAgcReq->PointsChain1, usLen);
	//channel Rf chain2
	pPos += usLen;
	usLen = (unsigned short)(RfChain2ArfcnLen);
	memcpy(pPos, pAgcReq->ChannelsChian2, usLen);
	//apt Rf chain2
	pPos += usLen;
	usLen = (unsigned short)(RfChain2AgcLen);
	memcpy(pPos, pAgcReq->PointsChain2, usLen);
	DeclareDiagHeader(hd, DIAG_MODEM_RF_E, ZERO_SUBTYPE); 
	//////////////////////////////////////////////////////////////////////////
	CRecvPkgsList recvList;
	recvList.AddCondition(hd);
	SPRESULT res = SendAndRecv(hd, (const void* )lpBuf, usLength, recvList, m_dwTimeOut); 
	delete[] lpBuf;
	if (SP_OK != res)
	{
		return res;
	}

	uint32 recvLen = 0;
	//2nd packet
	if (!UnpackPRT(recvList.GetPackage(0), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: 2nd packet UnpackPRT return False", __FUNCTION__);
		return SP_E_PHONE_INVALID_DATA;
	}
	int nReqGainNum = pAgcReq->Header.AgcRfChain[LTE_RF_CHAIN_CC0].RxGainNum + pAgcReq->Header.AgcRfChain[LTE_RF_CHAIN_CC1].RxGainNum + pAgcReq->Header.AgcRfChain[LTE_RF_CHAIN_CC2].RxGainNum;
	uint32 unRecLen = sizeof(L1_COMMON_REQ_T) + sizeof(uint32)*2 + nReqGainNum*2;
	if (recvLen < unRecLen)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: 2nd packet length != sizeof(PC_MODEM_RF_V3_LTE_AGC_PARAM), %d != %d", __FUNCTION__, recvLen, unRecLen);
		return SP_E_PHONE_INVALID_LENGTH;
	}
	else
	{
		short RssiNumber = *(short*)(m_diagBuff + sizeof(L1_COMMON_REQ_T) + sizeof(int));
		if (RssiNumber != nReqGainNum)
		{
			LogFmtStrA(SPLOGLV_ERROR, "[CCaliCmd::ModemV3_lteAgc] RssiNumber != Header.PointNumber, %d != %d!", RssiNumber, nReqGainNum);
			return SP_E_PHONE_INVALID_DATA;
		}
		else
		{
			memcpy(pAgcRlst, ((char*)m_diagBuff) + sizeof(L1_COMMON_REQ_T) + sizeof(int)*2, RssiNumber * sizeof(unsigned short));	
		}
		return SP_OK;
	}
}

SPRESULT CCaliCmd::ModemV4_LTE_AgcCal(const PC_MODEM_RF_V4_LTE_AGC_PARAM* pAgcReq, unsigned short* pAgcRlst)
{
	if (NULL == pAgcReq || NULL == pAgcRlst)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid parameters!", __FUNCTION__);
		return SP_E_PHONE_INVALID_PARAMETER;
	}

	LogRawStrA(SPLOGLV_INFO, "ModemV4_lteAgc");

	L1_COMMON_REQ_T    L1;
	ZeroMemory((void*)&L1, sizeof(L1));

	//////////////////////////////////////////////////////////////////////////
	uint16 RfChain0ArfcnLen = pAgcReq->Header.AgcRfChain[LTE_RF_CHAIN_CC0].ArfcnNum * pAgcReq->Header.ArfcnSize;
	uint16 RfChain0AgcLen = pAgcReq->Header.AgcRfChain[LTE_RF_CHAIN_CC0].RxGainNum * pAgcReq->Header.RxGainSize;

	uint16 RfChain1ArfcnLen = 0;
	uint16 RfChain1AgcLen = 0;

	uint16 RfChain2ArfcnLen = 0;
	uint16 RfChain2AgcLen = 0;

	switch (pAgcReq->Header.CaFlag)
	{
	case LTE_NON_CA:
		break;
	case LTE_CA_NON_CONTINUOUS:
	case LTE_CA_CONTINUOUS:
	{
		RfChain1ArfcnLen = pAgcReq->Header.AgcRfChain[LTE_RF_CHAIN_CC1].ArfcnNum * pAgcReq->Header.ArfcnSize;
		RfChain1AgcLen = pAgcReq->Header.AgcRfChain[LTE_RF_CHAIN_CC1].RxGainNum * pAgcReq->Header.RxGainSize;
		break;
	}
	case LTE_CA_3CC:
	{
		RfChain1ArfcnLen = pAgcReq->Header.AgcRfChain[LTE_RF_CHAIN_CC1].ArfcnNum * pAgcReq->Header.ArfcnSize;
		RfChain1AgcLen = pAgcReq->Header.AgcRfChain[LTE_RF_CHAIN_CC1].RxGainNum * pAgcReq->Header.RxGainSize;
		RfChain2ArfcnLen = pAgcReq->Header.AgcRfChain[LTE_RF_CHAIN_CC2].ArfcnNum * pAgcReq->Header.ArfcnSize;
		RfChain2AgcLen = pAgcReq->Header.AgcRfChain[LTE_RF_CHAIN_CC2].RxGainNum * pAgcReq->Header.RxGainSize;
		break;
	}
	default:
	{
		LogFmtStrA(SPLOGLV_ERROR, "CaFlag parameter is out of range(0~3):%d", pAgcReq->Header.CaFlag);
		return SP_E_PHONE_INVALID_PARAMETER;
	}
	}
	//////////////////////////////////////////////////////////////////////////
	unsigned short usLength = (unsigned short)(sizeof(L1)
		+ sizeof(PC_MODEM_RF_V3_LTE_AGC_HEADER_T)
		+ sizeof(PC_MODEM_RF_V3_LTE_AGC_TRIGGER_T)
		+ RfChain0ArfcnLen
		+ RfChain0AgcLen
		+ RfChain1ArfcnLen
		+ RfChain1AgcLen
		+ RfChain2ArfcnLen
		+ RfChain2AgcLen);

	L1.head.SubCmdSize = usLength;
	L1.head.SubCmdCode = CAL_LTE_AGC_REQ_V2;

	unsigned char* lpBuf = new unsigned char[usLength];
	if (NULL == lpBuf)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Memory allocation failed.", __FUNCTION__);
		return SP_E_PHONE_ALLOC_MEMORY;
	}
	else
	{
		ZeroMemory((void*)lpBuf, usLength);
	}

	unsigned char* pPos = lpBuf;
	uint16 usLen = sizeof(L1);
	memcpy(pPos, &L1, usLen);
	//Header
	pPos += usLen;
	usLen = sizeof(PC_MODEM_RF_V3_LTE_AGC_HEADER_T);
	memcpy(pPos, &pAgcReq->Header, usLen);
	//Trigger
	pPos += usLen;
	usLen = sizeof(PC_MODEM_RF_V3_LTE_AGC_TRIGGER_T);
	memcpy(pPos, &pAgcReq->Trigger, usLen);
	//channel Rf chain0
	pPos += usLen;
	usLen = (unsigned short)(RfChain0ArfcnLen);
	memcpy(pPos, pAgcReq->ChannelsChian0, usLen);
	//apt Rf chain0
	pPos += usLen;
	usLen = (unsigned short)(RfChain0AgcLen);
	memcpy(pPos, pAgcReq->PointsChain0, usLen);
	//channel Rf chain1
	pPos += usLen;
	usLen = (unsigned short)(RfChain1ArfcnLen);
	memcpy(pPos, pAgcReq->ChannelsChian1, usLen);
	//apt Rf chain1
	pPos += usLen;
	usLen = (unsigned short)(RfChain1AgcLen);
	memcpy(pPos, pAgcReq->PointsChain1, usLen);
	//channel Rf chain2
	pPos += usLen;
	usLen = (unsigned short)(RfChain2ArfcnLen);
	memcpy(pPos, pAgcReq->ChannelsChian2, usLen);
	//apt Rf chain2
	pPos += usLen;
	usLen = (unsigned short)(RfChain2AgcLen);
	memcpy(pPos, pAgcReq->PointsChain2, usLen);
	DeclareDiagHeader(hd, DIAG_MODEM_RF_E, ZERO_SUBTYPE);
	//////////////////////////////////////////////////////////////////////////
	CRecvPkgsList recvList;
	recvList.AddCondition(hd);
	SPRESULT res = SendAndRecv(hd, (const void*)lpBuf, usLength, recvList, m_dwTimeOut);
	delete[] lpBuf;
	if (SP_OK != res)
	{
		return res;
	}

	uint32 recvLen = 0;
	//2nd packet
	if (!UnpackPRT(recvList.GetPackage(0), NULL, (void*)&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: 2nd packet UnpackPRT return False", __FUNCTION__);
		return SP_E_PHONE_INVALID_DATA;
	}
	int nReqGainNum = pAgcReq->Header.AgcRfChain[LTE_RF_CHAIN_CC0].RxGainNum + pAgcReq->Header.AgcRfChain[LTE_RF_CHAIN_CC1].RxGainNum + pAgcReq->Header.AgcRfChain[LTE_RF_CHAIN_CC2].RxGainNum;
	uint32 unRecLen = sizeof(L1_COMMON_REQ_T) + sizeof(uint32) * 2 + nReqGainNum * 2;
	if (recvLen < unRecLen)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: 2nd packet length != sizeof(PC_MODEM_RF_V3_LTE_AGC_PARAM), %d != %d", __FUNCTION__, recvLen, unRecLen);
		return SP_E_PHONE_INVALID_LENGTH;
	}
	else
	{
		short RssiNumber = *(short*)(m_diagBuff + sizeof(L1_COMMON_REQ_T) + sizeof(int));
		if (RssiNumber != nReqGainNum)
		{
			LogFmtStrA(SPLOGLV_ERROR, "[CCaliCmd::ModemV3_lteAgc] RssiNumber != Header.PointNumber, %d != %d!", RssiNumber, nReqGainNum);
			return SP_E_PHONE_INVALID_DATA;
		}
		else
		{
			memcpy(pAgcRlst, ((char*)m_diagBuff) + sizeof(L1_COMMON_REQ_T) + sizeof(int) * 2, RssiNumber * sizeof(unsigned short));
		}
		return SP_OK;
	}
}

SPRESULT CCaliCmd::ModemV3_LTE_PdetCal(const PC_MODEM_RF_V3_LTE_PDET_PARAM *pPdetReq, PC_MODEM_RF_V3_LTE_APC_RSP_T *pPdetRlst)
{
	CheckValidPointer(pPdetReq);
	CheckValidPointer(pPdetRlst);

	LogRawStrA(SPLOGLV_INFO, "ModemV3_ltePdet");  

	L1_COMMON_REQ_T L1;    
	ZeroMemory(&L1, sizeof(L1_COMMON_REQ_T));
	//////////////////////////////////////////////////////////////////////////
	uint16 RfChain0ArfcnLen = Convert16(pPdetReq->Header.PdetRfChain[LTE_RF_CHAIN_CC0].ArfcnNum * pPdetReq->Header.ArfcnSize);
	uint16 RfChain0AptLen = Convert16((uint16)(pPdetReq->Header.PdetRfChain[LTE_RF_CHAIN_CC0].AptNum * pPdetReq->Header.AptSize));

	uint16 RfChain1ArfcnLen = 0;
	uint16 RfChain1AptLen = 0;

	if (LTE_NON_CA != pPdetReq->Header.CAFlag)
	{
		RfChain1ArfcnLen = pPdetReq->Header.PdetRfChain[LTE_RF_CHAIN_CC1].ArfcnNum * pPdetReq->Header.ArfcnSize;
		RfChain1AptLen = Convert16((uint16)(pPdetReq->Header.PdetRfChain[LTE_RF_CHAIN_CC1].AptNum * pPdetReq->Header.AptSize));
	}

	uint16 usLength =  (uint16)(sizeof(L1) + sizeof(PC_MODEM_RF_V3_LTE_PDT_HEADER_T)) + RfChain0ArfcnLen + RfChain0AptLen + RfChain1ArfcnLen + RfChain1AptLen;

	L1.head.SubCmdSize = usLength;
	L1.head.SubCmdCode = CAL_LTE_PDET_REQ;

	uint8* pBuff = NULL;
	try
	{
		pBuff = new uint8[usLength];
	}
	catch (const std::bad_alloc& /*e*/)
	{
		pBuff = NULL;
	}
	if (NULL == pBuff)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Memory allocation failed.", __FUNCTION__);
		return SP_E_PHONE_ALLOC_MEMORY;
	}

	unsigned char *pPos = pBuff;
	uint32 usLen =  sizeof(L1);
	memcpy(pPos, &L1, usLen);
	//Header
	pPos += usLen;
	usLen = sizeof(PC_MODEM_RF_V3_LTE_PDT_HEADER_T);
	memcpy(pPos, &pPdetReq->Header, usLen);
	//channel RF chain0
	pPos += usLen;
	usLen = (unsigned short)(RfChain0ArfcnLen);
	memcpy(pPos, pPdetReq->pChannelsChain0, usLen);
	//apt RF chain0
	pPos += usLen;
	usLen = (unsigned short)(RfChain0AptLen);
	memcpy(pPos, pPdetReq->pAptsChain0, usLen);
	//channel RF chain1
	pPos += usLen;
	usLen = (unsigned short)(RfChain1ArfcnLen);
	memcpy(pPos, pPdetReq->pChannelsChain1, usLen);
	//apt RF chain1
	pPos += usLen;
	usLen = (unsigned short)(RfChain1AptLen);
	memcpy(pPos, pPdetReq->pAptsChain1, usLen);

	DeclareDiagHeader(hd, DIAG_MODEM_RF_E, ZERO_SUBTYPE); 

	CRecvPkgsList recvList;
	recvList.AddCondition(hd);
	SPRESULT res = SendAndRecv(hd, (const void* )pBuff, usLength, recvList, m_dwTimeOut); 
	delete[] pBuff;
	if (SP_OK != res)
	{
		return res;
	}

	uint32 recvLen = 0;
	//2nd packet
	if (!UnpackPRT(recvList.GetPackage(0), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: 2nd packet UnpackPRT return False", __FUNCTION__);
		return SP_E_PHONE_INVALID_DATA;
	}
	if (recvLen < sizeof(L1_MODEM_RF_V3_LTE_APC_RSP_T))
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Response packet length %d is not correct", __FUNCTION__, recvLen);
		return SP_E_PHONE_INVALID_LENGTH;
	}
	////fetch result
	unsigned short RetNumber = *(unsigned short*)(m_diagBuff + sizeof(L1_COMMON_REQ_T)+ sizeof(uint32));
	L1_MODEM_RF_V3_LTE_APC_RSP_T* pRLT = (L1_MODEM_RF_V3_LTE_APC_RSP_T* )m_diagBuff;
	uint16 uType = Convert16(pRLT->CmdHeader.SubCmdCode);
	if (CAL_LTE_PDET_REQ != uType)
	{
		LogFmtStrA(SPLOGLV_ERROR, "Invalid response command ID: 0x%X != 0x%X", uType, CAL_LTE_PDET_REQ);
		return SP_E_PHONE_INVALID_DATA;       
	}

	if (SP_OK != pRLT->status)
	{
		LogFmtStrA(SPLOGLV_ERROR, "The returned opCode is %d", pRLT->status);
		return SP_E_PHONE_INVALID_STATE; 
	}

	if (_msize(pPdetRlst->pPdetRlst) != (sizeof(uint16)*RetNumber))
	{
		LogFmtStrA(SPLOGLV_ERROR, "Invalid Pdt result count(in bytes), Expected(Input Size) count %d, response count %d", _msize(pPdetRlst->pPdetRlst), RetNumber*2);
		return SP_E_PHONE_INVALID_DATA;  
	}
	pPdetRlst->PdtRlstCnt = RetNumber;

	memcpy(pPdetRlst->pPdetRlst, ((char*)m_diagBuff) + sizeof(L1_COMMON_REQ_T) + sizeof(uint32), RetNumber * sizeof(unsigned short));

	return SP_OK;
}

SPRESULT CCaliCmd::ModemV3_LTE_FdiqCal(const PC_MODEM_RF_V3_LTE_FDIQ_PARAM *pFdiqReq, PC_MODEM_RF_V3_LTE_FDIQ_RSP_T *pFdiqRlst)
{
	CheckValidPointer(pFdiqReq);
	CheckValidPointer(pFdiqRlst);
	LogRawStrA(SPLOGLV_INFO, "ModemV3_lteFdiq Cal:");

	L1_COMMON_REQ_T L1;
	ZeroMemory((void* )&L1, sizeof(L1));

	//////////////////////////////////////////////////////////////////////////
	uint16 bandnum = 0;
	uint16 rcount = 0;
	uint16 RfChain0BandLen = pFdiqReq->Header.fdiq_ch_ctrl[LTE_RF_CHAIN_CC0].band_num*pFdiqReq->Header.fdiq_ch_ctrl[LTE_RF_CHAIN_CC0].band_size;
	bandnum +=  pFdiqReq->Header.fdiq_ch_ctrl[LTE_RF_CHAIN_CC0].band_num;
	uint16 RfChain1BandLen = 0;
	uint16 RfChain2BandLen = 0;

	switch(pFdiqReq->Header.ca_flag)
	{
	case LTE_NON_CA:
		break;
	case LTE_CA_NON_CONTINUOUS:
	case LTE_CA_CONTINUOUS:
		{
			RfChain1BandLen = pFdiqReq->Header.fdiq_ch_ctrl[LTE_RF_CHAIN_CC1].band_num*pFdiqReq->Header.fdiq_ch_ctrl[LTE_RF_CHAIN_CC1].band_size;
			bandnum += pFdiqReq->Header.fdiq_ch_ctrl[LTE_RF_CHAIN_CC1].band_num;
			break;
		}
	case LTE_CA_3CC:
		{
			RfChain1BandLen = pFdiqReq->Header.fdiq_ch_ctrl[LTE_RF_CHAIN_CC1].band_num*pFdiqReq->Header.fdiq_ch_ctrl[LTE_RF_CHAIN_CC1].band_size;
			RfChain2BandLen = pFdiqReq->Header.fdiq_ch_ctrl[LTE_RF_CHAIN_CC2].band_num*pFdiqReq->Header.fdiq_ch_ctrl[LTE_RF_CHAIN_CC2].band_size;
			bandnum += pFdiqReq->Header.fdiq_ch_ctrl[LTE_RF_CHAIN_CC1].band_num;
			bandnum += pFdiqReq->Header.fdiq_ch_ctrl[LTE_RF_CHAIN_CC2].band_num;
			break;
		}
	default:
		{
			LogFmtStrA(SPLOGLV_ERROR, "CaFlag parameter is out of range(0~3):%d", pFdiqReq->Header.ca_flag);
			return SP_E_PHONE_INVALID_PARAMETER;
		}
	}

	rcount = bandnum*pFdiqReq->Header.bw_num*pFdiqReq->Header.lna_num;
	//////////////////////////////////////////////////////////////////////////
	unsigned short usLength = (unsigned short)(sizeof(L1)
		+ sizeof(PC_MODEM_RF_V3_LTE_FDIQ_HEADER_T)
		+ sizeof(PC_MODEM_RF_V3_LTE_FDIQ_TRIGGER_T)
		+ RfChain0BandLen
		+ RfChain1BandLen
		+ RfChain2BandLen);

	L1.head.SubCmdCode    = Convert16(CAL_LTE_FDIQ_REQ);
	L1.head.SubCmdSize    = usLength;
	//////////////////////////////////////////////////////////////////////////
	uint8* pBuff = NULL;
	try
	{
		pBuff = new uint8[usLength];
	}
	catch (const std::bad_alloc& /*e*/)
	{
		pBuff = NULL;
	}
	if (NULL == pBuff)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Memory allocation failed.", __FUNCTION__);
		return SP_E_PHONE_ALLOC_MEMORY;
	}

	unsigned char *pPos = pBuff;
	uint32 usLen =  sizeof(L1);
	memcpy(pPos, &L1, usLen);
	//Header
	pPos += usLen;
	usLen = sizeof(PC_MODEM_RF_V3_LTE_FDIQ_HEADER_T);
	memcpy(pPos, &pFdiqReq->Header, usLen);
	//Trigger
	pPos += usLen;
	usLen = sizeof(PC_MODEM_RF_V3_LTE_FDIQ_TRIGGER_T);
	memcpy(pPos, &pFdiqReq->Trigger, usLen);
	//channel RF chain0
	pPos += usLen;
	usLen = (unsigned short)(RfChain0BandLen);
	memcpy(pPos, pFdiqReq->pChannelsChain0, usLen);
	//channel RF chain1
	pPos += usLen;
	usLen = (unsigned short)(RfChain1BandLen);
	memcpy(pPos, pFdiqReq->pChannelsChain1, usLen);
	//channel RF chain2
	pPos += usLen;
	usLen = (unsigned short)(RfChain2BandLen);
	memcpy(pPos, pFdiqReq->pChannelsChain2, usLen);

	DeclareDiagHeader(hd, DIAG_MODEM_RF_E, ZERO_SUBTYPE);

	CRecvPkgsList recvList;
	recvList.AddCondition(hd);
	SPRESULT res = SendAndRecv(hd, (const void* )pBuff, usLength, recvList, m_dwTimeOut); 
	delete[] pBuff;
	if (SP_OK != res)
	{
		return res;
	}

	uint32 recvLen = 0;

	//2nd packet
	if (!UnpackPRT(recvList.GetPackage(0), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: 2nd packet UnpackPRT return False", __FUNCTION__);
		return SP_E_PHONE_INVALID_DATA;
	}

	if (recvLen != (sizeof(L1_COMMON_REQ_T) + sizeof(PC_MODEM_RF_V3_LTE_FDIQ_PGK_RET)*rcount + sizeof(uint16) + sizeof(uint16)))
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: 2nd packet length != sizeof(PC_MODEM_RF_V3_LTE_FDIQ_RSP_T), %d != %d", __FUNCTION__, recvLen, (sizeof(L1_COMMON_REQ_T) + sizeof(PC_MODEM_RF_V3_LTE_FDIQ_PGK_RET)*rcount + sizeof(uint16) + sizeof(uint16)));
		return SP_E_PHONE_INVALID_LENGTH;
	}

	PC_MODEM_RF_V3_LTE_FDIQ_RSP_T* pRLT = (PC_MODEM_RF_V3_LTE_FDIQ_RSP_T* )(m_diagBuff+sizeof(L1));

	if (SP_OK != pRLT->status)
	{
		LogFmtStrA(SPLOGLV_ERROR, "Fdiq Cal DUT response OpCode is %d", pRLT->status);
		return SP_E_PHONE_INVALID_DATA; 
	}

	if (pRLT->result_count!= rcount)
	{
		LogFmtStrA(SPLOGLV_ERROR, "The input band count %d doesn't match DUT response band count %d.", rcount, pRLT->result_count);
		return SP_E_PHONE_INVALID_DATA; 
	}
	pFdiqRlst->result_count = pRLT->result_count;
	pFdiqRlst->status = pRLT->status;

	memcpy(pFdiqRlst->pdata, &pRLT->pdata, sizeof(PC_MODEM_RF_V3_LTE_FDIQ_PGK_RET)*pRLT->result_count);

	return SP_OK;
}

SPRESULT CCaliCmd::ModemV4_LTE_FdiqCal(const PC_MODEM_RF_V4_LTE_FDIQ_PARAM* pFdiqReq, PC_MODEM_RF_V3_LTE_FDIQ_RSP_T* pFdiqRlst)
{
	CheckValidPointer(pFdiqReq);
	CheckValidPointer(pFdiqRlst);
	LogRawStrA(SPLOGLV_INFO, "ModemV4_lteFdiq Cal:");

	L1_COMMON_REQ_T L1;
	ZeroMemory((void*)&L1, sizeof(L1));

	//////////////////////////////////////////////////////////////////////////
	uint16 bandnum = 0;
	uint16 rcount = 0;
	uint16 RfChain0BandLen = pFdiqReq->Header.fdiq_ch_ctrl[LTE_RF_CHAIN_CC0].band_num * pFdiqReq->Header.fdiq_ch_ctrl[LTE_RF_CHAIN_CC0].band_size;
	bandnum += pFdiqReq->Header.fdiq_ch_ctrl[LTE_RF_CHAIN_CC0].band_num;
	uint16 RfChain1BandLen = 0;
	uint16 RfChain2BandLen = 0;

	switch (pFdiqReq->Header.ca_flag)
	{
	case LTE_NON_CA:
		break;
	case LTE_CA_NON_CONTINUOUS:
	case LTE_CA_CONTINUOUS:
	{
		RfChain1BandLen = pFdiqReq->Header.fdiq_ch_ctrl[LTE_RF_CHAIN_CC1].band_num * pFdiqReq->Header.fdiq_ch_ctrl[LTE_RF_CHAIN_CC1].band_size;
		bandnum += pFdiqReq->Header.fdiq_ch_ctrl[LTE_RF_CHAIN_CC1].band_num;
		break;
	}
	case LTE_CA_3CC:
	{
		RfChain1BandLen = pFdiqReq->Header.fdiq_ch_ctrl[LTE_RF_CHAIN_CC1].band_num * pFdiqReq->Header.fdiq_ch_ctrl[LTE_RF_CHAIN_CC1].band_size;
		RfChain2BandLen = pFdiqReq->Header.fdiq_ch_ctrl[LTE_RF_CHAIN_CC2].band_num * pFdiqReq->Header.fdiq_ch_ctrl[LTE_RF_CHAIN_CC2].band_size;
		bandnum += pFdiqReq->Header.fdiq_ch_ctrl[LTE_RF_CHAIN_CC1].band_num;
		bandnum += pFdiqReq->Header.fdiq_ch_ctrl[LTE_RF_CHAIN_CC2].band_num;
		break;
	}
	default:
	{
		LogFmtStrA(SPLOGLV_ERROR, "CaFlag parameter is out of range(0~3):%d", pFdiqReq->Header.ca_flag);
		return SP_E_PHONE_INVALID_PARAMETER;
	}
	}

	rcount = bandnum * pFdiqReq->Header.bw_num * pFdiqReq->Header.lna_num;
	//////////////////////////////////////////////////////////////////////////
	unsigned short usLength = (unsigned short)(sizeof(L1)
		+ sizeof(PC_MODEM_RF_V3_LTE_FDIQ_HEADER_T)
		+ sizeof(PC_MODEM_RF_V3_LTE_FDIQ_TRIGGER_T)
		+ RfChain0BandLen
		+ RfChain1BandLen
		+ RfChain2BandLen);

	L1.head.SubCmdCode = Convert16(CAL_LTE_FDIQ_REQ_V2);
	L1.head.SubCmdSize = usLength;
	//////////////////////////////////////////////////////////////////////////
	uint8* pBuff = NULL;
	try
	{
		pBuff = new uint8[usLength];
	}
	catch (const std::bad_alloc& /*e*/)
	{
		pBuff = NULL;
	}
	if (NULL == pBuff)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Memory allocation failed.", __FUNCTION__);
		return SP_E_PHONE_ALLOC_MEMORY;
	}

	unsigned char* pPos = pBuff;
	uint32 usLen = sizeof(L1);
	memcpy(pPos, &L1, usLen);
	//Header
	pPos += usLen;
	usLen = sizeof(PC_MODEM_RF_V3_LTE_FDIQ_HEADER_T);
	memcpy(pPos, &pFdiqReq->Header, usLen);
	//Trigger
	pPos += usLen;
	usLen = sizeof(PC_MODEM_RF_V3_LTE_FDIQ_TRIGGER_T);
	memcpy(pPos, &pFdiqReq->Trigger, usLen);
	//channel RF chain0
	pPos += usLen;
	usLen = (unsigned short)(RfChain0BandLen);
	memcpy(pPos, pFdiqReq->pChannelsChain0, usLen);
	//channel RF chain1
	pPos += usLen;
	usLen = (unsigned short)(RfChain1BandLen);
	memcpy(pPos, pFdiqReq->pChannelsChain1, usLen);
	//channel RF chain2
	pPos += usLen;
	usLen = (unsigned short)(RfChain2BandLen);
	memcpy(pPos, pFdiqReq->pChannelsChain2, usLen);

	DeclareDiagHeader(hd, DIAG_MODEM_RF_E, ZERO_SUBTYPE);

	CRecvPkgsList recvList;
	recvList.AddCondition(hd);
	SPRESULT res = SendAndRecv(hd, (const void*)pBuff, usLength, recvList, m_dwTimeOut);
	delete[] pBuff;
	if (SP_OK != res)
	{
		return res;
	}

	uint32 recvLen = 0;

	//2nd packet
	if (!UnpackPRT(recvList.GetPackage(0), NULL, (void*)&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: 2nd packet UnpackPRT return False", __FUNCTION__);
		return SP_E_PHONE_INVALID_DATA;
	}

	if (recvLen != (sizeof(L1_COMMON_REQ_T) + sizeof(PC_MODEM_RF_V3_LTE_FDIQ_PGK_RET) * rcount + sizeof(uint16) + sizeof(uint16)))
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: 2nd packet length != sizeof(PC_MODEM_RF_V3_LTE_FDIQ_RSP_T), %d != %d", __FUNCTION__, recvLen, (sizeof(L1_COMMON_REQ_T) + sizeof(PC_MODEM_RF_V3_LTE_FDIQ_PGK_RET) * rcount + sizeof(uint16) + sizeof(uint16)));
		return SP_E_PHONE_INVALID_LENGTH;
	}

	PC_MODEM_RF_V3_LTE_FDIQ_RSP_T* pRLT = (PC_MODEM_RF_V3_LTE_FDIQ_RSP_T*)(m_diagBuff + sizeof(L1));

	if (SP_OK != pRLT->status)
	{
		LogFmtStrA(SPLOGLV_ERROR, "Fdiq Cal DUT response OpCode is %d", pRLT->status);
		return SP_E_PHONE_INVALID_DATA;
	}

	if (pRLT->result_count != rcount)
	{
		LogFmtStrA(SPLOGLV_ERROR, "The input band count %d doesn't match DUT response band count %d.", rcount, pRLT->result_count);
		return SP_E_PHONE_INVALID_DATA;
	}
	pFdiqRlst->result_count = pRLT->result_count;
	pFdiqRlst->status = pRLT->status;

	memcpy(pFdiqRlst->pdata, &pRLT->pdata, sizeof(PC_MODEM_RF_V3_LTE_FDIQ_PGK_RET) * pRLT->result_count);

	return SP_OK;
}

SPRESULT CCaliCmd::ModemV3_LTE_PADroopCal( const PC_MODEM_RF_V3_LTE_PADROOP_PARAM *pPadroopReq, PC_MODEM_RF_V3_LTE_PADROOP_RSP_T *pPadroopRlst )
{
	CheckValidPointer(pPadroopReq);
	CheckValidPointer(pPadroopRlst);
	LogRawStrA(SPLOGLV_INFO, "ModemV3_LTE_PADroop Cal:");

	L1_COMMON_REQ_T L1;
	ZeroMemory((void* )&L1, sizeof(L1));

	uint16 usLength =  (uint16)((sizeof(L1) + sizeof(PC_MODEM_RF_V3_LTE_PADROOP_HEADER_T)) 
		+ pPadroopReq->Header.band_num*(sizeof(PC_MODEM_RF_V3_LTE_PADROOP_BAND_T))
		+ pPadroopReq->padroop_arfcn_count*sizeof(PC_MODEM_RF_V3_LTE_PADROOP_ARFCN_T));

	L1.head.SubCmdCode   = Convert16(CAL_LTE_PADROP_REQ);
	L1.head.SubCmdSize   = usLength;
	//////////////////////////////////////////////////////////////////////////
	uint8* pBuff = NULL;
	try
	{
		pBuff = new uint8[usLength];
	}
	catch (const std::bad_alloc& /*e*/)
	{
		pBuff = NULL;
	}
	if (NULL == pBuff)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Memory allocation failed.", __FUNCTION__);
		return SP_E_PHONE_ALLOC_MEMORY;
	}

	unsigned char *pPos = pBuff;
	uint32 usLen =  sizeof(L1);
	memcpy(pPos, &L1, usLen);
	//Header
	pPos += usLen;
	usLen = sizeof(PC_MODEM_RF_V3_LTE_PADROOP_HEADER_T);
	memcpy(pPos, &pPadroopReq->Header, usLen);
	//band
	pPos += usLen;
	usLen = (unsigned short)(pPadroopReq->Header.band_num*sizeof(PC_MODEM_RF_V3_LTE_PADROOP_BAND_T));
	memcpy(pPos, pPadroopReq->padroop_band, usLen);
	//arfcn
	pPos += usLen;
	usLen = (unsigned short)(pPadroopReq->padroop_arfcn_count*sizeof(PC_MODEM_RF_V3_LTE_PADROOP_ARFCN_T));
	memcpy(pPos, pPadroopReq->padroop_arfcn, usLen);

	DeclareDiagHeader(hd, DIAG_MODEM_RF_E, ZERO_SUBTYPE);

	CRecvPkgsList recvList;
	recvList.AddCondition(hd);
	SPRESULT res = SendAndRecv(hd, (const void* )pBuff, usLength, recvList, m_dwTimeOut); 
	delete[] pBuff;
	if (SP_OK != res)
	{
		return res;
	}

	uint32 recvLen = 0;

	//2nd packet
	if (!UnpackPRT(recvList.GetPackage(0), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: 2nd packet UnpackPRT return False", __FUNCTION__);
		return SP_E_PHONE_INVALID_DATA;
	}

	uint32 expectLen = 0;
	if(0 == pPadroopReq->Header.version)
	{
		expectLen = sizeof(L1_COMMON_REQ_T) + sizeof(uint32) + sizeof(uint16);
	}
	else
	{
		expectLen = sizeof(L1_COMMON_REQ_T) + sizeof(uint32) + sizeof(uint16) + pPadroopReq->padroop_arfcn_count*sizeof(uint16);
	}

	if (recvLen != expectLen)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: 2nd packet length != sizeof(PC_MODEM_RF_V3_LTE_PADROOP_RSP_T), %d != %d", __FUNCTION__, recvLen, expectLen);
		return SP_E_PHONE_INVALID_LENGTH;
	}

	PC_MODEM_RF_V3_LTE_PADROOP_RSP_T* pRLT = (PC_MODEM_RF_V3_LTE_PADROOP_RSP_T* )(m_diagBuff+sizeof(L1));

	if (SP_OK != pRLT->status)
	{
		LogFmtStrA(SPLOGLV_ERROR, "PADroop Cal DUT response OpCode is %d", pRLT->status);
		return SP_E_PHONE_INVALID_DATA; 
	}

	if(0 != pPadroopReq->Header.version)
	{
		if (pRLT->data_num != pPadroopReq->padroop_arfcn_count)
		{
			LogFmtStrA(SPLOGLV_ERROR, "The input data count %d doesn't match DUT response data count %d.", pRLT->data_num, pPadroopReq->padroop_arfcn_count);
			return SP_E_PHONE_INVALID_DATA; 
		}
	}
	
	pPadroopRlst->data_num = pRLT->data_num;
	pPadroopRlst->status = pRLT->status;

	memcpy(pPadroopRlst, &pRLT->data, sizeof(uint16)*pRLT->data_num);

	return SP_OK;
}

SPRESULT CCaliCmd::ModemV4_LTE_PADroopCal(const PC_MODEM_RF_V4_LTE_PADROOP_PARAM* pPadroopReq, PC_MODEM_RF_V3_LTE_PADROOP_RSP_T* pPadroopRlst)
{
	CheckValidPointer(pPadroopReq);
	CheckValidPointer(pPadroopRlst);
	LogRawStrA(SPLOGLV_INFO, "ModemV4_LTE_PADroop Cal:");

	L1_COMMON_REQ_T L1;
	ZeroMemory((void*)&L1, sizeof(L1));

	uint16 usLength = (uint16)((sizeof(L1) + sizeof(PC_MODEM_RF_V3_LTE_PADROOP_HEADER_T))
		+ pPadroopReq->Header.band_num * (sizeof(PC_MODEM_RF_V3_LTE_PADROOP_BAND_T))
		+ pPadroopReq->padroop_arfcn_count * sizeof(PC_MODEM_RF_V4_LTE_PADROOP_ARFCN_T));

	L1.head.SubCmdCode = Convert16(CAL_LTE_PADROP_REQ_V2);
	L1.head.SubCmdSize = usLength;
	//////////////////////////////////////////////////////////////////////////
	uint8* pBuff = NULL;
	try
	{
		pBuff = new uint8[usLength];
	}
	catch (const std::bad_alloc& /*e*/)
	{
		pBuff = NULL;
	}
	if (NULL == pBuff)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Memory allocation failed.", __FUNCTION__);
		return SP_E_PHONE_ALLOC_MEMORY;
	}

	unsigned char* pPos = pBuff;
	uint32 usLen = sizeof(L1);
	memcpy(pPos, &L1, usLen);
	//Header
	pPos += usLen;
	usLen = sizeof(PC_MODEM_RF_V3_LTE_PADROOP_HEADER_T);
	memcpy(pPos, &pPadroopReq->Header, usLen);
	//band
	pPos += usLen;
	usLen = (unsigned short)(pPadroopReq->Header.band_num * sizeof(PC_MODEM_RF_V3_LTE_PADROOP_BAND_T));
	memcpy(pPos, pPadroopReq->padroop_band, usLen);
	//arfcn
	pPos += usLen;
	usLen = (unsigned short)(pPadroopReq->padroop_arfcn_count * sizeof(PC_MODEM_RF_V4_LTE_PADROOP_ARFCN_T));
	memcpy(pPos, pPadroopReq->padroop_arfcn, usLen);

	DeclareDiagHeader(hd, DIAG_MODEM_RF_E, ZERO_SUBTYPE);

	CRecvPkgsList recvList;
	recvList.AddCondition(hd);
	SPRESULT res = SendAndRecv(hd, (const void*)pBuff, usLength, recvList, m_dwTimeOut);
	delete[] pBuff;
	if (SP_OK != res)
	{
		return res;
	}

	uint32 recvLen = 0;

	//2nd packet
	if (!UnpackPRT(recvList.GetPackage(0), NULL, (void*)&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: 2nd packet UnpackPRT return False", __FUNCTION__);
		return SP_E_PHONE_INVALID_DATA;
	}

	uint32 expectLen = 0;
	if (0 == pPadroopReq->Header.version)
	{
		expectLen = sizeof(L1_COMMON_REQ_T) + sizeof(uint32) + sizeof(uint16);
	}
	else
	{
		expectLen = sizeof(L1_COMMON_REQ_T) + sizeof(uint32) + sizeof(uint16) + pPadroopReq->padroop_arfcn_count * sizeof(uint16);
	}

	if (recvLen != expectLen)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: 2nd packet length != sizeof(PC_MODEM_RF_V3_LTE_PADROOP_RSP_T), %d != %d", __FUNCTION__, recvLen, expectLen);
		return SP_E_PHONE_INVALID_LENGTH;
	}

	PC_MODEM_RF_V3_LTE_PADROOP_RSP_T* pRLT = (PC_MODEM_RF_V3_LTE_PADROOP_RSP_T*)(m_diagBuff + sizeof(L1));

	if (SP_OK != pRLT->status)
	{
		LogFmtStrA(SPLOGLV_ERROR, "PADroop Cal DUT response OpCode is %d", pRLT->status);
		return SP_E_PHONE_INVALID_DATA;
	}

	if (0 != pPadroopReq->Header.version)
	{
		if (pRLT->data_num != pPadroopReq->padroop_arfcn_count)
		{
			LogFmtStrA(SPLOGLV_ERROR, "The input data count %d doesn't match DUT response data count %d.", pRLT->data_num, pPadroopReq->padroop_arfcn_count);
			return SP_E_PHONE_INVALID_DATA;
		}
	}

	pPadroopRlst->data_num = pRLT->data_num;
	pPadroopRlst->status = pRLT->status;

	memcpy(pPadroopRlst, &pRLT->data, sizeof(uint16) * pRLT->data_num);

	return SP_OK;
}

SPRESULT CCaliCmd::ModemV3_LTE_TRX_Tune(const PC_MODEM_RF_V3_LTE_TRX_REQ_T *pTRXReq)
{
	CheckValidPointer(pTRXReq);
	LogRawStrA(SPLOGLV_INFO, "ModemV3_lteTRX_Tune Cal:");

	L1_MODEM_RF_V3_LTE_TRX_REQ_T req;
	ZeroMemory((void *)&req, sizeof(req));
	//////////////////////////////////////////////////////////////////////////
	req.CmdHeader.SubCmdCode = CAL_LTE_TRX_REQ;
	req.CmdHeader.SubCmdSize = sizeof(L1_MODEM_RF_V3_LTE_TRX_REQ_T);
	//////////////////////////////////////////////////////////////////////////
	memcpy(&req.bEnable, pTRXReq, sizeof(PC_MODEM_RF_V3_LTE_TRX_REQ_T));
	
	DeclareDiagHeader(hd, DIAG_MODEM_RF_E, ZERO_SUBTYPE);

	CRecvPkgsList recvList;
	recvList.AddCondition(hd);
	SPRESULT res = SendAndRecv(hd, (const void*)&req, sizeof(req), recvList, m_dwTimeOut);  
	if (SP_OK != res)
	{
		return res;
	}

	uint32 recvLen = 0;
	//2nd packet
	if (!UnpackPRT(recvList.GetPackage(0), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: 2nd packet UnpackPRT return False", __FUNCTION__);
		return SP_E_PHONE_INVALID_DATA;
	}

	if (recvLen < sizeof(L1_COMMON_REQ_T)+sizeof(uint32))
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Response data length %d is less than %d", __FUNCTION__, recvLen, sizeof(L1_COMMON_REQ_T));
		return SP_E_PHONE_INVALID_LENGTH;
	}

	uint32 Opstatus = *(m_diagBuff+sizeof(L1_COMMON_REQ_T));

	if (SP_OK != Opstatus)
	{
		LogFmtStrA(SPLOGLV_ERROR, "ModemV3_lteTRX_Tune Cal DUT response OpCode is %d", Opstatus);
		return SP_E_PHONE_INVALID_DATA; 
	}

	return SP_OK;
}

SPRESULT CCaliCmd::ModemV3_LTE_TRX_Tune_RlstQuery(uint16 RssiEn, uint16 PdetEn, uint16 FreqEn, uint16 RssiRlst[4][4], uint16 PdetRlst[4][4], uint16 FreqRlst[4][4])
{
	LogRawStrA(SPLOGLV_INFO, "ModemV3_lteTRX_Tune_RlstQuery Cal:");

	L1_MODEM_RF_V3_LTE_TRX_RSP_T L1;
	ZeroMemory((void* )&L1, sizeof(L1));
	//////////////////////////////////////////////////////////////////////////
	L1.CmdHeader.SubCmdCode    = Convert16(CAL_LTE_TRX_RSP);
	L1.CmdHeader.SubCmdSize    = sizeof(L1_MODEM_RF_V3_LTE_TRX_RSP_T);
	L1.RssiEn = (uint8)RssiEn;
	L1.PdetEn = (uint8)PdetEn;
	L1.FreqEn = (uint8)FreqEn;

	DeclareDiagHeader(hd, DIAG_MODEM_RF_E, ZERO_SUBTYPE);

	CRecvPkgsList recvList;
	recvList.AddCondition(hd);
	SPRESULT res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), recvList, m_dwTimeOut); 

	if (SP_OK != res)
	{
		return res;
	}

	uint32 recvLen = 0;
	//2nd packet
	if (!UnpackPRT(recvList.GetPackage(0), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: 2nd packet UnpackPRT return False", __FUNCTION__);
		return SP_E_PHONE_INVALID_DATA;
	}

	if (recvLen < sizeof(L1_COMMON_REQ_T) + sizeof(uint16)*16*3)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Response data length %d is less than %d", __FUNCTION__, recvLen, sizeof(L1_COMMON_REQ_T)+sizeof(uint16)*16);
		return SP_E_PHONE_INVALID_LENGTH;
	}

	uint32 OpStatus = *(m_diagBuff+sizeof(L1_COMMON_REQ_T));

	if (SP_OK != OpStatus)
	{
		LogFmtStrA(SPLOGLV_ERROR, "ModemV3_lteTRX_Tune_RlstQuery Cal DUT response OpCode is %d", OpStatus);
		return SP_E_PHONE_INVALID_DATA; 
	}

	if (RssiEn)
	{
		memcpy(RssiRlst, m_diagBuff+sizeof(L1_COMMON_REQ_T) + sizeof(uint32), 32);
	}

	if (PdetEn)
	{
		memcpy(PdetRlst, m_diagBuff+sizeof(L1_COMMON_REQ_T) + sizeof(uint32) + 32, 32);
	}

	if(FreqEn)
	{
		memcpy(FreqRlst, m_diagBuff+sizeof(L1_COMMON_REQ_T) + sizeof(uint32) + 64, 32);
	}
	return SP_OK;
}

SPRESULT CCaliCmd::ModemV3_LTE_TRX_Tune_RssiQuery( uint16 RssiRlst[4][4])
{
	LogRawStrA(SPLOGLV_INFO, "ModemV3_LTE_TRX_Tune_RssiQuery Cal:");

	L1_MODEM_RF_V3_LTE_TRX_RSP_T L1;
	ZeroMemory((void* )&L1, sizeof(L1));
	//////////////////////////////////////////////////////////////////////////
	L1.CmdHeader.SubCmdCode    = Convert16(CAL_LTE_TRX_RSP);
	L1.CmdHeader.SubCmdSize    = sizeof(L1_MODEM_RF_V3_LTE_TRX_RSP_T);
	L1.RssiEn = uint8(TRUE);

	DeclareDiagHeader(hd, DIAG_MODEM_RF_E, ZERO_SUBTYPE);

	CRecvPkgsList recvList;
	recvList.AddCondition(hd);
	SPRESULT res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), recvList, m_dwTimeOut); 

	if (SP_OK != res)
	{
		return res;
	}

	uint32 recvLen = 0;
	//2nd packet
	if (!UnpackPRT(recvList.GetPackage(0), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: packet UnpackPRT return False", __FUNCTION__);
		return SP_E_PHONE_INVALID_DATA;
	}

	if (recvLen < sizeof(L1_COMMON_REQ_T) + sizeof(uint16)*16)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Response data length %d is less than %d", __FUNCTION__, recvLen, sizeof(L1_COMMON_REQ_T)+sizeof(uint16)*16);
		return SP_E_PHONE_INVALID_LENGTH;
	}

	uint32 OpStatus = *(m_diagBuff+sizeof(L1_COMMON_REQ_T));

	if (SP_OK != OpStatus)
	{
		LogFmtStrA(SPLOGLV_ERROR, "ModemV3_lteTRX_Tune_RlstQuery Cal DUT response OpCode is %d", OpStatus);
		return SP_E_PHONE_INVALID_DATA; 
	}
	memcpy(RssiRlst, m_diagBuff+sizeof(L1_COMMON_REQ_T) + sizeof(uint32), 32);

	return SP_OK;
}

SPRESULT CCaliCmd::ModemV3_LTE_TRX_Tune_PdetQuery( uint16 PdetRlst[MAX_PC_MODEM_V3_RF_TRX_CHAIN_CNT][4])
{
	LogRawStrA(SPLOGLV_INFO, "ModemV3_LTE_TRX_Tune_PdetQuery Cal:");

	L1_MODEM_RF_V3_LTE_TRX_RSP_T L1;
	ZeroMemory((void* )&L1, sizeof(L1));
	//////////////////////////////////////////////////////////////////////////
	L1.CmdHeader.SubCmdCode    = Convert16(CAL_LTE_TRX_RSP);
	L1.CmdHeader.SubCmdSize    = sizeof(L1_MODEM_RF_V3_LTE_TRX_RSP_T);
	L1.PdetEn = uint8(TRUE);

	DeclareDiagHeader(hd, DIAG_MODEM_RF_E, ZERO_SUBTYPE);

	CRecvPkgsList recvList;
	recvList.AddCondition(hd);
	SPRESULT res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), recvList, m_dwTimeOut); 

	if (SP_OK != res)
	{
		return res;
	}

	uint32 recvLen = 0;
	//2nd packet
	if (!UnpackPRT(recvList.GetPackage(0), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: 2nd packet UnpackPRT return False", __FUNCTION__);
		return SP_E_PHONE_INVALID_DATA;
	}

	if (recvLen < sizeof(L1_MODEM_RF_V3_LTE_TRX_RSP_T))
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: response data length %d is less than %d ", __FUNCTION__, recvLen, sizeof(L1_MODEM_RF_V3_LTE_TRX_RSP_T));
		return SP_E_PHONE_INVALID_DATA;
	}

	uint32 OpStatus = *(m_diagBuff+sizeof(L1_COMMON_REQ_T));

	if (SP_OK != OpStatus)
	{
		LogFmtStrA(SPLOGLV_ERROR, "ModemV3_lteTRX_Tune_RlstQuery Cal DUT response OpCode is %d", OpStatus);
		return SP_E_PHONE_INVALID_DATA; 
	}
		memcpy(PdetRlst, m_diagBuff+sizeof(L1_COMMON_REQ_T) + sizeof(uint32) + 32, 32);
	return SP_OK;
}

SPRESULT CCaliCmd::ModemV3_LTE_RfChain_Query(const PC_MODEM_RF_V3_LTE_RFCHAIN_REQ_T *pRfChainReq, PC_MODEM_RF_V3_LTE_RFCHAIN_RLST_T *pRfChainRlst)
{
	CheckValidPointer(pRfChainReq);
	CheckValidPointer(pRfChainRlst);
	LogRawStrA(SPLOGLV_INFO, "ModemV3_lteRfChain_Query:");

	L1_MODEM_RF_V3_LTE_RFCHAIN_REQ_T L1;
	ZeroMemory((void* )&L1, sizeof(L1));
	//////////////////////////////////////////////////////////////////////////
	L1.CmdHeader.SubCmdCode    = Convert16(CAL_LTE_QUERY_CH_REQ);
	L1.CmdHeader.SubCmdSize    = sizeof(L1_MODEM_RF_V3_LTE_RFCHAIN_REQ_T);
	//////////////////////////////////////////////////////////////////////////
//	memcpy(&L1 + sizeof(L1_COMMON_REQ_T), pRfChainReq, sizeof(L1_MODEM_RF_V3_LTE_RFCHAIN_REQ_T) -sizeof(L1_COMMON_REQ_T));
	L1.BandNum = pRfChainReq->BandNum;
	CopyMemory(L1.BandList, pRfChainReq->BandList, 24);

	DeclareDiagHeader(hd, DIAG_MODEM_RF_E, ZERO_SUBTYPE);

	CRecvPkgsList recvList;
	recvList.AddCondition(hd);
	//recvList.AddCondition(hd);
	SPRESULT res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), recvList, m_dwTimeOut); 
	if (SP_OK != res)
	{
		return res;
	}

	uint32 recvLen = 0;
	//2nd packet
	if (!UnpackPRT(recvList.GetPackage(0), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: 2nd packet UnpackPRT return False", __FUNCTION__);
		return SP_E_PHONE_INVALID_DATA;
	}

	if (recvLen < sizeof(PC_MODEM_RF_V3_LTE_RFCHAIN_RSP_T))
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Response data length %d is less than %d", __FUNCTION__, recvLen, sizeof(PC_MODEM_RF_V3_LTE_RFCHAIN_RSP_T));
		return SP_E_PHONE_INVALID_LENGTH;
	}

	PC_MODEM_RF_V3_LTE_RFCHAIN_RSP_T* pRLT = (PC_MODEM_RF_V3_LTE_RFCHAIN_RSP_T* )(m_diagBuff+sizeof(L1_SUBCMD_HEAD_T));

	if (SP_OK != pRLT->status)
	{
		LogFmtStrA(SPLOGLV_ERROR, "ModemV3_lteRfChain_Query Cal DUT response OpCode is %d", pRLT->status);
		return SP_E_PHONE_INVALID_DATA; 
	}

	CopyMemory(pRfChainRlst, &pRLT->RfChain[0], sizeof(PC_MODEM_RF_V3_LTE_RFCHAIN_RLST_T));

	return SP_OK;
}
SPRESULT CCaliCmd::ModemV4_LTE_RfChain_Query(const PC_MODEM_RF_V4_LTE_CH_REQ_CMD_T* pRfChainReq, PC_MODEM_RF_V4_LTE_CH_RSP_T* pRfChainRlst)
{
	CheckValidPointer(pRfChainReq);
	CheckValidPointer(pRfChainRlst);
	LogRawStrA(SPLOGLV_INFO, "ModemV4_lteRfChain_Query:");

	L1_MODEM_RF_V3_LTE_RFCHAIN_REQ_T L1;
	ZeroMemory((void*)&L1, sizeof(L1));
	//////////////////////////////////////////////////////////////////////////
	L1.CmdHeader.SubCmdCode = Convert16(CAL_LTE_QUERY_CH_V4_REQ);
	L1.CmdHeader.SubCmdSize = sizeof(L1_MODEM_RF_V3_LTE_RFCHAIN_REQ_T);
	//////////////////////////////////////////////////////////////////////////
//	memcpy(&L1 + sizeof(L1_COMMON_REQ_T), pRfChainReq, sizeof(L1_MODEM_RF_V3_LTE_RFCHAIN_REQ_T) -sizeof(L1_COMMON_REQ_T));
	L1.BandNum = pRfChainReq->nBandNum;
	CopyMemory(L1.BandList, pRfChainReq->bandList, 24);

	DeclareDiagHeader(hd, DIAG_MODEM_RF_E, ZERO_SUBTYPE);

	CRecvPkgsList recvList;
	recvList.AddCondition(hd);
	//recvList.AddCondition(hd);
	SPRESULT res = SendAndRecv(hd, (const void*)&L1, sizeof(L1), recvList, m_dwTimeOut);
	if (SP_OK != res)
	{
		return res;
	}

	uint32 recvLen = 0;
	//2nd packet
	if (!UnpackPRT(recvList.GetPackage(0), NULL, (void*)&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: 2nd packet UnpackPRT return False", __FUNCTION__);
		return SP_E_PHONE_INVALID_DATA;
	}

	if (recvLen < sizeof(PC_MODEM_RF_V4_LTE_CH_RSP_T))
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Response data length %d is less than %d", __FUNCTION__, recvLen, sizeof(PC_MODEM_RF_V4_LTE_CH_RSP_T));
		return SP_E_PHONE_INVALID_LENGTH;
	}

	PC_MODEM_RF_V4_LTE_CH_RSP_T* pRLT = (PC_MODEM_RF_V4_LTE_CH_RSP_T*)(m_diagBuff + sizeof(L1_SUBCMD_HEAD_T));

	if (SP_OK != pRLT->status)
	{
		LogFmtStrA(SPLOGLV_ERROR, "ModemV3_lteRfChain_Query Cal DUT response OpCode is %d", pRLT->status);
		return SP_E_PHONE_INVALID_DATA;
	}

	CopyMemory(pRfChainRlst, &pRLT->status, sizeof(PC_MODEM_RF_V4_LTE_CH_RSP_T));

	return SP_OK;
}

SPRESULT CCaliCmd::ModemV4_LTE_Load_PathInfo(const LTE_BAND_E Band, LTE_RF_PATH_INFO_T* pPathInfo)
{
	CheckValidPointer(pPathInfo);
	LogFmtStrA(SPLOGLV_INFO, "%s", __FUNCTION__);

	uint32 recvSize = 0;

	L1_MODEM_RF_V3_LTE_RFCHAIN_REQ_T L1 = { 0 };
	memset(&L1, 0, sizeof(L1));
	L1.CmdHeader.SubCmdCode = Convert16(CAL_LTE_QUERY_CH_V4_REQ);
	L1.CmdHeader.SubCmdSize = sizeof(L1_MODEM_RF_V3_LTE_RFCHAIN_REQ_T);
	L1.BandNum = 1;
	L1.BandList[0] = CLteUtility::m_BandInfo[Band].BandIdent;

	DeclareDiagHeader(hd, DIAG_MODEM_RF_E, ZERO_SUBTYPE);

	CHKRESULT(SendAndRecv(hd, (const void*)& L1, sizeof(L1), hd, (void*)m_diagBuff, sizeof(m_diagBuff), &recvSize, m_dwTimeOut));

	if (recvSize < sizeof(PC_MODEM_RF_V4_LTE_CH_RSP_T))
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: recvSize is too small", __FUNCTION__);
		return SP_E_PHONE_INVALID_LENGTH;
	}

	CHKRESULT(DetermineStatus(CAL_LTE_QUERY_CH_REQ, m_diagBuff, recvSize));
	LogFmtStrA(SPLOGLV_INFO, "%s OK", __FUNCTION__);

	CopyMemory(pPathInfo, ((PC_MODEM_RF_V4_LTE_CH_RSP_T*)((char*)m_diagBuff + sizeof(L1_SUBCMD_HEAD_T)))->RFChannel, sizeof(LTE_RF_PATH_INFO_T));

	return SP_OK;
}

SPRESULT CCaliCmd::ModemV4_LTE_Load_PathInfo_ActualAnt(const LTE_BAND_E Band, LTE_RF_PATH_INFO_NEW_T* pPathInfo)
{
	CheckValidPointer(pPathInfo);
	LogFmtStrA(SPLOGLV_INFO, "%s", __FUNCTION__);

	uint32 recvSize = 0;

	L1_MODEM_RF_V3_LTE_RFCHAIN_REQ_T L1 = { 0 };
	memset(&L1, 0, sizeof(L1));
	L1.CmdHeader.SubCmdCode = Convert16(CAL_LTE_QUERY_CH_V4_REQ_NEW);
	L1.CmdHeader.SubCmdSize = sizeof(L1_MODEM_RF_V3_LTE_RFCHAIN_REQ_T);
	L1.BandNum = 1;
	L1.BandList[0] = CLteUtility::m_BandInfo[Band].BandIdent;

	DeclareDiagHeader(hd, DIAG_MODEM_RF_E, ZERO_SUBTYPE);

	CHKRESULT(SendAndRecv(hd, (const void*)& L1, sizeof(L1), hd, (void*)m_diagBuff, sizeof(m_diagBuff), &recvSize, m_dwTimeOut));

	if (recvSize < sizeof(PC_MODEM_RF_V4_LTE_CH_RSP_NEW_T))
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: recvSize is too small", __FUNCTION__);
		return SP_E_PHONE_INVALID_LENGTH;
	}

	CHKRESULT(DetermineStatus(CAL_LTE_QUERY_CH_V4_REQ_NEW, m_diagBuff, recvSize));
	LogFmtStrA(SPLOGLV_INFO, "%s OK", __FUNCTION__);

	CopyMemory(pPathInfo, ((PC_MODEM_RF_V4_LTE_CH_RSP_NEW_T*)((char*)m_diagBuff + sizeof(L1_SUBCMD_HEAD_T)))->RFChannel, sizeof(LTE_RF_PATH_INFO_NEW_T));

	return SP_OK;
}

SPRESULT CCaliCmd::ModemV3_LTE_NSTActive(BOOL bActive)
{
	LogFmtStrA(SPLOGLV_INFO, "%s", "ModemV3_lteNSTActive");

    SPRESULT res = SP_OK;

	L1_MODEM_RF_V3_LTE_EMPTY_REQ_T L1;
	ZeroMemory((void* )&L1, sizeof(L1));
	L1.CmdHeader.SubCmdSize  = Convert16((uint16)sizeof(L1));

	if (bActive)
	{
		L1.CmdHeader.SubCmdCode  = Convert16(NST_LTE_ACTIVE_REQ);
	}
	else
	{
		L1.CmdHeader.SubCmdCode  = Convert16(NST_LTE_DEACTIVE_REQ);
	}

	DeclareDiagHeader(hd, DIAG_MODEM_RF_E, ZERO_SUBTYPE); 

	CRecvPkgsList recvList;
	recvList.AddCondition(hd);

    res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), recvList, m_dwTimeOut);

    if (SP_OK != res)
    {
        LogFmtStrA(SPLOGLV_ERROR, "ModemV3_LTE_NSTActive Fail!");
    }

    return res;
}

SPRESULT CCaliCmd::ModemV3_LTE_NSTSync(const PC_MODEM_RF_V3_LTE_SYNC_REQ_T* pSyncReq, RF_OP_STATUS_E* pStatus)
{
	CheckValidPointer(pSyncReq);
	CheckValidPointer(pStatus);
	LogFmtStrA(SPLOGLV_INFO, "ModemV3_lteNSTSync: Cell = %d", pSyncReq->CellId);
	LogFmtStrA(SPLOGLV_INFO, "ModemV3_lteNSTSync: arfcn = %d", pSyncReq->Arfcn);
	LogFmtStrA(SPLOGLV_INFO, "ModemV3_lteNSTSync: RB = %d", pSyncReq->RbNum);
	LogFmtStrA(SPLOGLV_INFO, "ModemV3_lteNSTSync: Pos = %d", pSyncReq->RbPos);
	LogFmtStrA(SPLOGLV_INFO, "ModemV3_lteNSTSync: BW = %d", pSyncReq->Bw);
	LogFmtStrA(SPLOGLV_INFO, "ModemV3_lteNSTSync: MCS = %d", pSyncReq->MCS);
	LogFmtStrA(SPLOGLV_INFO, "ModemV3_lteNSTSync: TDD = %d", pSyncReq->TDDFrameConf);
	LogFmtStrA(SPLOGLV_INFO, "ModemV3_lteNSTSync: CellPower = %d", (int16)pSyncReq->CellPwr);
	LogFmtStrA(SPLOGLV_INFO, "ModemV3_lteNSTSync: RTNI = %d", pSyncReq->RTNI);
	LogFmtStrA(SPLOGLV_INFO, "ModemV3_lteNSTSync: RedundancyVer = %d", pSyncReq->RedundancyVer);
	LogFmtStrA(SPLOGLV_INFO, "ModemV3_lteNSTSync: precoding = %d", pSyncReq->PreCodInfo);
	LogFmtStrA(SPLOGLV_INFO, "ModemV3_lteNSTSync: Ant = %d", pSyncReq->Ant);
	LogFmtStrA(SPLOGLV_INFO, "ModemV3_lteNSTSync: RxRfChain = %d", pSyncReq->RxRfChain);
	LogFmtStrA(SPLOGLV_INFO, "ModemV3_lteNSTSync: DuplexMode = %d", pSyncReq->DuplexMode);

	L1_MODEM_RF_V3_LTE_SYNC_REQ_T L1;
	ZeroMemory((void* )&L1,  sizeof(L1));
	L1.CmdHeader.SubCmdCode      = Convert16(NST_LTE_SYNC_REQ);
	L1.CmdHeader.SubCmdSize      = Convert16((uint16)sizeof(L1));

	L1.CellId                  = (uint8)(pSyncReq->CellId);
	L1.Arfcn                   = Convert32(pSyncReq->Arfcn);
	L1.Band                    = Convert32(pSyncReq->Band);
	L1.RbNum                   = (uint8)pSyncReq->RbNum;
	L1.RbPos                   = (uint8)pSyncReq->RbPos;
	L1.Bw                      = (uint8)pSyncReq->Bw;
	L1.MCS                     = (uint8)pSyncReq->MCS;
	L1.TDDFrameConf            = (uint8)pSyncReq->TDDFrameConf;
	L1.CellPwr                 = Convert16((uint16)(pSyncReq->CellPwr*10));
	L1.RTNI                    = Convert16(pSyncReq->RTNI);
	L1.RedundancyVer           = Convert16(pSyncReq->RedundancyVer);
	L1.PreCodInfo              = Convert16(pSyncReq->PreCodInfo);
	L1.Ant                     = (uint8)(pSyncReq->Ant);
	L1.RxRfChain			   = (uint8)pSyncReq->RxRfChain;
	L1.DuplexMode              = (Duplexingmodes_e)(pSyncReq->DuplexMode);
	DeclareDiagHeader(hd, DIAG_MODEM_RF_E, ZERO_SUBTYPE); 

	CRecvPkgsList recvList;
	recvList.AddCondition(hd);
	SPRESULT res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), recvList, m_dwTimeOut); 
	if (SP_OK != res)
	{
		return res;
	}
	uint32 recvLen = 0;
	if (!UnpackPRT(recvList.GetPackage(0), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
	{
		LogFmtStrA(SPLOGLV_ERROR, "Unpack data failed");
		return SP_E_PHONE_INVALID_DATA; 
	}

	if (recvLen >= sizeof(L1_MODEM_RF_V3_LTE_SYNC_RSP_T))
	{
		L1_MODEM_RF_V3_LTE_SYNC_RSP_T* pRLT = (L1_MODEM_RF_V3_LTE_SYNC_RSP_T* )m_diagBuff;
		uint16 uType = Convert16(pRLT->CmdHeader.SubCmdCode);
		if (NST_LTE_SYNC_REQ != uType)
		{
			LogFmtStrA(SPLOGLV_ERROR, "Invalid response command ID: 0x%X <> 0x%X", uType, NST_LTE_SYNC_REQ);
			return SP_E_PHONE_INVALID_DATA;       
		}

		*pStatus = static_cast<RF_OP_STATUS_E>(Convert32(pRLT->status));
		LogFmtStrA(SPLOGLV_INFO, "SYNC status = %d", *pStatus);

		return SP_OK;
	}

	LogRawStrA(SPLOGLV_ERROR, "Invalid response received!");
	return SP_E_PHONE_INVALID_DATA;
}

SPRESULT CCaliCmd::ModemV3_LTE_Infor_Query( PC_MODEM_RF_V3_LTE_BANDLIST_RSP_T *pBandListRlst, uint32 nBandCntSize )
{
	LogRawStrA(SPLOGLV_INFO, "ModemV3_LTE_BandList_Query Cal:");
	CheckValidPointer(pBandListRlst);
	

	L1_MODEM_RF_V3_LTE_INFOR_REQ_T L1;
	ZeroMemory((void* )&L1, sizeof(L1));
	L1.CmdHeader.SubCmdCode    = Convert16(CAL_LTE_INFOR_QUERY_REQ);
	L1.CmdHeader.SubCmdSize    = sizeof(L1_MODEM_RF_V3_LTE_INFOR_REQ_T);

	L1.CmdType = INFOR_QUERY_SUPPORT_BAND_LIST;

	DeclareDiagHeader(hd, DIAG_MODEM_RF_E, ZERO_SUBTYPE);

	CRecvPkgsList recvList;
	recvList.AddCondition(hd);
	SPRESULT res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), recvList, m_dwTimeOut); 
	if (SP_OK != res)
	{
		return res;
	}

	uint32 recvLen = 0;
	//2nd packet
	if (!UnpackPRT(recvList.GetPackage(0), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: 2nd packet UnpackPRT return False", __FUNCTION__);
		return SP_E_PHONE_INVALID_DATA;
	}

	L1_MODEM_RF_V3_LTE_INFOR_RSP_T *pRsp;

	pRsp = (L1_MODEM_RF_V3_LTE_INFOR_RSP_T*)m_diagBuff;
	if (recvLen < sizeof(L1_MODEM_RF_V3_LTE_INFOR_RSP_T))
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Response data length %d is less than %d", __FUNCTION__, recvLen, sizeof(L1_SUBCMD_HEAD_T) + sizeof(uint32), sizeof(uint32));
		return SP_E_PHONE_INVALID_LENGTH;
	}

	//uint32 status = SP_OK;
	//memcpy(&status, m_diagBuff+sizeof(L1_SUBCMD_HEAD_T), sizeof(uint32));
	if (SP_OK != pRsp->status)
	{
		LogFmtStrA(SPLOGLV_ERROR, "ModemV3_LTE_Infor_Query DUT response OpCode is %d",pRsp->status);
		return SP_E_PHONE_INVALID_DATA; 
	}

	uint32 nBandCnt = pRsp->nBandCnt;

	if (nBandCntSize < nBandCnt )
	{
		LogFmtStrA(SPLOGLV_ERROR, "The input parameter space length is not enough, required size (byte) is %d, Input size (byte) is %d",nBandCnt * (sizeof(uint32)), nBandCntSize);
		return SP_E_PHONE_INVALID_DATA; 
	}

	pBandListRlst->nBandCnt = nBandCnt;
	memcpy(pBandListRlst->pBand, m_diagBuff+sizeof(L1_MODEM_RF_V3_LTE_INFOR_RSP_T), nBandCnt );
	return SP_OK;
}

SPRESULT CCaliCmd::ModemV3_LTE_NST_Start(const PC_MODEM_RF_V3_LTE_NST_CONFIG_T* req)
{
	CheckValidPointer(req);
	LogFmtStrA(SPLOGLV_INFO, "LTE NST Start modemV3: Arfcn Count = %d, Rx Arfcn Count = %d, Ant = %d", req->arfcn_num, req->RxArfcnCount, req->Ant);
	L1_MODEM_RF_V3_LTE_NST_CONFIG_REQ_T L1;
	ZeroMemory((void* )&L1, sizeof(L1));
	L1.CmdHeader.SubCmdCode   = Convert16(NST_LTE_START_TEST_REQ);
	L1.CmdHeader.SubCmdSize   = Convert16((uint16)sizeof(L1));
	L1.arfcn_num    = Convert16(req->arfcn_num);
	L1.Ant          = Convert16(req->Ant);
	L1.Rx_arfcn_count = Convert32(req->RxArfcnCount);

	for (uint16 i = 0; i < req->arfcn_num; i++)
	{
		L1.arfcn[i].arfcn      = Convert32(req->arfcn[i].arfcn);
		L1.arfcn[i].Band      = Convert32(req->arfcn[i].Band);
		L1.arfcn[i].frame_num  = Convert16(req->arfcn[i].frame_num);
		if (req->arfcn[i].rx.frame_num > req->arfcn[i].frame_num)
		{
			LogFmtStrA(SPLOGLV_ERROR, "Arfcn[%2d] = %d, Invalid rx frame count, %d <= %d", i, req->arfcn[i].arfcn, req->arfcn[i].rx.frame_num, req->arfcn[i].frame_num);
			return SP_E_PHONE_INVALID_PARAMETER;
		}
		L1.arfcn[i].rx.frame_num   = (uint8)req->arfcn[i].rx.frame_num;
		L1.arfcn[i].BW = (uint8)req->arfcn[i].BW;
		L1.arfcn[i].rx.CellPower   = Convert16((uint16)(req->arfcn[i].rx.CellPower*10));
		int   total_tx_frames = 0;
		L1.arfcn[i].tx_count  = req->arfcn[i].tx_count;
		for (uint8 j = 0; j < req->arfcn[i].tx_count; j++)
		{
			total_tx_frames += req->arfcn[i].tx[j].frame_num;
			L1.arfcn[i].tx[j].frame_num        = (uint8)req->arfcn[i].tx[j].frame_num;
			L1.arfcn[i].tx[j].RB_num           = (uint8)req->arfcn[i].tx[j].RB_num;
			L1.arfcn[i].tx[j].RB_pos           = (uint8)req->arfcn[i].tx[j].RB_pos;
			L1.arfcn[i].tx[j].TPC              = (uint8)req->arfcn[i].tx[j].TPC;
			L1.arfcn[i].tx[j].closeloop_power  = (int8 )req->arfcn[i].tx[j].closeloop_power;
			L1.arfcn[i].tx[j].MCS              = (uint8)req->arfcn[i].tx[j].MCS;
			L1.arfcn[i].tx[j].reserved[0]      = (uint8)req->arfcn[i].tx[j].reserved[0];
			L1.arfcn[i].tx[j].reserved[1]      = (uint8)req->arfcn[i].tx[j].reserved[1];
		}
		if (total_tx_frames > req->arfcn[i].frame_num)
		{
			LogFmtStrA(SPLOGLV_ERROR, "Arfcn[%2d] = %d, Invalid total tx frame count, %d <= %d", i, req->arfcn[i].arfcn, total_tx_frames, req->arfcn[i].frame_num);
			return SP_E_PHONE_INVALID_PARAMETER;
		}
	}
	DeclareDiagHeader(hd, DIAG_MODEM_RF_E, ZERO_SUBTYPE); 
	return SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, NULL, 0, NULL, m_dwTimeOut);
}

SPRESULT CCaliCmd::ModemV3_LTE_NST_GetBLER(PC_MODEM_RF_V3_NST_SEBLER_T* BLER)
{
	CheckValidPointer(BLER);

	LogFmtStrA(SPLOGLV_INFO, "ModemV3_LTE_NST_GetBLER:");
	L1_MODEM_RF_V3_LTE_EMPTY_REQ_T L1;
	ZeroMemory((void* )&L1, sizeof(L1));

	L1.CmdHeader.SubCmdCode = Convert16(NST_LTE_GET_BLER_REQ);
	L1.CmdHeader.SubCmdSize = Convert16((uint16)sizeof(L1));

	DeclareDiagHeader(hd, DIAG_MODEM_RF_E, ZERO_SUBTYPE); 

	CRecvPkgsList recvList;
	recvList.AddCondition(hd);
	SPRESULT res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), recvList, m_dwTimeOut); 
	if (SP_OK != res)
	{
		return res;
	}

	uint32 recvLen = 0;
	if (!UnpackPRT(recvList.GetPackage(0), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
	{
		LogFmtStrA(SPLOGLV_ERROR, "Invalid response command ID: 0x%X",  NST_LTE_GET_BLER_REQ);
		return SP_E_PHONE_INVALID_DATA; 
	}

	if (recvLen >= sizeof(L1_SUBCMD_HEAD_T) + sizeof(uint16)*(2 + BLER->arfcn_num))
	{
		L1_MODEM_RF_V3_NST_SEBLER_T* pRLT = (L1_MODEM_RF_V3_NST_SEBLER_T* )m_diagBuff;
		uint16 uType = Convert16(pRLT->CmdHeader.SubCmdCode);
		if (NST_LTE_GET_BLER_REQ != uType)
		{
			LogFmtStrA(SPLOGLV_ERROR, "Invalid response command ID: 0x%X <> 0x%X", uType, NST_LTE_GET_BLER_REQ);
			return SP_E_PHONE_INVALID_DATA;       
		}

		BLER->status = Convert16(pRLT->status);

		LogFmtStrA(SPLOGLV_INFO, "BLER status = %d!", BLER->status);


		BLER->arfcn_num = Convert16(pRLT->arfcn_num);
		if (BLER->arfcn_num > MAX_PC_LTE_NST_ARFCN_NUM)
		{
			LogFmtStrA(SPLOGLV_INFO, "Invalid BLER Arfcn count = %d!", BLER->arfcn_num);
			return SP_E_PHONE_INVALID_DATA;
		}

		for (uint16 i = 0; i <BLER->arfcn_num; i++)
		{
			BLER->BLER10[i] = Convert16(pRLT->BLER10[i]);
			LogFmtStrA(SPLOGLV_INFO, "BLER Arfcn[%2d] = %d", i+1, BLER->BLER10[i]);
		}

		return SP_OK;
	}

	LogRawStrA(SPLOGLV_ERROR, "Invalid response received!");
	return SP_E_PHONE_INVALID_DATA;
}

SPRESULT CCaliCmd::ModemV3_LTE_NST_GetRSSI(PC_MODEM_RF_V3_NST_RSSI_T* RSSI)
{
	CheckValidPointer(RSSI);

	LogRawStrA(SPLOGLV_INFO, "ModemV3_LTE_NST_GetRSSI:");
	L1_MODEM_RF_V3_LTE_EMPTY_REQ_T L1;

	ZeroMemory((void* )&L1, sizeof(L1));

	L1.CmdHeader.SubCmdCode = Convert16(NST_LTE_GET_RSRP_REQ);
	L1.CmdHeader.SubCmdSize = Convert16((uint16)sizeof(L1));

	DeclareDiagHeader(hd, DIAG_MODEM_RF_E, ZERO_SUBTYPE); 

	CRecvPkgsList recvList;
	recvList.AddCondition(hd);
	SPRESULT res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), recvList, m_dwTimeOut); 
	if (SP_OK != res)
	{
		return res;
	}
	uint32 recvLen = 0;
	if (!UnpackPRT(recvList.GetPackage(0), NULL, (void* )&m_diagBuff[0], sizeof(m_diagBuff), &recvLen))
	{
		LogFmtStrA(SPLOGLV_ERROR, "Invalid response command ID: 0x%X", NST_LTE_GET_RSRP_REQ);
		return SP_E_PHONE_INVALID_DATA; 
	}

	if (recvLen >= sizeof(L1_SUBCMD_HEAD_T) +sizeof(uint16)*(2 + RSSI->arfcn_num))
	{
		L1_MODEM_RF_V3_NST_GET_RSSI_RLT_T* pRLT = (L1_MODEM_RF_V3_NST_GET_RSSI_RLT_T* )m_diagBuff;
		uint16 uType = Convert16(pRLT->CmdHeader.SubCmdCode);
		if (NST_LTE_GET_RSRP_REQ != uType)
		{
			LogFmtStrA(SPLOGLV_ERROR, "Invalid response command ID: 0x%X <> 0x%X", uType, NST_LTE_GET_RSRP_REQ);
			return SP_E_PHONE_INVALID_DATA;       
		}

		RSSI->status = Convert16(pRLT->status);

		LogFmtStrA(SPLOGLV_INFO, "RSSI status = %d!", RSSI->status);

		RSSI->arfcn_num = Convert16(pRLT->arfcn_num);
		if (RSSI->arfcn_num > MAX_PC_LTE_NST_ARFCN_NUM)
		{
			LogFmtStrA(SPLOGLV_INFO, "Invalid RSSI Arfcn count = %d!", RSSI->arfcn_num);
			return SP_E_PHONE_INVALID_DATA;
		}

		for (uint16 i = 0; i < RSSI->arfcn_num; i++)
		{
			RSSI->RSSI[i] = Convert16(pRLT->RSSI[i]);
			LogFmtStrA(SPLOGLV_INFO, "RSSI Arfcn[%2d] = %d", i+1, RSSI->RSSI[i]);
		}

		return SP_OK;
	}

	LogRawStrA(SPLOGLV_ERROR, "Invalid response received!");
	return SP_E_PHONE_INVALID_DATA;
}


SPRESULT CCaliCmd::ModemV3_LTE_LoadCalFlag(int* pBandCnt, LTE_BAND_FLAG* pFlagInfo)
{
	if (NULL == pFlagInfo || NULL == pBandCnt)
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid parameters!", __FUNCTION__);
		return SP_E_PHONE_INVALID_PARAMETER;
	}
	NvData_LTE_BAND_SUPPORT_INFO    BandSupportInfo[MAX_LTE_BAND_NUM];
	ZeroMemory(&BandSupportInfo, sizeof(NvData_LTE_BAND_SUPPORT_INFO)*MAX_LTE_BAND_NUM);
	uint32 CalibrationFlag [MAX_LTE_BAND_NUM] = {0};

	PC_MODEM_RF_V3_DATA_REQ_CMD_T NvReq;
	ZeroMemory(&NvReq, sizeof(PC_MODEM_RF_V3_DATA_REQ_CMD_T));
	NvReq.eNvType = NVM_LTE_DOWNLOAD_BAND_INFOR;
	PC_MODEM_RF_V3_DATA_PARAM_T NvRlst ;
	ZeroMemory(&NvRlst, sizeof(PC_MODEM_RF_V3_DATA_PARAM_T));
	CHKRESULT(ModemV3_Nv_Read(&NvReq,&NvRlst));
	memcpy(&BandSupportInfo[0], &NvRlst.nData[0], sizeof(NvData_LTE_BAND_SUPPORT_INFO)*MAX_LTE_BAND_NUM);

	PC_MODEM_RF_V3_LTE_BAND_LIST_INFOR BandListEnFlag;
	PC_MODEM_RF_V3_LTE_BANDLIST_RSP_T stBandListRsp;
	ZeroMemory(&stBandListRsp, sizeof(PC_MODEM_RF_V3_LTE_BANDLIST_RSP_T));

	uint8 pBuff[MAX_LTE_BAND_NUM] = {0};
	stBandListRsp.pBand = pBuff;
	CHKRESULT(ModemV3_LTE_Infor_Query(&stBandListRsp, MAX_LTE_BAND_NUM));
	BandListEnFlag.BandCount = stBandListRsp.nBandCnt;
	memcpy(&BandListEnFlag.Band[0], stBandListRsp.pBand, BandListEnFlag.BandCount);

	ZeroMemory(&NvReq, sizeof(PC_MODEM_RF_V3_DATA_REQ_CMD_T));
	ZeroMemory(&NvRlst, sizeof(PC_MODEM_RF_V3_DATA_PARAM_T));
	NvReq.eNvType = NVM_LTE_CAL_DATA_INFOMATION;
	CHKRESULT(ModemV3_Nv_Read(&NvReq,&NvRlst));	
	memcpy(&CalibrationFlag[0], &NvRlst.nData[0], sizeof(NvData_Reserved)*MAX_LTE_BAND_NUM);

	*pBandCnt = 0;

	for (uint32 j = 0; j < BandListEnFlag.BandCount; j++)
	{
		for(int i = 0; i < MAX_LTE_BAND_NUM ;i++)
		{
			if(BandSupportInfo[i].band_num == BandListEnFlag.Band[j])
			{
				for( int m = 0;m < MAX_LTE_BAND;m++)
				{
					if ( CLteUtility::m_BandInfo[m].BandIdent == (LTE_BAND_IDENT_E)BandSupportInfo[i].band_num )
					{
						LogFmtStrA(SPLOGLV_INFO, "LTE Band: %d,  Flag: 0x%X", BandSupportInfo[i].band_num, CalibrationFlag[i]);

						if ( strlen(CLteUtility::m_BandInfo[m].NameA) >= sizeof(pFlagInfo[*pBandCnt].szBand) )
						{
							LogRawStrA(SPLOGLV_ERROR, "Invalid NameA!");
							return SP_E_PHONE_INVALID_PARAMETER;
						}
						else
						{
							memcpy(&pFlagInfo[*pBandCnt].szBand[0], CLteUtility::m_BandInfo[m].NameA, strlen(CLteUtility::m_BandInfo[m].NameA));
						}
						pFlagInfo[*pBandCnt].nFlag = CalibrationFlag[i];
						(*pBandCnt)++;

						break;
					}
				}
			}
		}
	}

	return SP_OK;
}


SPRESULT CCaliCmd::ModemV3_LTE_COMMOM_GetVersion(LTE_VERSION_T* pVersion)
{
	uint32 recvSize = 0;

	LogFmtStrA(SPLOGLV_INFO, "%s", __FUNCTION__);

	L1_RFB_COM_QUERY_CAL_IF_VERSION_REQ_T L1;
	ZeroMemory((void* )&L1, sizeof(L1));
	L1.head.SubCmdCode  = RF_COM_QUERY_CAL_IF_VERSION_REQ;
	L1.head.SubCmdSize  = (uint16)sizeof(L1);

	L1.rf_mode = RF_MODE_LTE;

	DeclareDiagHeader(hd, DIAG_MODEM_RF_E, ZERO_SUBTYPE);

	CHKRESULT(SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvSize, m_dwTimeOut));

	if (recvSize < sizeof(L1_RFB_COM_QUERY_CAL_IF_VERSION_RLT_T))
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: recvSize is too small", __FUNCTION__);
		return SP_E_PHONE_INVALID_LENGTH;
	}

	CHKRESULT(DetermineStatus(RF_COM_QUERY_CAL_IF_VERSION_REQ, m_diagBuff, recvSize));

	L1_RFB_COM_QUERY_CAL_IF_VERSION_RLT_T* pRLT = (L1_RFB_COM_QUERY_CAL_IF_VERSION_RLT_T*)m_diagBuff;
	memcpy(pVersion, pRLT->if_version, sizeof(LTE_VERSION_T));

	LogFmtStrA(SPLOGLV_INFO, "%s OK", __FUNCTION__);

	return SP_OK;
}

SPRESULT CCaliCmd::ModemV3_LTE_MULTION_SWITCH(const LTE_MULTION_T* pMultionSwitch)
{
	CheckValidPointer(pMultionSwitch);

	LogFmtStrA(SPLOGLV_INFO, "%s", __FUNCTION__);

	L1_MODEM_RF_V3_LTE_MULTION_SWITCH_REQ_T req;
	ZeroMemory((void*)&req, sizeof(req));

	req.CmdHeader.SubCmdCode = Convert16((uint16)CAL_LTE_COM_CMD_REQ);
	req.CmdHeader.SubCmdSize = Convert16((uint16)sizeof(req));
	req.type = Convert32(pMultionSwitch->type);
	for (int i = 0; i < 5; i++)
	{
		req.params[i] = Convert32(pMultionSwitch->params[i]);
	}

	DeclareDiagHeader(hd, DIAG_MODEM_RF_E, ZERO_SUBTYPE);
	CRecvPkgsList recvList;
	recvList.AddCondition(hd);
	return SendAndRecv(hd, (const void*)&req, sizeof(req), recvList, m_dwTimeOut);
}