﻿#include "StdAfx.h"
#include "DevMode.h"
#include "AppSettings.h"
#include "LteUtility.h"
#include "AppVer.h"
#include "RunMode.h"
#include <regex>
#include "Security_Auth.h"
//////////////////////////////////////////////////////////////////////////
CDevMode::CDevMode(void)
    : m_bWorking(FALSE)
    , m_nCampSN(0xFFFF0000)
    , m_dwPlugPort(0)
    , m_errModeCode(SP_OK)
    , m_bQuitSignal(FALSE)
    , m_nConnState(USB_Org_CONN)
{
	bAuth = 0;
    ZeroMemory(m_szPortLocationPath, sizeof(m_szPortLocationPath));
}

CDevMode::~CDevMode( void )
{
    if ( NULL != m_hMonitorLogPort )
    {
        CloseHandle( m_hMonitorLogPort );
    }
}

wstring CDevMode::SubDevicePath(wstring strLocationPath)
{
    wregex r(L"#USB\\(\\d+\\)#USB");

    if (regex_search(strLocationPath, r))
    {
        return strLocationPath.substr(0, strLocationPath.rfind(L"#USB"));
    }

    return  strLocationPath;
}

void CDevMode::OnDevChange(INT nEvent, LPCVOID lpData)
{
    if (NULL == lpData)
    {
        return;
    }

    SPDBT_INFO epi = *((SPDBT_INFO*)lpData);
    switch (nEvent)
    {
    case SPDBT_DEVICEARRIVAL:
    {
        LogFmtStrW( SPLOGLV_VERBOSE, L"PORT %d is plugged. %s", epi.nPortNum, epi.szDescription );
        LogFmtStrW( SPLOGLV_VERBOSE, L"location:%s", epi.szLocationPath );
        LogFmtStrW( SPLOGLV_VERBOSE, L"ConnState:%d", m_nConnState );
        LogFmtStrW( SPLOGLV_VERBOSE, L"%d, %d", epi.bGetUsbInfo, wcslen( m_szPortLocationPath ) != 0 );
        
        //对比时的问题
        BOOL bSamePath = FALSE;
        if ( wcslen( m_szPortLocationPath ) > 0 )
        {
            LogFmtStrW( SPLOGLV_VERBOSE, L"Location: 1st: %s; 2nd: %s", m_szPortLocationPath, epi.szLocationPath );
            bSamePath = ( 0 == wcsncmp( epi.szLocationPath, m_szPortLocationPath, wcslen( m_szPortLocationPath ) ) ) ? TRUE : FALSE;
        }
        
        if ( ( m_ModeInfo.nExpPort <= 0 )
             || ( m_ModeInfo.nExpPort > 0 && epi.nPortNum == ( unsigned int )m_ModeInfo.nExpPort )
             || ( m_ModeInfo.nExpPort > 0 && epi.bGetUsbInfo && bSamePath ) )
        {
            if ( CAppSettings::GetInstance().IsValidDriver( epi.szDescription ) )
            {
                m_dwPlugPort = epi.nPortNum;
                m_DBT = epi;
                SetEvent( m_hBootUpEvent );
                
                IDevMonitor* lpMonitor = GetMonitor();
                if ( NULL != lpMonitor )
                {
                    lpMonitor->OnDevChange( nEvent, ( LPCVOID )& epi );
                }
            }
            else if ( CAppSettings::GetInstance().IsValidLogDriver( epi.szDescription ) && bSamePath )
            {
                // Bug 2368463
                // When the LogPort is obtained, notify the end of the secondary enumeration process;
                SetEvent( m_hMonitorLogPort );
                m_nLogPort = epi.nPortNum;
                StartCaptureLog();
            }
        }
    }
    break;

    case SPDBT_DEVICEREMOVE:
    {
        LogFmtStrA(SPLOGLV_VERBOSE, "PORT %d is removed.", epi.nPortNum);

        if (m_ca.ChannelType == CHANNEL_TYPE_COM && epi.nPortNum == m_ca.Com.dwPortNum)
        {
            // Current port is removed
            if (NULL != m_lpAssert && m_lpAssert->IsAsserted())
            {
                m_lpAssert->TerminalDump(DUMP_VCOM_PLUGGED_OUT);
            }

            Close();
        }

        if (epi.nPortNum == m_ca.Com.dwPortNum)
        {
            IDevMonitor* lpMonitor = GetMonitor();
            if (NULL != lpMonitor)
            {
                lpMonitor->OnDevChange(nEvent, (LPCVOID)& epi);
            }
        }

        //if (wcsstr(epi.szLocationPath, m_szPortLocationPath) != NULL)
        if (epi.nPortNum == m_ca.Com.dwPortNum)
        {
            Close();
        }
    }
    break;

    default:
        break;
    }
}

uint32 CDevMode::ConvertCampSN(RM_MODE_ENUM eMode, uint32 wsn)
{
    uint32 sn = 0;
    if (IsPostMode(eMode))
    {
        sn = wsn;

        DWORD dwLowLvEndian = SP_LITTLE_ENDIAN;
        GetProperty(SP_ATTR_ENDIAN, 0, (LPVOID)& dwLowLvEndian);

        if (SP_LITTLE_ENDIAN == dwLowLvEndian)
        {
            sn = MAKELONG(MAKEWORD(HIBYTE(HIWORD(wsn)), LOBYTE(HIWORD(wsn))), \
                MAKEWORD(HIBYTE(LOWORD(wsn)), LOBYTE(LOWORD(wsn))));
        }
    }
    else
    {
        sn = 0;
    }

    return sn;
}

void CDevMode::run(void)
{
    CFnLog log(GetISpLogObject(), _T("DevMode Run"));

    DIAG_HEADER hd;
    hd.type = RM_COMMAND_T;
    hd.subtype = (unsigned char)(RM_U0_AS_DIAG | m_ModeInfo.eMode);
    hd.sn = ConvertCampSN(m_ModeInfo.eMode, m_nCampSN);
    hd.len = 0;
    DIAG_HEADER rd = hd;


    m_nConnState = USB_Org_CONN;
    BOOL  bUSB = m_ModeInfo.bUsbPort;

    BOOL  bModeOK = FALSE;
    SPRESULT  res = SP_OK;
    CSPTimer timer;

    // Default should be TimeOut code
    m_errModeCode = SP_E_TIMEOUT;
    do
    {
        if (!m_bWorking)
        {
            /// User Stopped
            LogFmtStrA(SPLOGLV_WARN, "%s abort.", __FUNCTION__);
            m_errModeCode = SP_E_USER_ABORT;
            break;
        }

        if (WAIT_OBJECT_0 == WaitForSingleObject(m_hBootUpEvent, 100 /* 100ms decrease CPU loading and system consume*/))
        {
            ResetEvent(m_hBootUpEvent);

            if (!bUSB)
            {
                /// UART
                if (SP_OK == SendCmd(hd, NULL, 0))
                {
                    bModeOK = TRUE;
                    break;
                }
            }
            else
            {
                DWORD dwPlugPort = m_dwPlugPort;
                m_nConnState++;

                if (USB_1st_CONN == m_nConnState)
                {
                    m_ca.ChannelType = CHANNEL_TYPE_COM;
                    m_ca.Com.dwPortNum = dwPlugPort;
                    m_ca.Com.dwBaudRate = 115200;
                    ZeroMemory(m_szPortLocationPath, sizeof(m_szPortLocationPath));
                    if (m_DBT.bGetUsbInfo)
                    {
                        wcscpy_s(m_szPortLocationPath, m_DBT.szLocationPath);
                    }

                    CFnLog log(GetISpLogObject(), _T("1st Connection"), _T("( Port:%d; Location:%s )"), dwPlugPort, m_szPortLocationPath);

                    if (RM_NORMAL_MODE == m_ModeInfo.eMode)
                    {
                        continue;
                    }
                    if (SP_OK != Open(&m_ca))
                    {
                        m_nConnState = USB_Org_CONN;
                        continue;
                    }

                    // Bug 1013478: Because uboot will not enum USB until kernel enum, so here we increase the timeout from 3s to 5s. 
                    res = SendAndRecv(hd, NULL, 0, rd, NULL, 0, NULL, TIMEOUT_5S);
                    if (res != SP_OK) // if fail ,retry.
                    {
                        res = SendAndRecv(hd, NULL, 0, rd, NULL, 0, NULL, TIMEOUT_3S);
                    }

					if ((bAuth == 1) || ((bAuth == -1) && (m_bAuthMode == 1)))
					{
						LogFmtStrA(SPLOGLV_INFO, "Start Authentication");
						res = Authentication();
					}
					else
					{
						LogFmtStrA(SPLOGLV_INFO, "Not Need Authentication");
					}

					
                    // Bug 1013478 
                    if (SP_OK == res && RM_SPECIAL_SUBTYPE == rd.subtype)
                    {
                        // If subtype is RM_SPECIAL_SUBTYPE, that means DUT is already under specified test mode, 
                        // We just need to query the current mode 
                        // and check whether the current mode is equal to the target mode or not
                        // If not equal, quit the mode thread immediately.
                        INT nCurrMode = -1;
                        m_errModeCode = QueryCurrRunMode(nCurrMode);
                        if (SP_OK != m_errModeCode)
                        {
                            break;
                        }

                        if (m_ModeInfo.eMode == nCurrMode)
                        {
                            // OK, current mode is exact the target mode
                            bModeOK = TRUE;
                            LogFmtStrA(SPLOGLV_INFO, "Current mode is already %d", nCurrMode);
                            break;
                        }
                        else
                        {
                            m_errModeCode = SP_E_PHONE_INCORRECT_RUN_MODE;
                            LogFmtStrA(SPLOGLV_ERROR, "Current mode %d is not %d", nCurrMode, m_ModeInfo.eMode);
                            break;
                        }
                    }

                    if ((SP_OK == res) && (RM_CALIBR_NV_ACCESS_MODE == m_ModeInfo.eMode))
                    {
                        // 快速写号模式只枚举一次
                        bModeOK = TRUE;
                        break;
                    }

                    // Uboot mode security [6/25/2017 Jian.Zhong]
                    if ((SP_OK == res) && (RM_UBOOT_CALIBRATION_MODE == m_ModeInfo.eMode))
                    {
                        res = apSetUbootSecureBoot();
                        if (SP_OK != res)
                        {
                            m_nConnState = USB_Org_CONN;
                            LogFmtStrA(SPLOGLV_ERROR, "Set Uboot Secure boot failed.");
                        }
                        else
                        {
                            // Ð´ÍêUboot Secure bootÖ®ºó£¬Ö±½Ó½øÈëÐ£×¼Ä£Ê½ [6/25/2017 Jian.Zhong]
                            res = apExitProgramKey(RM_CALIBRATION_MODE);
                            if (SP_OK != res)
                            {
                                LogFmtStrA(SPLOGLV_ERROR, "Exit program key failed.");
                            }
                            else
                            {
                                LogFmtStrA(SPLOGLV_INFO, "Exit program key Successfully.");
                            }
                        }
                    }

                    Close();

                    if (res != SP_OK)
                    {
                        m_nConnState = USB_Org_CONN;
                        continue;
                    }

                    timer.Reset(); // Bug 1129917
                }
                else if (USB_2nd_CONN == m_nConnState)
                {
                    /// ZeroMemory(m_szPortLocationPath, sizeof(m_szPortLocationPath));
                    /// 2nd USB plugged 
                    BOOL bCompare = FALSE;
                    CAppSettings::GetInstance().GetValue(CAppSettings::ComparePort, (LPVOID)& bCompare);
                    if (!bCompare)
                    {
                        m_ca.Com.dwPortNum = dwPlugPort;
                    }
                    else
                    {
                        if (dwPlugPort != m_ca.Com.dwPortNum)
                        {
                            /// This is not our waiting port, Keep USB_1st_CONN state and waiting the specified port.
                            LogFmtStrA(SPLOGLV_INFO, "Port = %d is not the waiting port. %d", dwPlugPort, m_ca.Com.dwPortNum);
                            m_nConnState = USB_1st_CONN;
                            continue;
                        }
                    }


                    CFnLog log(GetISpLogObject(), _T("2nd Connection"), _T("( Port:%d )"), dwPlugPort);

                    if (SP_OK == Open(&m_ca))
                    {
                        bModeOK = TRUE;

                        //  20190314 JXP: Move VCOM driver verify to UI because customer will use their driver.
                        //  CheckSprdVcomDriver();

                        if (RM_AUTOTEST_MODE == m_ModeInfo.eMode)
                        {
                            BOOL bActiveDiagChannel = FALSE;
                            CAppSettings::GetInstance().GetValue(CAppSettings::ModemToPC, (LPVOID)& bActiveDiagChannel);
                            if (bActiveDiagChannel)
                            {
                                // AT+SPATCPLOG=type?
                                // AT+SPATCPLOG=type, dest
                                // type: 
                                // 1: modem
                                // 2: wcn
                                // dest:
                                // 1: log输出到pc， 通过engpc转到pc
                                // 2: log 输出到sd卡， slogmodem去去读log并存到sd卡。
                                // 0: 关闭log
                                // 不能确保每个软件版本都支持，因此不判定返回值
                                uint8 u8buf[64] = { 0 };
                                SendATCommand("AT+SPATCPLOG=1,1", TRUE, u8buf, sizeof(u8buf), NULL, TIMEOUT_3S);
                            }
                        }
                        break;
                    }
                    else
                    {
                        m_nConnState = USB_Org_CONN;
                        continue;
                    }
                }
            }
        }
        /*
        // Move Sleep to WaitForSingleObject(m_hBootUpEvent, 100) to decrease the CPU loading and system consume.
                else
                {
                    Sleep(20);
                }
        */

    } while (!bModeOK && (INFINITE == m_ModeInfo.dwTimeOut || !timer.IsTimeOut(m_ModeInfo.dwTimeOut)));

    if (bModeOK || m_bQuitSignal)
    {
        if (bModeOK)
        {
            m_errModeCode = SP_OK;
        }

        SetEvent(m_ModeInfo.hMonitor);
    }

    m_bWorking = FALSE;
    //m_nConnState = USB_Org_CONN;
    // Bug 2326559 
    // Because the Log Port need to be obtained through the Loaction information, 
    // the Loaction information cannot be cleared immediately after obtaining the Diag Port.
    if ( m_bLogUsingOtherPort )
    {
        // Bug 2368463 
        // Ensure that the LogPort can be obtained when the second enumeration is completed.
        WaitForSingleObject( m_hMonitorLogPort, m_nWaitTimeLogPort );
        ResetEvent( m_hMonitorLogPort );
    }
    ZeroMemory(m_szPortLocationPath, sizeof(m_szPortLocationPath));
}

SPRESULT CDevMode::Get_calibration_IdentifyStart(uint8* pstrBuff, uint32* nBuffSize)
{
	DeclareDiagHeader(hd, DIAG_EXTEND_CMD, 0x91);
	DeclareDiagHeader(rd, DIAG_EXTEND_CMD, 0x91);
	TOOLS_DIAG_AP_REQ_T req;
	ZeroMemory((void*)& req, sizeof(req));
	req.cmd = Convert16((uint16)0x0090);
	req.length = Convert16((uint16)0);
	const unsigned long  BUF_LEN = sizeof(req);
	unsigned char reqBuf[BUF_LEN] = { 0 };
	memcpy((void*)reqBuf, (const void*)& req, sizeof(req));

	UINT32 recvLen = 0;
	SPRESULT res = SendAndRecv(hd, reqBuf, sizeof(reqBuf), hd, (void*)m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);
	if (SP_OK == res)
	{
		if (0 == recvLen)
		{
			LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid response length %d!", __FUNCTION__, recvLen);
			return SP_E_PHONE_INVALID_LENGTH;
		}
		TOOLS_DIAG_AUTH_T cnf = *((TOOLS_DIAG_AUTH_T*)m_diagBuff);
		cnf.status = Convert16((uint16)cnf.status);
		cnf.length = Convert16((uint16)cnf.length);
		if (0 != cnf.status)
		{
			return cnf.status;
		}
		memcpy(pstrBuff, m_diagBuff, sizeof(TOOLS_DIAG_AUTH_T));
		*nBuffSize = sizeof(TOOLS_DIAG_AUTH_T);
	}

	return res;
}


SPRESULT CDevMode::Set_calibration_IdentifyEnd(uint8* pstrBuff)
{

	DeclareDiagHeader(hd, DIAG_EXTEND_CMD, 0x91);
	DeclareDiagHeader(rd, DIAG_EXTEND_CMD, 0x91);
	TOOLS_DIAG_AP_AUTH_REQ_T req;
	ZeroMemory((void*)& req, sizeof(req));
	req.cmd = Convert16((uint16)0x0091);
	req.length = Convert16((uint16)0);

	AUTH_SECURE_DATA_T authData = { 0 };
	memcpy(&authData, pstrBuff, sizeof(AUTH_SECURE_DATA_T));

	const unsigned long  BUF_LEN = sizeof(req) + sizeof(AUTH_SECURE_DATA_T);
	uint8 reqBuf[BUF_LEN] = { 0 };
	memcpy((void*)reqBuf, (const void*)& req, sizeof(req));
	memcpy((void*)(reqBuf + sizeof(req)), (const void*)& authData, sizeof(authData));

	UINT32 recvLen = 0;
	SPRESULT res = SendAndRecv(hd, &reqBuf, BUF_LEN, rd, (void*)m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);
	if (SP_OK == res)
	{
		if (0 == recvLen)
		{
			LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid response length %d!", __FUNCTION__, recvLen);
			return SP_E_PHONE_INVALID_LENGTH;
		}
		TOOLS_DIAG_AP_CNF_T_V2 cnf = *((TOOLS_DIAG_AP_CNF_T_V2*)m_diagBuff);
		cnf.status = Convert16((uint16)cnf.status);
		cnf.length = Convert16((uint16)cnf.length);
		LogFmtStrA(SPLOGLV_INFO, "Set_calibration_IdentifyEnd status is %d", cnf.status);
		if (0 != cnf.status)
		{
			return SP_E_FAIL;
		}
	}

	return res;
}

SPRESULT CDevMode::Authentication()
{
	SPRESULT  res = SP_OK;
	vector<uint8> arrIdentifyStartList(1024, 0);
	uint32 arrIdentifyStartLength = 0;
	res = Get_calibration_IdentifyStart(arrIdentifyStartList.data(), &arrIdentifyStartLength);
	if (res == SP_OK)
	{
		LogFmtStrA(SPLOGLV_INFO, "start Identify");
		TOOLS_DIAG_AP_AUTH_T authStartData = { 0 };

		memcpy(&authStartData, arrIdentifyStartList.data(), sizeof(TOOLS_DIAG_AP_AUTH_T));
		AUTH_DUT_T lpInBuff;
		memcpy(lpInBuff.M1, authStartData.M1, sizeof(authStartData.M1));
		memcpy(lpInBuff.ProjectName, authStartData.ProjectName, sizeof(authStartData.ProjectName));
		lpInBuff.mStatus = authStartData.status;
		AUTH_SECURE_T lpOutBuff = { 0 };
		unsigned long nOutLen = 0;
		ServerAuth('0', &lpInBuff, &lpOutBuff, &nOutLen);
		vector<uint8>arrIdentifyEndList(1024, 0);
		memcpy(arrIdentifyEndList.data(), &lpOutBuff, sizeof(lpOutBuff));
		Sleep(1000);
		res = Set_calibration_IdentifyEnd(arrIdentifyEndList.data());
		if (res == SP_OK)
		{
			LogFmtStrA(SPLOGLV_INFO, "IdentifyEnd Success");
		}
	}
	else if (res == 1)
	{
		LogFmtStrA(SPLOGLV_ERROR, "start Identify error, status = %d", res);
		m_bWorking = FALSE;
	}
	else
	{
		LogFmtStrA(SPLOGLV_INFO, "not need Identify, status = %d", res);
		res = SP_OK;
	}

	return res;
}


SPRESULT CDevMode::StartModeProcess(BOOL bUsbDevice, int nExpPort, RM_MODE_ENUM eMode, HANDLE hMonitorEvent, DWORD dwTimeOut, BOOL bQuitSignal/* = FALSE*/)
{
    m_bQuitSignal = bQuitSignal;

    LogFmtStrA(SPLOGLV_INFO, "Start to enter mode 0x%x by %s: target port = %d, TimeOut = %d, QuitSignal = %d", eMode | RM_U0_AS_DIAG, bUsbDevice ? "USB" : "UART", nExpPort, dwTimeOut, bQuitSignal);
    SPRESULT res = SP_OK;

    m_ModeInfo.bUsbPort = bUsbDevice;
    m_ModeInfo.nExpPort = nExpPort;
    m_ModeInfo.eMode = eMode;
    m_ModeInfo.hMonitor = hMonitorEvent;
    m_ModeInfo.dwTimeOut = (0 == dwTimeOut) ? INFINITE : dwTimeOut;

    if (NULL == hMonitorEvent)
    {
        m_ModeInfo.hMonitor = CreateEvent(NULL, TRUE, FALSE, NULL);
        if (NULL == m_ModeInfo.hMonitor)
        {
            LogFmtStrA(SPLOGLV_ERROR, "%s: CreateEvent failed! WinErr = 0x%X", __FUNCTION__, ::GetLastError());
            return SP_E_PHONE_CREATE_OBJECT;
        }
    }

    ResetEvent(m_hBootUpEvent);

    m_hMonitorLogPort = CreateEvent( NULL, TRUE, FALSE, NULL );
    if ( NULL == m_hMonitorLogPort )
    {
        LogFmtStrA( SPLOGLV_ERROR, "%s: CreateEvent(m_hMonitorLogPort) failed, WinErr = %d", __FUNCTION__, ::GetLastError() );
        return FALSE;
    }
    ResetEvent( m_hMonitorLogPort );

    m_bWorking = TRUE;
    if (!CThread::start())
    {
        m_bWorking = FALSE;

        CloseHandle(hMonitorEvent);
        LogFmtStrA(SPLOGLV_ERROR, "%s: Start mode thread failed!", __FUNCTION__);
        return SP_E_PHONE_THREAD_START;
    }

    /// Synchronously
    if (NULL == hMonitorEvent)
    {
        CThread::join();

        res = (WAIT_OBJECT_0 == WaitForSingleObject(m_ModeInfo.hMonitor, 0)) ? SP_OK : SP_E_PHONE_TIMEOUT;

        CloseHandle(m_ModeInfo.hMonitor);
        m_ModeInfo.hMonitor = NULL;

        if (SP_OK != res)
        {
            LogRawStrA(SPLOGLV_ERROR, "Synchronously enter mode timeout!");
        }
    }

    return res;
}

void CDevMode::StopModeProcess(void)
{
    if (m_bWorking)
    {
        m_bWorking = FALSE;
        CThread::join();
    }
}

SPRESULT CDevMode::SetCampParam(SP_MODE_INFO eMode, SP_BAND_INFO eBand, int nArfcn)
{
    LogFmtStrA(SPLOGLV_INFO, "%s: mode = %d, band = %d, arfcn = %d", __FUNCTION__, eMode, eBand, nArfcn);

    uint8 sn[4] = { 0 };
    switch (eMode)
    {
    case SP_GSM:
    {
        sn[0] = (uint8)(((((uint8)eBand) & 0xF)) | (((nArfcn & 0x700) >> 8) << 5));
        sn[1] = (uint8)nArfcn;
    }
    break;
    case SP_LTE:
    {
        nArfcn = (int)CLteUtility::GetDlCHannel((uint32)nArfcn);
        if (nArfcn > 65535)
        {
            sn[0] = (uint8)((nArfcn >> 8) & 0xFF);
            sn[1] = (uint8)(nArfcn & 0xFF);
            sn[2] = (uint8)((nArfcn >> 16) & 0xFF);   // Byte[2] means Bit 16-23 [6/20/2017 jian.zhong]
        }
        else
        {
            sn[0] = (uint8)((nArfcn >> 8) & 0xFF);
            sn[1] = (uint8)(nArfcn & 0xFF);
        }
    }
    break;
    case SP_TDSCDMA:
    case SP_WCDMA:
    {
        sn[0] = (uint8)((nArfcn >> 8) & 0xFF);
        sn[1] = (uint8)(nArfcn & 0xFF);
    }
    break;
    case SP_C2K:
    {
        sn[0] = (uint8)((nArfcn >> 8) & 0xFF);
        sn[1] = (uint8)(nArfcn & 0xFF);
        sn[2] = (uint8)(eBand & 0xFF);
    }
    break;
    case SP_NR:
    {
        sn[0] = (uint8)((nArfcn >> 16) & 0xFF);
        sn[1] = (uint8)((nArfcn >> 8) & 0xFF);
        sn[2] = (uint8)(nArfcn & 0xFF);
        sn[3] = (uint8)(eBand & 0xFF);
    }
    break;
    default:
    {
        // Clear
        sn[0] = 0xFF;
        sn[1] = 0xFF;
    }
    break;
    }

    m_nCampSN = (sn[0] << 24) | (sn[1] << 16) | (sn[2] << 8) | (sn[3]);

    return SP_OK;
}

SPRESULT CDevMode::RestartPhone(void)
{
    LogRawStrA(SPLOGLV_INFO, "Restart phone ...");
    DeclareDiagHeader(hd, DIAG_CHANGE_MODE_F, 0x08/* RESET_MODE */);

    SPRESULT res = SendCmd(hd, NULL, 0);
    //  Safe remove device: 
    //  Make sure port is closed before device is removed
    //  Otherwise PC USB host perhaps hang dead and restore unless reboot PC.
    if (SP_OK == res)
    {
        Close();
    }

    return res;
}

SPRESULT CDevMode::QueryCurrRunMode(INT& nMode)
{
    LogFmtStrA(SPLOGLV_INFO, "%s", __FUNCTION__);
    /*
        -->: AT+GETTESTMODE?
        <--: +GETTESTMODE:BBAT
        <--: OK
    */

    LPCSTR AT = "AT+GETTESTMODE?";
    CHAR szRsp[1024] = { 0 };
    SPRESULT res = SendATCommand(AT, TRUE, (LPVOID)szRsp, sizeof(szRsp), NULL, TIMEOUT_3S);
    if (SP_OK == res)
    {
        std::string strRsp = szRsp;
        // Remove \r\nOK\r\n
        CRunMode::replace_all(strRsp, "\r\nOK\r\n", "");
        LPCSTR RESPONSE = "+GETTESTMODE:";
        CONST INT  SIZE = strlen(RESPONSE);
        if (std::string::npos != strRsp.find("+CME ERROR") || std::string::npos == strRsp.find(RESPONSE))
        {
            LogFmtStrA(SPLOGLV_ERROR, "Invalid %s response: %s", AT, strRsp.c_str());
            return SP_E_PHONE_QUERY_CURRENT_RUN_MODE;
        }

        std::string strMode = strRsp.substr(strRsp.find(RESPONSE) + SIZE);
        nMode = CRunMode::QueryMode(strMode);
        if (nMode < 0)
        {
            LogFmtStrA(SPLOGLV_ERROR, "Unknown run mode: %s", strMode.c_str());
            return SP_E_PHONE_UNKNOWN_RUN_MODE;
        }

        LogFmtStrA(SPLOGLV_INFO, "Current run mode is %d (%s).", nMode, strMode.c_str());
    }
    else
    {
        res = SP_E_PHONE_QUERY_CURRENT_RUN_MODE;
    }

    return res;
}

SPRESULT CDevMode::SetupNextRunMode(INT nMode)
{
    const char* lpszMode = CRunMode::QueryMode(nMode);
    if (NULL == lpszMode)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Unknown run mode %d", __FUNCTION__, nMode);
        return SP_E_PHONE_UNKNOWN_RUN_MODE;
    }
    else
    {
        LogFmtStrA(SPLOGLV_INFO, "%s: Setup run mode %d (%s)", __FUNCTION__, nMode, lpszMode);
    }

    /*
        -->: AT+SETTESTMODE=BBAT
        <--  +SETTESTMODE:OK
        <--: OK
    */

    std::string strAT = (std::string)"AT+SETTESTMODE=" + lpszMode;
    CHAR szRsp[1024] = { 0 };
    SPRESULT res = SendATCommand(strAT.c_str(), TRUE, (LPVOID)szRsp, sizeof(szRsp), NULL, TIMEOUT_3S);
    if (SP_OK == res)
    {
        std::string strRsp = (CHAR*)szRsp;
        LPCSTR RESPONSE = "+SETTESTMODE:OK";
        if (std::string::npos != strRsp.find("+CME ERROR") || std::string::npos == strRsp.find(RESPONSE))
        {
            LogFmtStrA(SPLOGLV_ERROR, "Invalid %s response %s", strAT.c_str(), strRsp.c_str());
            return SP_E_PHONE_SETUP_NEXT_RUN_MODE;
        }
    }
    else
    {
        res = SP_E_PHONE_SETUP_NEXT_RUN_MODE;
    }

    return res;
}
