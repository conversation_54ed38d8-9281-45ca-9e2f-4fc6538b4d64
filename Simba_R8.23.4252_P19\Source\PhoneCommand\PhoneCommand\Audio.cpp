#include "StdAfx.h"
#include "CommCmd.h"

//////////////////////////////////////////////////////////////////////////
SPRESULT CCommCmd::audioStartRecord(SP_AUDIO_REC_T* pConfig)
{
    CheckValidPointer(pConfig);

    LogFmtStrA(SPLOGLV_INFO, "Start Record: mic type=%d, sample rate=%d, track count=%d, time=%d, bit=%d", 
        pConfig->eMicType, pConfig->u16SampleRate, pConfig->u8TrackCount, pConfig->u32Time, pConfig->u8Bit);

    DIAG_AUDIO_REC_T req;
    req.nState = (uint8)AUDIO_DATA_STATUS_SINGLE;
    uint16 u16Type = (uint16)pConfig->eMicType;
    memcpy(&req.data[0], &u16Type, sizeof(u16Type));
    uint16 u16Rate = pConfig->u16SampleRate;
    memcpy(&req.data[2], &u16Rate, sizeof(u16Rate));
    req.data[4] = (uint8)pConfig->u8TrackCount;
    req.data[5] = (uint8)pConfig->u8Bit;
    uint32 u32Time = pConfig->u32Time;
    memcpy(&req.data[8], &u32Time, sizeof(u32Time));
    DeclareDiagHeader(whd, DIAG_AUDIO_F, 0x30);
    DeclareDiagHeader(rhd, DIAG_AUDIO_F, 0x30);

    uint32 recvLen = 0;
    CHKRESULT(SendAndRecv(whd, (const void *)&req, sizeof(req), rhd, (void *)&m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut));

    if (recvLen < sizeof(DIAG_AUDIO_REC_T))
    {
        LogFmtStrA(SPLOGLV_ERROR, "Start Record failed, recv len %d < %d", recvLen, sizeof(DIAG_AUDIO_REC_T));
        return SP_E_PHONE_INVALID_LENGTH;
    }

    DIAG_AUDIO_REC_T* pRlt = (DIAG_AUDIO_REC_T*)m_diagBuff;
    if (AUDIO_DATA_STATUS_OK != pRlt->nState)
    {
        LogFmtStrA(SPLOGLV_ERROR, "Start Record failed, state: %d != %d", pRlt->nState, AUDIO_DATA_STATUS_OK);
        return SP_E_PHONE_INVALID_STATE;
    }

    return SP_OK;
}

SPRESULT CCommCmd::audioSaveRecordFile(LPCWSTR lpFilePath, DWORD dwTimeOut)
{
    CheckValidPointer(lpFilePath);
    LogFmtStrW(SPLOGLV_INFO, L"Save Record file %s", lpFilePath);

    HANDLE hFile = ::CreateFileW(
        lpFilePath, 
        GENERIC_READ|GENERIC_WRITE,
        FILE_SHARE_READ|FILE_SHARE_WRITE, 
        NULL, 
        CREATE_ALWAYS, 
        FILE_ATTRIBUTE_NORMAL,//|FILE_FLAG_SEQUENTIAL_SCAN,  If this flag is set, the efficiency will be decreased. 
        NULL);
    if (INVALID_HANDLE_VALUE == hFile)
    {
        LogFmtStrW(SPLOGLV_ERROR, L"Open file failed, WinErr = 0x%X", ::GetLastError());
        return SP_E_OPEN_FILE;
    }

    DIAG_AUDIO_REC_T req;
    req.nState = (uint8)AUDIO_DATA_STATUS_SINGLE;
    DeclareDiagHeader(hd, DIAG_AUDIO_F, 0x31);
    DeclareDiagHeader(rd, DIAG_AUDIO_F, 0x31);

    CRecvPkgsList recvList(TRUE);
    recvList.AddCondition(rd);
    SPRESULT res = SendAndRecv(hd, (const void *)&req, sizeof(req), recvList, dwTimeOut);
    if (SP_OK != res)
    {
        ::CloseHandle(hFile);
        return res;
    }

    for (int i=0; i<recvList.GetPkgsCount(); i++)
    {
        uint32 u32pkgSize = 0;
        uint8  UnpackBuf[MAX_DIAG_BUFF_SIZE] = {0};
        if (UnpackPRT(recvList.GetPackage(i), NULL, (void* )UnpackBuf, sizeof(UnpackBuf), &u32pkgSize))
        {
            if (u32pkgSize < sizeof(DIAG_AUDIO_REC_T))
            {
                ::CloseHandle(hFile);
                LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid response length, %d", __FUNCTION__, u32pkgSize);
                return SP_E_PHONE_INVALID_LENGTH;
            }

            DIAG_AUDIO_REC_T* pRlt = (DIAG_AUDIO_REC_T* )UnpackBuf;
            if (AUDIO_DATA_STATUS_START == pRlt->nState || AUDIO_DATA_STATUS_MIDDLE == pRlt->nState || AUDIO_DATA_STATUS_END == pRlt->nState)
            {
                uint8*  pData = (uint8* )(UnpackBuf + sizeof(DIAG_AUDIO_REC_T));
                int32 i32Size = (int32 )(u32pkgSize - sizeof(DIAG_AUDIO_REC_T));
                if (i32Size > 0 && NULL != pData)
                {
                    DWORD dwWritten = 0;
                    if (!::WriteFile(hFile, pData, i32Size, &dwWritten, NULL) || (dwWritten != (DWORD)i32Size))
                    {
                        ::CloseHandle(hFile);
                        LogFmtStrA(SPLOGLV_ERROR, "Write file failed, WinErr = 0x%X", ::GetLastError());
                        return SP_E_PHONE_FILE_IO;
                    }
                }

                if (AUDIO_DATA_STATUS_END == pRlt->nState)
                {
                    LogFmtStrA(SPLOGLV_INFO, "Record file transmit finished");
                    break;
                }
            }
            else
            {
                ::CloseHandle(hFile);
                LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid status = %d!", __FUNCTION__, pRlt->nState);
                return SP_E_PHONE_AP_INVALID_STATUS;
            }
        }
    }

    ::CloseHandle(hFile);
    hFile = INVALID_HANDLE_VALUE;

    return SP_OK;
}

void CCommCmd::WakeupAudioRecordResponse(PRT_BUFF* lpBuff)
{
    if (NULL == lpBuff)
    {
        assert(0);
        return ;
    }

    uint8  UnpackBuf[MAX_DIAG_BUFF_SIZE] = {0};
    uint32 u32Size = 0;
    if (!UnpackPRT(lpBuff, NULL, (void* )UnpackBuf, sizeof(UnpackBuf), &u32Size))
    {
        return ;
    }

    if (u32Size < sizeof(DIAG_AUDIO_REC_T))
    {
        return ;
    }

    DIAG_AUDIO_REC_T* pRlt = (DIAG_AUDIO_REC_T* )UnpackBuf;
    if (AUDIO_DATA_STATUS_END == pRlt->nState)
    {
        WakeUpOnResponse();
    }
}