#pragma once
#include "global_def.h"

///
class CEnianConvert sealed
{
public:
     CEnianConvert(void);
    ~CEnianConvert(void);

    void SetEndian(SP_ENDIAN_TYPE eType);
    SP_ENDIAN_TYPE GetEndian(void)const;

    uint32   Conv32(uint32 Value);
    uint16   Conv16(uint16 Value);
    uint8*   Conv32(uint8* lpData, uint32 u32Size);
    uint8*   Conv16(uint8* lpData, uint32 u32Size);

private:
    SP_ENDIAN_TYPE m_eEndianType;
};

inline void CEnianConvert::SetEndian(SP_ENDIAN_TYPE eType)
{
    m_eEndianType = eType;
}

inline SP_ENDIAN_TYPE CEnianConvert::GetEndian(void)const 
{
    return m_eEndianType;
}

inline uint32 CEnianConvert::Conv32(uint32 Value)
{
    if (SP_LITTLE_ENDIAN != m_eEndianType)
    {
        return MAKELONG(MAKEWORD(HIBYTE(HIWORD(Value)), LOBYTE(HIWORD(Value))), \
            MAKEWORD(HIBYTE(LOWORD(Value)), LOBYTE(LOWORD(Value))));
    }
    else
    {
        return Value;
    }
}

inline uint16 CEnianConvert::Conv16(uint16 Value)
{
    if (SP_LITTLE_ENDIAN != m_eEndianType)
    {
        return MAKEWORD(HIBYTE(Value), LOBYTE(Value));
    }
    else
    {
        return Value;
    }
}