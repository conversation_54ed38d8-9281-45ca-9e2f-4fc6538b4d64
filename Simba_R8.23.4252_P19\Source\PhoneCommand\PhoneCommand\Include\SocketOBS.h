#pragma once

#include "global_def.h"
#include "ProtoChan.h"
#include <winsock.h>
#pragma comment(lib, "Ws2_32.lib")
///////////////////////////////////////////////////////////////////////////
/// 
class CSocketObserver : public IProtocolObserver
{
public:
    CSocketObserver(void) 
        : m_lpSocket(NULL) { };
    virtual ~CSocketObserver(void) { };

    BOOL IsConnected(void)const { return (NULL != m_lpSocket) ? TRUE : FALSE; };

    BOOL Start(char szIP[MAX_IP_ADDR_LEN], DWORD dwPort)
    {
        Stop();

        CreateChannel(&m_lpSocket, CHANNEL_TYPE_SOCKET);
        if (NULL == m_lpSocket)
        {
            assert(0);
            return FALSE;
        }

        CHANNEL_ATTRIBUTE ca;
        ca.ChannelType   = CHANNEL_TYPE_SOCKET;
        ca.Socket.dwIP   = ntohl(inet_addr(szIP));
        ca.Socket.dwPort = dwPort;
        ca.Socket.dwFlag = SC_TYPE_SERVER;  // Server

        if (!m_lpSocket->Open(&ca))
        {
            ReleaseChannel(m_lpSocket);
            m_lpSocket = NULL;
            return FALSE;
        }

        return TRUE;
    }

    void Stop(void)
    {
        if (NULL != m_lpSocket)
        {
            m_lpSocket->Close();
            ReleaseChannel(m_lpSocket);
            m_lpSocket = NULL;
        }
    }

protected:
    virtual int OnChannelData(LPVOID lpData, ULONG)
    {
        if (NULL == lpData)
        {
            return 0;
        }

        PRT_BUFF* lpBuff = (PRT_BUFF* )lpData;
        if (NULL != m_lpSocket)
        {
            m_lpSocket->Write(lpBuff->lpData, lpBuff->size, 0);
        }

        lpBuff->free_prt(lpBuff);
        return 1;
    }
    
    virtual int OnChannelEvent(unsigned int, void* lpEventData)
    {
        PRT_BUFF* lpBuff = static_cast<PRT_BUFF* >(lpEventData);
        if (NULL != lpBuff)
        {
            lpBuff->free_prt(lpBuff);
        }

        return 1;
    }

private:
    ICommChannel* m_lpSocket;
};