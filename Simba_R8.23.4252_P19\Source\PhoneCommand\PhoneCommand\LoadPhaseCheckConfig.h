#pragma once
#include "PhoneCommand.h"
#include <string>
#include <vector>

//////////////////////////////////////////////////////////////////////////
class CLoadPhaseCheckConfig sealed
{
public:
    CLoadPhaseCheckConfig(void);
   ~CLoadPhaseCheckConfig(void);

   void Load(HMODULE hApp);

public:
   SPPH_MAGIC          m_eMagic;
   SP09_SIGN_FLAG   m_eSignFlag;
   std::vector<std::string> m_vecStations;
};

