#include "StdAfx.h"
#include "CaliCmd.h"
#include "wcdmaUtility.h"
#include "WcdmaDef.h"

///
#define wcdmaIsValidBand(band)  ( (band) >= BI_W_B1 && (band) < BI_W_MAX_BAND ) 

//////////////////////////////////////////////////////////////////////////
uint16 CCaliCmd::wcdmaIoBand(SP_BAND_INFO eBand)
{
    return static_cast<uint16>(eBand + 1); 
}

SPRESULT CCaliCmd::wcdmaRLT(const void* lpRecvBuf, uint32 u32RecvSize)
{
    uint32 u32ExpSize = sizeof(L1_CALI_WCDMA_RLT_T);
    if (u32RecvSize < u32ExpSize)
    {
        LogFmtStrA(SPLOGLV_ERROR, "Invalid response length %d < %d!", u32RecvSize, u32ExpSize);
        return SP_E_PHONE_INVALID_LENGTH;
    }

    L1_CALI_WCDMA_RLT_T* pRLT = (L1_CALI_WCDMA_RLT_T* )lpRecvBuf;
    uint32 u32state = Convert32(pRLT->result);
    if (RF_OP_SUCCESS != u32state)
    {
        LogFmtStrA(SPLOGLV_ERROR, "WCDMA operation failed, state = %d.", u32state);
        return SP_E_PHONE_INVALID_STATE;
    }

    return SP_OK;
}

SPRESULT CCaliCmd::wcdma_32K_FrequencyError(int16* pFrequencyError)
{
    if (pFrequencyError == nullptr)
    {
        return SP_E_PHONE_INVALID_PARAMETER;
    }

    LogFmtStrA(SPLOGLV_INFO, "%s", __FUNCTION__);
    uint32 recvSize = 0;

    L1_TOOL_COMMON_REQ L1;
    ZeroMemory((void*)& L1, sizeof(L1));
    L1.SignalCode = Convert16(CALI_WCDMA_32K_FREQUENCY_ERROR);
    L1.SignalSize = Convert16((uint16)sizeof(L1));

    DeclareDiagHeader(hd, DIAG_RF_WCDMA_F, ZERO_SUBTYPE);

    CHKRESULT(SendAndRecv(hd, (const void*)& L1, sizeof(L1), hd, (void*)m_diagBuff, sizeof(m_diagBuff), &recvSize, m_dwTimeOut));

    if (recvSize < sizeof(L1_TOOL_WCDMA_32K_RLT))
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: recvSize is too small", __FUNCTION__);
        return SP_E_PHONE_INVALID_LENGTH;
    }

    L1_TOOL_WCDMA_32K_RLT* rlt = (L1_TOOL_WCDMA_32K_RLT*)m_diagBuff;

    *pFrequencyError = rlt->result;

    if (rlt->status != 0)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: rlt status = %d", __FUNCTION__, rlt->status);
        return SP_E_PHONE_INVALID_STATE;
    }

    LogFmtStrA(SPLOGLV_INFO, "%s OK", __FUNCTION__);

    return SP_OK;
}

SPRESULT CCaliCmd::wcdmaActive(BOOL bActive)
{
    LogFmtStrA(SPLOGLV_INFO, "%s", bActive ? "WCDMA Active" : "WCDMA DeActive");

    SPRESULT    res = SP_OK;
    uint32 recvSize = 0;

    if (bActive)
    {
        L1_CALI_WCDMA_REQ_T L1;
        ZeroMemory((void* )&L1,  sizeof(L1));
        L1.SignalCode  = Convert16(CALI_WCDMA_ACTIVE_MODE_REQ);
        L1.SignalSize  = Convert16((uint16)sizeof(L1));

        DeclareDiagHeader(hd, DIAG_RF_WCDMA_F, ZERO_SUBTYPE); 
        res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvSize, m_dwTimeOut);
    }
    else
    {
        L1_CALI_WCDMA_REQ_T L1;
        ZeroMemory((void* )&L1,    sizeof(L1));

        L1.SignalCode  = Convert16(CALI_WCDMA_DEACTIVE_MODE_REQ);
        L1.SignalSize  = Convert16((uint16)sizeof(L1));

        DeclareDiagHeader(hd, DIAG_RF_WCDMA_F, ZERO_SUBTYPE); 
        res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvSize, m_dwTimeOut);
    }

    if (SP_OK == res)
    {
        res = wcdmaRLT((const void* )m_diagBuff, recvSize);
    }
    else
    {
        LogFmtStrA(SPLOGLV_ERROR, "wcdmaActive Fail!");
    }

    return res;
}

SPRESULT CCaliCmd::wcdmaTxOnOff(const PC_CALI_WCDMA_TX_REQ_T* req)
{
    CheckValidPointer(req);

    LogFmtStrA(SPLOGLV_INFO, "WCDMA TX %s: band = %d, CH = %d, mode = %d, path = %d, mask = %d, gain = %d, dcdc = %d", \
        req->on_off ? "ON" : "OFF", 
        wcdmaIoBand(req->band), 
        req->arfcn, 
        req->mode, 
        req->path, 
        req->cmd_mask, 
        req->gain_index, 
        req->dcdc_value
        );
    
    L1_CALI_WCDMA_TX_REQ_T L1;
    ZeroMemory((void* )&L1, sizeof(L1));

    L1.SignalCode  = Convert16(CALI_WCDMA_TX_REQ);
    L1.SignalSize  = Convert16((uint16)sizeof(L1));

    L1.band        = Convert16((uint16)(wcdmaIoBand(req->band)));
    L1.path        = Convert16((uint16)(req->path));
    L1.on_off      = Convert16((uint16)(req->on_off));
    L1.uarfcn      = Convert16((uint16)(req->arfcn));
    L1.mode        = Convert16((uint16)(req->mode));
    L1.cmd_mask    = Convert16((uint16)(req->cmd_mask));
    L1.dcdc_value  = Convert16((uint16)(req->dcdc_value));
    L1.gain_index  = Convert16((uint16)(req->gain_index));
    L1.index_src   = Convert16((uint16) 1);       // Always: 1
    L1.iq_com      = Convert16((uint16)(req->iq_com));

    DeclareDiagHeader(hd, DIAG_RF_WCDMA_F, ZERO_SUBTYPE); 

    uint32 recvLen = 0;
    SPRESULT   res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);
    if (SP_OK == res)
    {
        res = wcdmaRLT((const void* )m_diagBuff, recvLen);
    }

    return res;
}

SPRESULT CCaliCmd::wcdmaSetAfcValue(const PC_CALI_WCDMA_SET_AFC_VALUE_REQ_T* req)
{
    CheckValidPointer(req);

    LogFmtStrA(SPLOGLV_INFO, "WCDMA Set AFC: %s, cdac = %d, cafc = %d", (RF_AFC_TYPE_TCXO == req->type) ? "TCXO" : "DCXO", req->cdac, req->cafc);
    
    L1_CALI_WCDMA_SET_AFC_REQ_T    L1;
    ZeroMemory((void* )&L1, sizeof(L1));

    L1.SignalCode  = Convert16(CALI_WCDMA_SET_AFC_REQ);
    L1.SignalSize  = Convert16((uint16)sizeof(L1));

    L1.type        = Convert32(req->type);
    L1.cdac        = Convert32(req->cdac);
    L1.cafc        = Convert32(req->cafc);
    L1.params3     = Convert32(req->reserved[0]);
    L1.params4     = Convert32(req->reserved[1]);
    L1.params5     = Convert32(req->reserved[2]);

    DeclareDiagHeader(hd, DIAG_RF_WCDMA_F, ZERO_SUBTYPE); 

    uint32 recvLen = 0;
    SPRESULT   res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);
    if (SP_OK == res)
    {
        res = wcdmaRLT((const void* )m_diagBuff, recvLen);
    }
    
    return res;
}

SPRESULT CCaliCmd::wcdmaSetDCDC(RF_WCDMA_DCDC_TYPE eType, uint32 u32Value)
{
    LogFmtStrA(SPLOGLV_INFO, "WCDMA Set DCDC %s = %d", (RF_CTRL_DCDC_BY_VALUE == eType) ? "value" : "index", u32Value);
    
    L1_CALI_WCDMA_SET_DCDC_REQ_T L1;
    ZeroMemory((void* )&L1, sizeof(L1));

    L1.SignalCode      = Convert16(CALI_WCDMA_SET_DCDC_REQ);
    L1.SignalSize      = Convert16((uint16)sizeof(L1));
    
    L1.index_or_value  = Convert32(eType);
    L1.params1         = Convert32(u32Value);
    L1.params2         = 0;

    DeclareDiagHeader(hd, DIAG_RF_WCDMA_F, ZERO_SUBTYPE); 

    uint32 recvLen = 0;
    SPRESULT   res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);
    if (SP_OK == res)
    {
        res = wcdmaRLT((const void* )m_diagBuff, recvLen);
    }
   
    return res;
}

SPRESULT CCaliCmd::wcdmaSetTxgain(const PC_CALI_WCDMA_TX_SET_GAIN_REQ_T* req, LPPC_CALI_WCDMA_TX_SET_GAIN_RLT_T rlt)
{
    if (NULL == req || NULL == rlt)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid parameters!", __FUNCTION__);
        return SP_E_PHONE_INVALID_PARAMETER;
    }

    LogFmtStrA(SPLOGLV_INFO, "WCDMA Set TX gain: band = %d, mode = %d, path = %d, mask = %d, gain = %d, dcdc = %d",
        wcdmaIoBand(req->band), 
        req->mode, 
        req->path, 
        req->cmd_mask, 
        req->gain_index, 
        req->dcdc_value
        );

    L1_CALI_WCDMA_TX_SET_GAIN_REQ_T L1;
    ZeroMemory((void *)&L1,  sizeof(L1));

    L1.SignalCode  = Convert16(CALI_WCDMA_TX_SET_GAIN_REQ);
    L1.SignalSize  = Convert16((uint16)sizeof(L1));

    L1.band        = Convert16((uint16)(wcdmaIoBand(req->band)));
    L1.path        = Convert16((uint16)(req->path));
    L1.mode        = Convert16((uint16)(req->mode));
    L1.cmd_mask    = Convert16((uint16)(req->cmd_mask));
    L1.dcdc_value  = Convert16((uint16)(req->dcdc_value));
    L1.gain_index  = Convert16((uint16)(req->gain_index));
    L1.index_src   = Convert16((uint16)(1));       // Always: 1
    L1.iq_com      = Convert16((uint16)(req->iq_com));

    DeclareDiagHeader(hd, DIAG_RF_WCDMA_F, ZERO_SUBTYPE); 

    uint32 recvLen = 0;
    SPRESULT   res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);
    if (SP_OK == res)
    {
        uint32 u32ExpSize = sizeof(L1_CALI_WCDMA_TX_SET_GAIN_RLT_T);
        if (recvLen < u32ExpSize)
        {
            LogFmtStrA(SPLOGLV_ERROR, "Invalid response length %d < %d!", recvLen, u32ExpSize);
            return SP_E_PHONE_INVALID_LENGTH;
        }

        L1_CALI_WCDMA_TX_SET_GAIN_RLT_T* pRLT = (L1_CALI_WCDMA_TX_SET_GAIN_RLT_T* )m_diagBuff;
        uint32 u32State = Convert32(pRLT->result);
        if (RF_OP_SUCCESS != u32State)
        {
            LogFmtStrA(SPLOGLV_ERROR, "WCDMA Operation failed, state = %d.", u32State);
            return SP_E_PHONE_INVALID_STATE;
        }
        else
        {
            rlt->gain_code = Convert32(pRLT->gain_code);
            rlt->hdt_value = Convert32(pRLT->hdt_value);
            rlt->dcdc_value= Convert32(pRLT->dcdc_value);
            rlt->values[0] = Convert32(pRLT->value2);
            rlt->values[1] = Convert32(pRLT->value3);
            rlt->values[2] = Convert32(pRLT->value4);

            LogFmtStrA(SPLOGLV_INFO, "gain_code = %d, HDT = %d, DCDC = %d", rlt->gain_code, rlt->hdt_value, rlt->dcdc_value);
        }
    }

    return res;
}

SPRESULT CCaliCmd::wcdmaSetHDTRange(int32 i32Range)
{
    LogFmtStrA(SPLOGLV_INFO, "WCDMA Set HDT range %d", i32Range);

    L1_CALI_WCDMA_SET_HDT_RANGE_REQ_T L1;
    ZeroMemory((void* )&L1, sizeof(L1));

    L1.SignalCode    = Convert16(CALI_WCDMA_SET_HDT_RANGE_REQ);
    L1.SignalSize    = Convert16((uint16)sizeof(L1));

    L1.pwrdect_range = Convert32(i32Range);

    DeclareDiagHeader(hd, DIAG_RF_WCDMA_F, ZERO_SUBTYPE); 

    uint32 recvLen = 0;
    SPRESULT   res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);
    if (SP_OK == res)
    {
        
    }

    return res;
}

SPRESULT CCaliCmd::wcdmaGetHDT(RF_WCDMA_PATH_E ePath, uint32* hdt)
{
    CheckValidPointer(hdt);

    LogFmtStrA(SPLOGLV_INFO, "WCDMA Get HDT: path = %d", ePath);

    L1_CALI_WCDMA_TX_READ_HDT_REQ_T L1;
    ZeroMemory((void* )&L1,  sizeof(L1));

    L1.SignalCode  = Convert16(CALI_WCDMA_TX_READ_HDT_REQ);
    L1.SignalSize  = Convert16((uint16)sizeof(L1));

    L1.path        = Convert32((uint32)(ePath));

    DeclareDiagHeader(hd, DIAG_RF_WCDMA_F, ZERO_SUBTYPE); 
    uint32 recvLen = 0;
    SPRESULT   res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);
    if (SP_OK == res)
    {
        uint32 u32ExpSize = sizeof(L1_CALI_WCDMA_TX_READ_HDT_RLT_T);
        if (recvLen < u32ExpSize)
        {
            LogFmtStrA(SPLOGLV_ERROR, "Invalid response length, %d < %d!", recvLen, u32ExpSize);
            return SP_E_PHONE_INVALID_LENGTH;
        }
    
        L1_CALI_WCDMA_TX_READ_HDT_RLT_T* pRLT = (L1_CALI_WCDMA_TX_READ_HDT_RLT_T* )m_diagBuff;
        uint32 u32State = Convert32(pRLT->result);
        if (RF_OP_SUCCESS != u32State)
        {
            LogFmtStrA(SPLOGLV_ERROR, "WCDMA operation failed, state = %d.", u32State);
            return SP_E_PHONE_INVALID_STATE;
        }
        else
        {
            *hdt = Convert32(pRLT->hdt_value);
            LogFmtStrA(SPLOGLV_INFO, "HDT = %d (0x%X)", *hdt, *hdt);
        }
    }
   
    return res;
}

SPRESULT CCaliCmd::wcdmaRxOnOff(const PC_CALI_WCDMA_RX_REQ_T* req)
{
    CheckValidPointer(req);

    LogFmtStrA(SPLOGLV_INFO, "WCDMA RX %s: band = %d, path = %d, CH = %d, gain = %d", 
        req->on_off ? "ON" : "FALSE",
        wcdmaIoBand(req->band), 
        req->path, 
        req->arfcn, 
        req->gain_index
        );
    
    L1_CALI_WCDMA_RX_REQ_T L1;
    ZeroMemory((void* )&L1, sizeof(L1));

    L1.SignalCode  = Convert16(CALI_WCDMA_RX_REQ);
    L1.SignalSize  = Convert16((uint16)sizeof(L1));

    L1.band        = Convert16((uint16)(wcdmaIoBand(req->band)));
    L1.path        = Convert16((uint16)(req->path));
    L1.on_off      = Convert16((uint16)(req->on_off));
    L1.uarfcn      = Convert16((uint16)(req->arfcn));
    L1.gain_index  = Convert16((uint16)(req->gain_index));
    L1.index_src   = Convert16((uint16)(1));   // Always: 1

    DeclareDiagHeader(hd, DIAG_RF_WCDMA_F, ZERO_SUBTYPE); 

    uint32 recvLen = 0;
    SPRESULT   res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);
    if (SP_OK == res)
    {
        res = wcdmaRLT((const void* )m_diagBuff, recvLen);
    }
    
    return res;
}

SPRESULT CCaliCmd::wcdmaSetRxgain(const PC_CALI_WCDMA_RX_SET_GAIN_REQ_T* req, LPPC_CALI_WCDMA_RX_SET_GAIN_RLT_T rlt)
{
    if (NULL == req || NULL == rlt)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid parameters!", __FUNCTION__);
        return SP_E_PHONE_INVALID_PARAMETER;
    }
    
    LogFmtStrA(SPLOGLV_INFO, "WCDMA Set TX gain: band = %d, path = %d, gain = %d", wcdmaIoBand(req->band), req->path, req->gain_index);
    
    L1_CALI_WCDMA_RX_SET_GAIN_REQ_T L1;
    ZeroMemory((void *)&L1,  sizeof(L1));

    L1.SignalCode  = Convert16(CALI_WCDMA_RX_SET_GAIN_REQ);
    L1.SignalSize  = Convert16((uint16)sizeof(L1));

    L1.band        = Convert16((uint16)(wcdmaIoBand(req->band)));
    L1.path        = Convert16((uint16)(req->path));
    L1.gain_index  = Convert16((uint16)(req->gain_index));
    L1.index_src   = Convert16((uint16)(1));   // Always: 1

    DeclareDiagHeader(hd, DIAG_RF_WCDMA_F, ZERO_SUBTYPE); 

    uint32 recvLen = 0;
    SPRESULT   res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);
    if (SP_OK == res)
    {
        uint32 u32ExpSize = sizeof(L1_CALI_WCDMA_RX_SET_GAIN_RLT_T);
        if (recvLen < u32ExpSize)
        {
            LogFmtStrA(SPLOGLV_ERROR, "Invalid response length %d < %d!", recvLen, u32ExpSize);
            return SP_E_PHONE_INVALID_LENGTH;
        }

        L1_CALI_WCDMA_RX_SET_GAIN_RLT_T* pRLT = (L1_CALI_WCDMA_RX_SET_GAIN_RLT_T* )m_diagBuff;
        uint32 u32State = Convert32(pRLT->result);
        if (RF_OP_SUCCESS != u32State)
        {
            LogFmtStrA(SPLOGLV_ERROR, "WCDMA operation failed, state = %d.", u32State);
            return SP_E_PHONE_INVALID_STATE;
        }
        else
        {
            rlt->gain_code = Convert32(pRLT->gain_code);
            rlt->rssi      = Convert32(pRLT->rssi);
            rlt->values[0] = Convert32(pRLT->value1);
            rlt->values[1] = Convert32(pRLT->value2);
            rlt->values[2] = Convert32(pRLT->value3);
            rlt->values[3] = Convert32(pRLT->value4);

            LogFmtStrA(SPLOGLV_INFO, "gain_code = %d, rssi = %d (0x%X)", rlt->gain_code, rlt->rssi, rlt->rssi);
        }
    }

    return res;
}

SPRESULT CCaliCmd::wcdmaGetRSSI(RF_WCDMA_PATH_E ePath, uint32* rssi1, uint32* rssi2)
{
    CheckValidPointer(rssi1);

    LogFmtStrA(SPLOGLV_INFO, "Get WCDMA RSSI: path = %d", ePath);

    L1_CALI_WCDMA_RX_RSSI_REQ_T    L1;
    ZeroMemory((void* )&L1, sizeof(L1));

    L1.SignalCode  = Convert16(CALI_WCDMA_RX_RSSI_REQ);
    L1.SignalSize  = Convert16((uint16)sizeof(L1));

    L1.path        = Convert32((uint32)ePath);

    DeclareDiagHeader(hd, DIAG_RF_WCDMA_F, ZERO_SUBTYPE); 

    uint32 recvLen = 0;
    SPRESULT   res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);
    if (SP_OK == res)
    {
        uint32 u32ExpSize = sizeof(L1_CALI_WCDMA_RX_RSSI_RLT_T);
        if (recvLen < u32ExpSize)
        {
            LogFmtStrA(SPLOGLV_ERROR, "Invalid response length %d < %d!", recvLen, u32ExpSize);
            return SP_E_PHONE_INVALID_LENGTH;
        }

        L1_CALI_WCDMA_RX_RSSI_RLT_T* pRLT = (L1_CALI_WCDMA_RX_RSSI_RLT_T* )m_diagBuff;
        uint32 u32State = Convert32(pRLT->result);
        if (RF_OP_FAIL == u32State || RF_OP_FAIL_V2 == u32State)
        {
            LogFmtStrA(SPLOGLV_ERROR, "WCDMA operation failed, state = %d.", u32State);
            return SP_E_PHONE_INVALID_STATE;
        }

        if (RF_OP_SUCCESS_V2 == u32State)
        {
            uint32 u32Tmp = Convert32(pRLT->rssi);
            *rssi1 = u32Tmp & 0xFFFF;
            *rssi1 = *rssi1 << 3;
            LogFmtStrA(SPLOGLV_INFO, "rssi1 = %d (0x%X)", *rssi1, *rssi1);

            if (NULL != rssi2)
            {
                *rssi2 = u32Tmp >> 16;
                *rssi2 = *rssi2 << 3;
                LogFmtStrA(SPLOGLV_INFO, "rssi2 = %d (0x%X)", *rssi2, *rssi2);
            }
            else
            {
                LogFmtStrA(SPLOGLV_ERROR, "rssi2 is invalid parameter.");
                return SP_E_PHONE_INVALID_PARAMETER;
            }
        }
        else
        {
            *rssi1 = Convert32(pRLT->rssi);
            LogFmtStrA(SPLOGLV_INFO, "rssi1 = %d (0x%X)", *rssi1, *rssi1);
        }
    }

    return res;
}

SPRESULT CCaliCmd::wcdmaGetAusRSSI(RF_WCDMA_PATH_E ePath, uint32* rssi)
{
    CheckValidPointer(rssi);

    LogFmtStrA(SPLOGLV_INFO, "Get Aus WCDMA RSSI: path = %d", ePath);

    L1_CALI_WCDMA_RX_RSSI_REQ_AUS_T    L1;
    ZeroMemory((void* )&L1, sizeof(L1));

    L1.SignalCode  = Convert16(CALI_WCDMA_RX_RSSI_AUS_REQ);
    L1.SignalSize  = Convert16((uint16)sizeof(L1));

    L1.path        = Convert32((uint32)ePath);
    L1.read_count = 2048;

    DeclareDiagHeader(hd, DIAG_RF_WCDMA_F, ZERO_SUBTYPE); 

    uint32 recvLen = 0;
    SPRESULT   res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);
    if (SP_OK == res)
    {
        uint32 u32ExpSize = sizeof(L1_CALI_WCDMA_RX_RSSI_RLT_T);
        if (recvLen < u32ExpSize)
        {
            LogFmtStrA(SPLOGLV_ERROR, "Invalid response length %d < %d!", recvLen, u32ExpSize);
            return SP_E_PHONE_INVALID_LENGTH;
        }

        L1_CALI_WCDMA_RX_RSSI_RLT_T* pRLT = (L1_CALI_WCDMA_RX_RSSI_RLT_T* )m_diagBuff;
        uint32 u32State = Convert32(pRLT->result);
        if (RF_OP_FAIL == u32State || RF_OP_FAIL_V2 == u32State)
        {
            LogFmtStrA(SPLOGLV_ERROR, "WCDMA operation failed, state = %d.", u32State);
            return SP_E_PHONE_INVALID_STATE;
        }

        if (RF_OP_SUCCESS_V2 == u32State)
        {
            uint32 u32Tmp = Convert32(pRLT->rssi);
            *rssi = u32Tmp & 0xFFFF;
            *rssi = *rssi << 3;
            LogFmtStrA(SPLOGLV_INFO, "rssi = %d (0x%X)", *rssi, *rssi);           
        }
        else
        {
            *rssi = Convert32(pRLT->rssi);
            LogFmtStrA(SPLOGLV_INFO, "rssi = %d (0x%X)", *rssi, *rssi);
        }
    }

    return res;
}

SPRESULT CCaliCmd::wcdmaCalIQ(RF_WCDMA_PATH_E ePath, int32* cof_i, int32* cof_q)
{
    if (NULL == cof_i || NULL == cof_q)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid parameters!", __FUNCTION__);
        return SP_E_PHONE_INVALID_PARAMETER;
    }

    LogFmtStrA(SPLOGLV_INFO, "Get WCDMA IQ: path = %d", ePath);

    L1_CALI_WCDMA_CAL_IQ_REQ_T     L1;
    ZeroMemory((void* )&L1, sizeof(L1));

    L1.SignalCode  = Convert16(CALI_WCDMA_CAL_IQ_REQ);
    L1.SignalSize  = Convert16((uint16)sizeof(L1));

    L1.path        = Convert16((uint16)ePath);
    L1.reserved    = 0;
    
    DeclareDiagHeader(hd, DIAG_RF_WCDMA_F, ZERO_SUBTYPE); 

    uint32 recvLen = 0;
    SPRESULT   res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);
    if (SP_OK == res)
    {
        uint32 u32ExpSize = sizeof(L1_CALI_WCDMA_CAL_IQ_RLT_T);
        if (recvLen < u32ExpSize)
        {
            LogFmtStrA(SPLOGLV_ERROR, "Invalid response length %d < %d!", recvLen, u32ExpSize);
            return SP_E_PHONE_INVALID_LENGTH;
        }

        L1_CALI_WCDMA_CAL_IQ_RLT_T* pRLT = (L1_CALI_WCDMA_CAL_IQ_RLT_T* )m_diagBuff;
        uint32 u32State = Convert32(pRLT->result);
        if (RF_OP_FAIL == u32State || RF_OP_FAIL_V2 == u32State)
        {
            LogFmtStrA(SPLOGLV_ERROR, "WCDMA operation failed, state = %d.", u32State);
            return SP_E_PHONE_INVALID_STATE;
        }
        else
        {
            *cof_i = Convert32(pRLT->cof_i);
            *cof_q = Convert32(pRLT->cof_q);
            LogFmtStrA(SPLOGLV_INFO, "I = %d, Q = %d", *cof_i, *cof_q);
        }
    }

    return res;
}

SPRESULT CCaliCmd::wcdmaDP(const PC_CALI_WCDMA_TX_DYNAMIC_PWR_REQ_V2_T* req)
{
    CheckValidPointer(req);

    LogRawStrA(SPLOGLV_INFO, "WCDMA Dynamic:");

    L1_CALI_WCDMA_TX_DYNAMIC_PWR_REQ_V2_T L1;
    ZeroMemory((void* )&L1, sizeof(L1));

    L1.interval                = Convert32((uint32)(req->interval));

    L1.tx.dft_mode             = Convert16((uint16)(req->tx.dft_mode));
    L1.tx.dft_dcdc_value       = Convert16((uint16)(req->tx.dft_dcdc_value));
    L1.tx.dft_gain_index       = Convert16((uint16)(req->tx.dft_gain_index));
    L1.tx.dft_iq_com           = Convert16((uint16)(req->tx.dft_iq_com));
    L1.tx.path                 = Convert16((uint16)(req->tx.path));
    L1.tx.inter_cnt_change_ch  = Convert16((uint16)(req->tx.inter_cnt_change_ch));
    L1.tx.cmd_mask             = Convert16((uint16)(req->tx.cmd_mask));
    L1.tx.seq_num              = Convert16((uint16)(req->tx.seq_num));
    for (int i=0; i<MAX_WCDMA_SEQ_NUM_V2; i++)
    {
        L1.tx.seq[i].seg_num   = Convert32(req->tx.seq[i].seg_num);
        for (int j=0; j<MAX_WCDMA_TX_SEG_NUMBER_V2; j++)
        {
            L1.tx.seq[i].seg[j].start_gain_index   = Convert16(req->tx.seq[i].seg[j].start_gain_index);
            L1.tx.seq[i].seg[j].gain_index_step    = Convert16(req->tx.seq[i].seg[j].gain_index_step);
            L1.tx.seq[i].seg[j].step_num           = Convert16(req->tx.seq[i].seg[j].step_num);

            BYTE mode = (BYTE)req->tx.seq[i].seg[j].mode;
            BYTE dcdc = (BYTE)req->tx.seq[i].seg[j].dcdc_value;
            uint16 mode_dcdc = MAKEWORD(dcdc, mode);
            L1.tx.seq[i].seg[j].mode_dcdc          = Convert16((uint16)(mode_dcdc));  
            L1.tx.seq[i].seg[j].band               = Convert16((uint16)(wcdmaIoBand(req->tx.seq[i].seg[j].band)));
            L1.tx.seq[i].seg[j].uarfcn             = Convert16((uint16)(req->tx.seq[i].seg[j].uarfcn));
        }
    }

    L1.SignalCode  = Convert16(CALI_WCDMA_TX_DYNAMIC_PWR_V2_REQ);
    L1.SignalSize  = Convert16((uint16)sizeof(L1));

    DeclareDiagHeader(hd, DIAG_RF_WCDMA_F, ZERO_SUBTYPE); 

    uint32 recvLen = 0;
    SPRESULT   res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);
    if (SP_OK == res)
    {
        res = wcdmaRLT((const void* )m_diagBuff, recvLen);
    }

    return res;
}

SPRESULT CCaliCmd::wcdmaDP_V3(const PC_CALI_WCDMA_TX_DYNAMIC_PWR_REQ_V2_T* req)
{
    CheckValidPointer(req);
    LogRawStrA(SPLOGLV_INFO, "WCDMA Dynamic V3:");

    L1_CALI_WCDMA_TX_DYNAMIC_PWR_REQ_V2_T L1;
    ZeroMemory((void *)&L1, sizeof(L1));

    L1.interval                = Convert32((uint32)(req->interval));

    L1.tx.dft_mode             = Convert16((uint16)(req->tx.dft_mode));
    L1.tx.dft_dcdc_value       = Convert16((uint16)(req->tx.dft_dcdc_value));
    L1.tx.dft_gain_index       = Convert16((uint16)(req->tx.dft_gain_index));
    L1.tx.dft_iq_com           = Convert16((uint16)(req->tx.dft_iq_com));
    L1.tx.path                 = Convert16((uint16)(req->tx.path));
    L1.tx.inter_cnt_change_ch  = Convert16((uint16)(req->tx.inter_cnt_change_ch));
    L1.tx.cmd_mask             = Convert16((uint16)(req->tx.cmd_mask));
    L1.tx.seq_num              = Convert16((uint16)(req->tx.seq_num));
    for (int i=0; i<MAX_WCDMA_SEQ_NUM_V2; i++)
    {
        L1.tx.seq[i].seg_num   = Convert32((uint32)(req->tx.seq[i].seg_num));
        for (int j=0; j<MAX_WCDMA_TX_SEG_NUMBER_V2; j++)
        {
            L1.tx.seq[i].seg[j].start_gain_index   = Convert16((uint16)(req->tx.seq[i].seg[j].start_gain_index));
            L1.tx.seq[i].seg[j].gain_index_step    = Convert16((uint16)(req->tx.seq[i].seg[j].gain_index_step));
            L1.tx.seq[i].seg[j].step_num           = Convert16((uint16)(req->tx.seq[i].seg[j].step_num));

            BYTE mode = (BYTE)req->tx.seq[i].seg[j].mode;
            BYTE dcdc = (BYTE)req->tx.seq[i].seg[j].dcdc_value;
            uint16 mode_dcdc = MAKEWORD(dcdc, mode);
            L1.tx.seq[i].seg[j].mode_dcdc          = Convert16((uint16)(mode_dcdc));  
            L1.tx.seq[i].seg[j].band               = Convert16((uint16)(wcdmaIoBand(req->tx.seq[i].seg[j].band)));
            L1.tx.seq[i].seg[j].uarfcn             = Convert16((uint16)(req->tx.seq[i].seg[j].uarfcn));
        }
    }

    L1.SignalCode  = Convert16(CALI_WCDMA_TX_DYNAMIC_PWR_V3_REQ);
    L1.SignalSize  = Convert16((uint16)sizeof(L1));

    DeclareDiagHeader(hd, DIAG_RF_WCDMA_F, ZERO_SUBTYPE); 

    uint32 recvLen = 0;
    SPRESULT   res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);
    if (SP_OK == res)
    {
        res = wcdmaRLT((const void* )m_diagBuff, recvLen);
    }

    return res;
}

SPRESULT CCaliCmd::wcdmaFDT_V2(const PC_CALI_WCDMA_TXRX_SEQ_REQ_V2_T* req)
{
    CheckValidPointer(req);

    LogRawStrA(SPLOGLV_INFO, "WCDMA FDT V2:");

    int i = 0, j = 0;
    L1_CALI_WCDMA_TXRX_SEQ_REQ_V2_T L1;
    ZeroMemory((void *)&L1, sizeof(L1));
    L1.interval                = Convert32((uint32)(req->interval));

    // TX
    L1.tx.dft_mode             = Convert16((uint16)(req->tx.dft_mode));
    L1.tx.dft_dcdc_value       = Convert16((uint16)(req->tx.dft_dcdc_value));
    L1.tx.dft_gain_index       = Convert16((uint16)(req->tx.dft_gain_index));
    L1.tx.dft_iq_com           = Convert16((uint16)(req->tx.dft_iq_com));
    L1.tx.path                 = Convert16((uint16)(req->tx.path));
    L1.tx.inter_cnt_change_ch  = Convert16((uint16)(req->tx.inter_cnt_change_ch));
    L1.tx.cmd_mask             = Convert16((uint16)(req->tx.cmd_mask));
    L1.tx.seq_num              = Convert16((uint16)(req->tx.seq_num));
    for (i=0; i<MAX_WCDMA_SEQ_NUM_V2; i++)
    {
        L1.tx.seq[i].seg_num   = Convert32(req->tx.seq[i].seg_num);
        for (j=0; j<MAX_WCDMA_TX_SEG_NUMBER_V2; j++)
        {
            L1.tx.seq[i].seg[j].start_gain_index   = Convert16(req->tx.seq[i].seg[j].start_gain_index);
            L1.tx.seq[i].seg[j].gain_index_step    = Convert16(req->tx.seq[i].seg[j].gain_index_step);
            L1.tx.seq[i].seg[j].step_num           = Convert16(req->tx.seq[i].seg[j].step_num);

            BYTE mode = (BYTE)req->tx.seq[i].seg[j].mode;
            BYTE dcdc = (BYTE)req->tx.seq[i].seg[j].dcdc_value;
            uint16 mode_dcdc = MAKEWORD(dcdc, mode);
            L1.tx.seq[i].seg[j].mode_dcdc          = Convert16((uint16)(mode_dcdc));  
            L1.tx.seq[i].seg[j].band               = Convert16((uint16)(wcdmaIoBand(req->tx.seq[i].seg[j].band)));
            L1.tx.seq[i].seg[j].uarfcn             = Convert16((uint16)(req->tx.seq[i].seg[j].uarfcn));
        }
    }

    // RX
    L1.rx.dft_gain_index       = Convert16((uint16)(req->rx.dft_gain_index));
    L1.rx.inter_cnt_change_ch  = Convert16((uint16)(req->rx.inter_cnt_change_ch));
    L1.rx.path                 = Convert16((uint16)(req->rx.path));
    L1.rx.seq_num              = Convert16((uint16)(req->rx.seq_num));
    for (i=0; i<MAX_WCDMA_SEQ_NUM_V2; i++)
    {
        L1.rx.seq[i].band      = Convert16((uint16)(wcdmaIoBand(req->rx.seq[i].band)));
        L1.rx.seq[i].array_num = Convert16((uint16)(req->rx.seq[i].array_num));
        for (j=0; j<MAX_WCDMA_ENTRY_PER_SEQ_NUM_V2; j++)
        {
            L1.rx.seq[i].array[j].gain_index   = Convert16((uint16)(req->rx.seq[i].array[j].gain_index));
            L1.rx.seq[i].array[j].uarfcn       = Convert16((uint16)(req->rx.seq[i].array[j].uarfcn));
        }
    }

    L1.SignalCode  = Convert16(CALI_WCDMA_TXRX_SEQ_V2_REQ);
    L1.SignalSize  = Convert16((uint16)sizeof(L1));

    DeclareDiagHeader(hd, DIAG_RF_WCDMA_F, ZERO_SUBTYPE); 

    uint32 recvLen = 0;
    SPRESULT   res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);
    if (SP_OK == res)
    {
        uint32 u32ExpSize = sizeof(L1_CALI_WCDMA_TXRX_RLT_V2_T);
        if (recvLen < u32ExpSize)
        {
            LogFmtStrA(SPLOGLV_ERROR, "Invalid response length %d < %d!", recvLen, u32ExpSize);
            return SP_E_PHONE_INVALID_LENGTH;
        }

        L1_CALI_WCDMA_TXRX_RLT_V2_T* pRLT = (L1_CALI_WCDMA_TXRX_RLT_V2_T* )m_diagBuff;
        uint32 u32State = Convert32(pRLT->result);
        if (RF_OP_SUCCESS != u32State)
        {
            LogFmtStrA(SPLOGLV_ERROR, "WCDMA operation failed, state = %d.", u32State);
            return SP_E_PHONE_INVALID_STATE;
        }
    }

    return res;
}

SPRESULT CCaliCmd::wcdmaGetFDTRxValues_V2(LPPC_CALI_WCDMA_RX_SEQ_RSSI_V2_RLT_T rlt)
{
    CheckValidPointer(rlt);

    LogRawStrA(SPLOGLV_INFO, "Get WCDMA FDT RSSI V2:");

    L1_CALI_WCDMA_REQ_T L1;
    ZeroMemory((void* )&L1, sizeof(L1));
    L1.SignalCode  = Convert16(CALI_WCDMA_RX_SEQ_RSSI_V2_REQ);
    L1.SignalSize  = Convert16((uint16)sizeof(L1));

    DeclareDiagHeader(hd, DIAG_RF_WCDMA_F, ZERO_SUBTYPE); 

    uint32 recvLen = 0;
    SPRESULT   res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);
    if (SP_OK == res)
    {
        uint32 u32ExpSize = sizeof(L1_CALI_WCDMA_RX_SEQ_RSSI_V2_RLT_T);
        if (recvLen < u32ExpSize)
        {
            LogFmtStrA(SPLOGLV_ERROR, "Invalid response length %d < %d!", recvLen, u32ExpSize);
            return SP_E_PHONE_INVALID_LENGTH;
        }

        L1_CALI_WCDMA_RX_SEQ_RSSI_V2_RLT_T* pRLT = (L1_CALI_WCDMA_RX_SEQ_RSSI_V2_RLT_T* )m_diagBuff;
        uint32 u32State = Convert32(pRLT->state);
        if (RF_OP_SUCCESS != u32State)
        {
            LogFmtStrA(SPLOGLV_ERROR, "WCDMA operation failed, state = %d.", u32State);
            return SP_E_PHONE_INVALID_STATE;
        }

        rlt->result.seq_num = Convert32(pRLT->result.seq_num);
        for (uint32 i=0; i<MAX_WCDMA_SEQ_NUM_V2; i++)
        {
            rlt->result.channel[i].array_num    = Convert32(pRLT->result.channel[i].array_num);
            for (uint32 j=0; j<MAX_WCDMA_ENTRY_PER_SEQ_NUM_V2; j++)
            {
                rlt->result.channel[i].rssi[j]  = Convert32(pRLT->result.channel[i].rssi[j]);
                if ((i < rlt->result.seq_num) && (j < rlt->result.channel[i].array_num))
                {
                    LogFmtStrA(SPLOGLV_INFO, "[%2d]: rssi[%2d] = %d (0x%X)", i, j, rlt->result.channel[i].rssi[j], rlt->result.channel[i].rssi[j]);
                }
            }
        }
    }

    return res;
}

SPRESULT CCaliCmd::wcdmaGetHDTValues_V2(LPPC_CALI_WCDMA_TX_HDT_RESULT_RLT_V2_T rlt)
{
    CheckValidPointer(rlt);

    LogRawStrA(SPLOGLV_INFO, "Get WCDMA HDET values V2:");

    L1_CALI_WCDMA_REQ_T L1;
    ZeroMemory((void* )&L1, sizeof(L1));

    L1.SignalCode  = Convert16(CALI_WCDMA_TX_SEQ_HDT_V2_REQ);
    L1.SignalSize  = Convert16((uint16)sizeof(L1));

    DeclareDiagHeader(hd, DIAG_RF_WCDMA_F, ZERO_SUBTYPE); 

    uint32 recvLen = 0;
    SPRESULT   res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);
    if (SP_OK == res)
    {
        uint32 u32ExpSize = sizeof(L1_CALI_WCDMA_TX_HDT_RESULT_RLT_V2_T);
        if (recvLen < u32ExpSize)
        {
            LogFmtStrA(SPLOGLV_ERROR, "Invalid response length %d < %d!", recvLen, u32ExpSize);
            return SP_E_PHONE_INVALID_LENGTH;
        }

        L1_CALI_WCDMA_TX_HDT_RESULT_RLT_V2_T* pRLT = (L1_CALI_WCDMA_TX_HDT_RESULT_RLT_V2_T* )m_diagBuff;
        uint32 u32State = Convert32(pRLT->state);
        if (RF_OP_SUCCESS != u32State)
        {
            LogFmtStrA(SPLOGLV_ERROR, "WCDMA operation failed, state = %d.", u32State);
            return SP_E_PHONE_INVALID_STATE;
        }

        rlt->hdt.seq_num = Convert32(pRLT->hdt.seq_num);
        for (uint32 j=0; j<rlt->hdt.seq_num; j++)
        {
            rlt->hdt.seq_result[j].array_num = pRLT->hdt.seq_result[j].array_num;
            for (uint32 i=0; i<MAX_HDT_ENTRY_PER_SEQ_V2_NUM; i++)
            {
                rlt->hdt.seq_result[j].result[i]  = Convert16(pRLT->hdt.seq_result[j].result[i]);
                LogFmtStrA(SPLOGLV_INFO, "result[%2d] = %d (0x%X)", i, rlt->hdt.seq_result[j].result[i], rlt->hdt.seq_result[j].result[i]);
            }
        }
    }

    return res;
}

SPRESULT CCaliCmd::wcdmaGetHDTValues_V3(LPPC_CALI_WCDMA_TX_HDT_RESULT_RLT_V3_T rlt)
{
    CheckValidPointer(rlt);

    LogRawStrA(SPLOGLV_INFO, "Get WCDMA HDET values V3:");

    L1_CALI_WCDMA_REQ_T L1;
    ZeroMemory((void* )&L1, sizeof(L1));

    L1.SignalCode  = Convert16(CALI_WCDMA_TX_SEQ_HDT_V3_REQ);
    L1.SignalSize  = Convert16((uint16)sizeof(L1));

    DeclareDiagHeader(hd, DIAG_RF_WCDMA_F, ZERO_SUBTYPE); 

    uint32 recvLen = 0;
    SPRESULT   res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);
    if (SP_OK == res)
    {
        uint32 u32ExpSize = sizeof(L1_CALI_WCDMA_TX_HDT_RESULT_RLT_V3_T);
        if (recvLen < u32ExpSize)
        {
            LogFmtStrA(SPLOGLV_ERROR, "Invalid response length %d < %d!", recvLen, u32ExpSize);
            return SP_E_PHONE_INVALID_LENGTH;
        }

        L1_CALI_WCDMA_TX_HDT_RESULT_RLT_V3_T* pRLT = (L1_CALI_WCDMA_TX_HDT_RESULT_RLT_V3_T* )m_diagBuff;
        uint32 u32State = Convert32(pRLT->state);
        if (RF_OP_SUCCESS != u32State)
        {
            LogFmtStrA(SPLOGLV_ERROR, "WCDMA operation failed, state = %d.", u32State);
            return SP_E_PHONE_INVALID_STATE;
        }

        rlt->hdt.seq_num = Convert32(pRLT->hdt.seq_num);
        for (uint32 j=0; j<rlt->hdt.seq_num; j++)
        {
            rlt->hdt.seq_result[j].array_num = pRLT->hdt.seq_result[j].array_num;
            for (uint32 i=0; i<MAX_HDT_ENTRY_PER_SEQ_V2_NUM; i++)
            {
                rlt->hdt.seq_result[j].result[i][0]  = Convert16(pRLT->hdt.seq_result[j].result[i][0]);
                rlt->hdt.seq_result[j].result[i][1]  = Convert16(pRLT->hdt.seq_result[j].result[i][1]);
                rlt->hdt.seq_result[j].result[i][2]  = Convert16(pRLT->hdt.seq_result[j].result[i][2]);

                LogFmtStrA(SPLOGLV_INFO, "result[%2d] = %d,%d,%d(0x%X,0x%X,0x%X)", i, 
                                                        rlt->hdt.seq_result[j].result[i][0], 
                                                        rlt->hdt.seq_result[j].result[i][1], 
                                                        rlt->hdt.seq_result[j].result[i][2],
                                                        rlt->hdt.seq_result[j].result[i][0],
                                                        rlt->hdt.seq_result[j].result[i][1],
                                                        rlt->hdt.seq_result[j].result[i][2]);
            }
        }
    }

    return res;
}

SPRESULT CCaliCmd::wcdmaGetHDTValues_V4(LPPC_CALI_WCDMA_TX_HDT_RESULT_RLT_V4_T rlt)
{
    CheckValidPointer(rlt);

    LogRawStrA(SPLOGLV_INFO, "Get WCDMA HDET values V4:");

    L1_CALI_WCDMA_REQ_T L1;
    ZeroMemory((void *)&L1, sizeof(L1));

    L1.SignalCode  = Convert16(CALI_WCDMA_TX_SEQ_HDT_V4_REQ);
    L1.SignalSize  = Convert16((uint16)sizeof(L1));

    DeclareDiagHeader(hd, DIAG_RF_WCDMA_F, ZERO_SUBTYPE); 

    uint32 recvLen = 0;
    SPRESULT   res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);
    if (SP_OK == res)
    {
        uint32 u32ExpSize = sizeof(L1_CALI_WCDMA_TX_HDT_RESULT_RLT_V4_T);
        if (recvLen < u32ExpSize)
        {
            LogFmtStrA(SPLOGLV_ERROR, "Invalid response length %d < %d!", recvLen, u32ExpSize);
            return SP_E_PHONE_INVALID_LENGTH;
        }

        L1_CALI_WCDMA_TX_HDT_RESULT_RLT_V4_T* pRLT = (L1_CALI_WCDMA_TX_HDT_RESULT_RLT_V4_T* )m_diagBuff;
        uint32 u32State = Convert32(pRLT->state);
        if (RF_OP_SUCCESS != u32State)
        {
            LogFmtStrA(SPLOGLV_ERROR, "WCDMA operation failed, state = %d.", u32State);
            return SP_E_PHONE_INVALID_STATE;
        }

        rlt->hdt.seq_num = Convert32(pRLT->hdt.seq_num);
        for (uint32 j=0; j<rlt->hdt.seq_num; j++)
        {
            rlt->hdt.seq_result[j].array_num = pRLT->hdt.seq_result[j].array_num;
            for (uint32 i=0; i<MAX_HDT_ENTRY_PER_SEQ_V4_NUM; i++)
            {
                rlt->hdt.seq_result[j].result[i][0]  = Convert16(pRLT->hdt.seq_result[j].result[i][0]);
                rlt->hdt.seq_result[j].result[i][1]  = Convert16(pRLT->hdt.seq_result[j].result[i][1]);
                rlt->hdt.seq_result[j].result[i][2]  = Convert16(pRLT->hdt.seq_result[j].result[i][2]);

                LogFmtStrA(SPLOGLV_INFO, "result[%2d] = %d,%d,%d(0x%X,0x%X,0x%X)", i, 
                                                    rlt->hdt.seq_result[j].result[i][0], 
                                                    rlt->hdt.seq_result[j].result[i][1], 
                                                    rlt->hdt.seq_result[j].result[i][2],
                                                    rlt->hdt.seq_result[j].result[i][0],
                                                    rlt->hdt.seq_result[j].result[i][1],
                                                    rlt->hdt.seq_result[j].result[i][2]);
            }
        }
    }

    return res;
}

SPRESULT CCaliCmd::wcdmaGetHDTValues_V6(LPPC_CALI_WCDMA_TX_HDT_RESULT_RLT_V6_T rlt)
{
	CheckValidPointer(rlt);
	LogRawStrA(SPLOGLV_INFO, "wcdmaGetHDTValues_V6:");
	L1_CALI_WCDMA_REQ_T  L1;
	ZeroMemory((void *)&L1,    sizeof(L1));
	L1.SignalCode  = Convert16((uint16)CALI_WCDMA_TX_SEQ_HDT_V6_REQ);
	L1.SignalSize  = Convert16((uint16)sizeof(L1_CALI_WCDMA_REQ_T));
	DeclareDiagHeader(hd, DIAG_RF_WCDMA_F, ZERO_SUBTYPE); 
	uint32 recvLen = 0;
	SPRESULT   res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);
	if (SP_OK == res)
	{   
		L1_CALI_WCDMA_TX_HDT_SEQ_RLT_V6_T *pRlt = (L1_CALI_WCDMA_TX_HDT_SEQ_RLT_V6_T*)m_diagBuff;
		if(pRlt->data.hdt_num > 1000)
		{
			LogFmtStrA(SPLOGLV_ERROR, "[wcdmaGetHDTValues_V6] Invalid hdt_num = %d!",  pRlt->data.hdt_num );
			return SP_E_PHONE_INVALID_LENGTH;
		}
		unsigned long length =  sizeof(PC_CALI_WCDMA_TX_HDT_RESULT_RLT_V6_T) - (1000 - pRlt->data.hdt_num)*6;
		if (recvLen < length)
		{
			LogFmtStrA(SPLOGLV_ERROR, "[wcdmaGetHDTValues_V6] hdt_num = %d, Invalid response length %d!", pRlt->data.hdt_num, recvLen);
			return SP_E_PHONE_INVALID_LENGTH;
		}
		uint32 state = Convert32((uint32)(pRlt->state));
		if (RF_OP_SUCCESS != state)
		{
			LogFmtStrA(SPLOGLV_ERROR, "WCDMA operation failed, state = %d.", state);
			return SP_E_PHONE_INVALID_STATE;
		}
		memcpy((void*)rlt,(void*)&pRlt->data,length);
	}
	return res;
}

SPRESULT CCaliCmd::wcdmaFDT_V4(const PC_CALI_WCDMA_TXRX_SEQ_REQ_V2_T* req)
{
    CheckValidPointer(req);

    LogRawStrA(SPLOGLV_INFO, "WCDMA FDT V4:");

    int i = 0;
    int j = 0;
    L1_CALI_WCDMA_TXRX_SEQ_REQ_V2_T L1;
    ZeroMemory((void* )&L1,  sizeof(L1));

    L1.SignalCode  = Convert16(CALI_WCDMA_TXRX_SEQ_V4_REQ);
    L1.SignalSize  = Convert16((uint16)sizeof(L1));

    L1.interval                = Convert32((uint32)(req->interval));

    // TX
    L1.tx.dft_mode             = Convert16((uint16)(req->tx.dft_mode));
    L1.tx.dft_dcdc_value       = Convert16((uint16)(req->tx.dft_dcdc_value));
    L1.tx.dft_gain_index       = Convert16((uint16)(req->tx.dft_gain_index));
    L1.tx.dft_iq_com           = Convert16((uint16)(req->tx.dft_iq_com));
    L1.tx.path                 = Convert16((uint16)(req->tx.path));
    L1.tx.inter_cnt_change_ch  = Convert16((uint16)(req->tx.inter_cnt_change_ch));
    L1.tx.cmd_mask             = Convert16((uint16)(req->tx.cmd_mask));
    L1.tx.seq_num              = Convert16((uint16)(req->tx.seq_num));
    for (i=0; i<MAX_WCDMA_SEQ_NUM_V2; i++)
    {
        L1.tx.seq[i].seg_num   = Convert32((uint32)(req->tx.seq[i].seg_num));
        for (j=0; j<MAX_WCDMA_TX_SEG_NUMBER_V2; j++)
        {
            L1.tx.seq[i].seg[j].start_gain_index   = Convert16((uint16)(req->tx.seq[i].seg[j].start_gain_index));
            L1.tx.seq[i].seg[j].gain_index_step    = Convert16((uint16)(req->tx.seq[i].seg[j].gain_index_step));
            L1.tx.seq[i].seg[j].step_num           = Convert16((uint16)(req->tx.seq[i].seg[j].step_num));

            BYTE mode = (BYTE)req->tx.seq[i].seg[j].mode;
            BYTE dcdc = (BYTE)req->tx.seq[i].seg[j].dcdc_value;
            uint16 mode_dcdc = MAKEWORD(dcdc, mode);
            L1.tx.seq[i].seg[j].mode_dcdc          = Convert16((uint16)(mode_dcdc));  
            L1.tx.seq[i].seg[j].band               = Convert16((uint16)(wcdmaIoBand(req->tx.seq[i].seg[j].band)));
            L1.tx.seq[i].seg[j].uarfcn             = Convert16((uint16)(req->tx.seq[i].seg[j].uarfcn));
        }
    }

    // RX
    L1.rx.dft_gain_index       = Convert16((uint16)(req->rx.dft_gain_index));
    L1.rx.inter_cnt_change_ch  = Convert16((uint16)(req->rx.inter_cnt_change_ch));
    L1.rx.path                 = Convert16((uint16)(req->rx.path));
    L1.rx.seq_num              = Convert16((uint16)(req->rx.seq_num));
    for (i=0; i<MAX_WCDMA_SEQ_NUM_V2; i++)
    {
        L1.rx.seq[i].band      = Convert16((uint16)(wcdmaIoBand(req->rx.seq[i].band)));
        L1.rx.seq[i].array_num = Convert16((uint16)(req->rx.seq[i].array_num));
        for (j=0; j<MAX_WCDMA_ENTRY_PER_SEQ_NUM_V2; j++)
        {
            L1.rx.seq[i].array[j].gain_index   = Convert16((uint16)(req->rx.seq[i].array[j].gain_index));
            L1.rx.seq[i].array[j].uarfcn       = Convert16((uint16)(req->rx.seq[i].array[j].uarfcn));
        }
    }

    DeclareDiagHeader(hd, DIAG_RF_WCDMA_F, ZERO_SUBTYPE); 

    uint32 recvLen = 0;
    SPRESULT   res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);
    if (SP_OK == res)
    {
        uint32 u32ExpSize = sizeof(L1_CALI_WCDMA_TXRX_RLT_V2_T);
        if (recvLen < u32ExpSize)
        {
            LogFmtStrA(SPLOGLV_ERROR, "Invalid response length %d < %d!", recvLen, u32ExpSize);
            return SP_E_PHONE_INVALID_LENGTH;
        }

        L1_CALI_WCDMA_TXRX_RLT_V2_T* pRLT = (L1_CALI_WCDMA_TXRX_RLT_V2_T* )m_diagBuff;
        uint32 u32State = Convert32(pRLT->result);
        if (RF_OP_SUCCESS != u32State)
        {
            LogFmtStrA(SPLOGLV_ERROR, "WCDMA operation failed, state = %d.", u32State);
            return SP_E_PHONE_INVALID_STATE;
        }
    }
    
    return res;
}

SPRESULT CCaliCmd::wcdmaFDT_V6(const PC_CALI_WCDMA_TXRX_SEQ_REQ_V6_T* req)
{
	CheckValidPointer(req);
	LogRawStrA(SPLOGLV_INFO, "wcdmaFDT_V6:");
	L1_CALI_WCDMA_TXRX_SEQ_V6_REQ_T L1;
	ZeroMemory((void *)&L1, sizeof(L1));
	memcpy(&L1.data, req,sizeof(L1.data));
	int nArraynum = 0;
	for(int i = 0; req->band[i] != 0; i ++)
	{
		nArraynum += req->tx.arraycountperband[i];
	}
    L1.SignalCode  = Convert16((uint16)CALI_WCDMA_TXRX_SEQ_V6_REQ);
    L1.SignalSize  = Convert16((uint16)(sizeof(L1_CALI_WCDMA_TXRX_SEQ_V6_REQ_T) - (1000 - nArraynum)*sizeof(PC_CALI_WCDMA_TX_ENTRY_V6_T)));
	DeclareDiagHeader(hd, DIAG_RF_WCDMA_F, ZERO_SUBTYPE); 
	uint32 recvLen = 0;
	SPRESULT   res = SendAndRecv(hd, (const void* )&L1, L1.SignalSize, hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);
	if (SP_OK == res)
	{
		if (recvLen < sizeof(L1_CALI_WCDMA_TXRX_SEQ_RLT_V6_T))
		{
			LogFmtStrA(SPLOGLV_ERROR, "Invalid response length %d < %d!", recvLen, sizeof(L1_CALI_WCDMA_TXRX_SEQ_RLT_V6_T));
			return SP_E_PHONE_INVALID_LENGTH;
		}
		L1_CALI_WCDMA_TXRX_SEQ_RLT_V6_T *pRLT = (L1_CALI_WCDMA_TXRX_SEQ_RLT_V6_T *)m_diagBuff;
		uint32 state = Convert32((uint32)(pRLT->result));
		if (RF_OP_SUCCESS != state)
		{
			LogFmtStrA(SPLOGLV_ERROR, "WCDMA operation failed, state = %d.", state);
			return SP_E_PHONE_INVALID_STATE;
		}
	}
	return res;
}

SPRESULT CCaliCmd::wcdmaGetFDTRxValues_V4(LPPC_CALI_WCDMA_RX_SEQ_RSSI_V2_RLT_T rlt)
{
    CheckValidPointer(rlt);

    LogRawStrA(SPLOGLV_INFO, "Get WCDMA FDT RSSI V4:");

    L1_CALI_WCDMA_REQ_T L1;
    ZeroMemory((void* )&L1, sizeof(L1));

    L1.SignalCode  = Convert16(CALI_WCDMA_RX_SEQ_RSSI_V4_REQ);
    L1.SignalSize  = Convert16((uint16)sizeof(L1));

    DeclareDiagHeader(hd, DIAG_RF_WCDMA_F, ZERO_SUBTYPE); 

    uint32 recvLen = 0;
    SPRESULT   res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);
    if (SP_OK == res)
    {
        uint32 u32ExpSize = sizeof(L1_CALI_WCDMA_RX_SEQ_RSSI_V2_RLT_T);
        if (recvLen < u32ExpSize)
        {
            LogFmtStrA(SPLOGLV_ERROR, "Invalid response length %d < %d!", recvLen, u32ExpSize);
            return SP_E_PHONE_INVALID_LENGTH;
        }

        L1_CALI_WCDMA_RX_SEQ_RSSI_V2_RLT_T* pRLT = (L1_CALI_WCDMA_RX_SEQ_RSSI_V2_RLT_T* )m_diagBuff;
        uint32 u32State = Convert32(pRLT->state);
        if (RF_OP_SUCCESS != u32State)
        {
            LogFmtStrA(SPLOGLV_ERROR, "WCDMA operation failed, state = %d.", u32State);
            return SP_E_PHONE_INVALID_STATE;
        }

        rlt->result.seq_num = Convert32(pRLT->result.seq_num);
        for (uint32 i=0; i<MAX_WCDMA_SEQ_NUM_V2; i++)
        {
            rlt->result.channel[i].array_num    = Convert32(pRLT->result.channel[i].array_num);
            for (uint32 j=0; j<MAX_WCDMA_ENTRY_PER_SEQ_NUM_V2; j++)
            {
                rlt->result.channel[i].rssi[j]  = Convert32(pRLT->result.channel[i].rssi[j]);
                if ((i < rlt->result.seq_num) && (j < rlt->result.channel[i].array_num))
                {
                    LogFmtStrA(SPLOGLV_INFO, "[%2d]: rssi[%2d] = %d (0x%X)", i, j, rlt->result.channel[i].rssi[j], rlt->result.channel[i].rssi[j]);
                }
            }
        }
    }
    
    return res;
}

SPRESULT CCaliCmd::wcdmaGetFDTRxValues_V6(LPPC_CALI_WCDMA_RX_SEQ_RSSI_V6_RLT_T rlt)
{
	CheckValidPointer(rlt);
	LogRawStrA(SPLOGLV_INFO, "wcdmaGetFDTRxValues_V6:");
	L1_CALI_WCDMA_REQ_T L1;
	ZeroMemory((void *)&L1,    sizeof(L1));
	L1.SignalCode  = Convert16((uint16)CALI_WCDMA_RX_SEQ_RSSI_V6_REQ);
	L1.SignalSize  = Convert16((uint16)sizeof(L1_CALI_WCDMA_REQ_T));
	DeclareDiagHeader(hd, DIAG_RF_WCDMA_F, ZERO_SUBTYPE); 
	uint32 recvLen = 0;
	SPRESULT   res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);
	if (SP_OK == res)
	{   
		L1_CALI_WCDMA_RX_SEQ_RSSI_RLT_V6_T *pRLT = (L1_CALI_WCDMA_RX_SEQ_RSSI_RLT_V6_T *)m_diagBuff;
		if(pRLT->data.rxrssi_num > 1000)
		{
			LogFmtStrA(SPLOGLV_ERROR, "[wcdmaGetFDTRxValues_V6] rx rssi num = %d bigger than 1000!", pRLT->data.rxrssi_num);
			return SP_E_PHONE_INVALID_LENGTH;
		}
		uint32 length = sizeof(PC_CALI_WCDMA_RX_SEQ_RSSI_V6_RLT_T) - (1000 - pRLT->data.rxrssi_num)*4;
		if (recvLen < length)
		{
			LogFmtStrA(SPLOGLV_ERROR, "[wcdmaGetFDTRxValues_V6] rx rssi num = %d, Invalid response length %d!", pRLT->data.rxrssi_num, recvLen);
			return SP_E_PHONE_INVALID_LENGTH;
		}
		uint32 state = Convert32((uint32)(pRLT->state));
		if (RF_OP_SUCCESS != state)
		{
			LogFmtStrA(SPLOGLV_ERROR, "WCDMA operation failed, state = %d.", state);
			return SP_E_PHONE_INVALID_STATE;
		}
		memcpy((void*)rlt,(void*)&pRLT->data,length);
	}
	return res;
}

uint32 CCaliCmd::wcdmaGetExpCalNvSize(WCDMA_CAL_NV_TYPE_E eNvType)
{
    uint32 u32ExpNvSize = 0;
    switch(eNvType)
    {
    case WCDMA_CAL_NV_TYPE_VERSION:
        u32ExpNvSize = 2;
        break;
    case WCDMA_CAL_NV_TYPE_CAL_FLAG:
        u32ExpNvSize = 2;
        break;
    case WCDMA_CAL_NV_TYPE_AFC:
        u32ExpNvSize = sizeof(W_afc_cal_1950M_t);
        break;
    case WCDMA_CAL_NV_TYPE_BAND_INFOR:
        u32ExpNvSize = sizeof(CAL_BAND_INFOR);
        break;
    case WCDMA_CAL_NV_TYPE_AGC_1stBAND:
    case WCDMA_CAL_NV_TYPE_AGC_2ndBAND:
    case WCDMA_CAL_NV_TYPE_AGC_3rdBAND:
    case WCDMA_CAL_NV_TYPE_AGC_4thBAND:
    case WCDMA_CAL_NV_TYPE_AGC_5veBAND:
        u32ExpNvSize = sizeof(CAL_AGC);
        break;
    case WCDMA_CAL_NV_TYPE_APC_1stBAND:
    case WCDMA_CAL_NV_TYPE_APC_2ndBAND:
    case WCDMA_CAL_NV_TYPE_APC_3rdBAND:
    case WCDMA_CAL_NV_TYPE_APC_4thBAND:
    case WCDMA_CAL_NV_TYPE_APC_5veBAND:
        u32ExpNvSize = sizeof(CAL_APC_HDET);
        break;
    case WCDMA_CAL_NV_TYPE_AGC_DIV_1stBAND:
    case WCDMA_CAL_NV_TYPE_AGC_DIV_2ndBAND:
    case WCDMA_CAL_NV_TYPE_AGC_DIV_3rdBAND:
    case WCDMA_CAL_NV_TYPE_AGC_DIV_4thBAND:
    case WCDMA_CAL_NV_TYPE_AGC_DIV_5veBAND:
        u32ExpNvSize = sizeof(CAL_DIV);
        break;
    case WCDMA_CAL_COMANCHE_NV_TYPE_RX_FILTER_PRI_1stBAND:
    case WCDMA_CAL_COMANCHE_NV_TYPE_RX_FILTER_PRI_2ndBAND:
    case WCDMA_CAL_COMANCHE_NV_TYPE_RX_FILTER_PRI_3rdBAND:
    case WCDMA_CAL_COMANCHE_NV_TYPE_RX_FILTER_PRI_4thBAND:
    case WCDMA_CAL_COMANCHE_NV_TYPE_RX_FILTER_PRI_5veBAND:
        u32ExpNvSize = sizeof(comanche_w_rx_filter_cal_oneband_t);
        break;
    case WCDMA_CAL_NV_TYPE_COM_AFC:
        u32ExpNvSize = sizeof(AFC_CALI_NV);
        break;
    case WCDMA_CAL_NV_TYPE_ADC_FLAG:
        u32ExpNvSize = sizeof(uint32);
        break;
    default:
        break;
    }

    return u32ExpNvSize;
}

void CCaliCmd::wcdmaCalNvEndianConv(WCDMA_CAL_NV_TYPE_E eNvType, uint8* lpData, uint32 u32Size)
{
    DWORD dwLowLvEndian = SP_LITTLE_ENDIAN;
    GetProperty(SP_ATTR_ENDIAN, 0, (LPVOID)&dwLowLvEndian);
    if (SP_LITTLE_ENDIAN == dwLowLvEndian)
    {
        return ;
    }

    switch(eNvType)
    {
    case WCDMA_CAL_NV_TYPE_VERSION:
    case WCDMA_CAL_NV_TYPE_AFC:
    case WCDMA_CAL_NV_TYPE_APC_1stBAND:
    case WCDMA_CAL_NV_TYPE_APC_2ndBAND:
    case WCDMA_CAL_NV_TYPE_APC_3rdBAND:
    case WCDMA_CAL_NV_TYPE_APC_4thBAND:
    case WCDMA_CAL_NV_TYPE_APC_5veBAND:
    case WCDMA_CAL_NV_TYPE_CAL_FLAG:
        {
            Convert16(lpData, u32Size);
        }
        break;

    case WCDMA_CAL_NV_TYPE_AGC_1stBAND:
    case WCDMA_CAL_NV_TYPE_AGC_2ndBAND:
    case WCDMA_CAL_NV_TYPE_AGC_3rdBAND:
    case WCDMA_CAL_NV_TYPE_AGC_4thBAND:
    case WCDMA_CAL_NV_TYPE_AGC_5veBAND:
        {
            CAL_AGC* lpAgc = (CAL_AGC* )lpData;
            for (int i=0; i<25; i++)
            {
                W_agc_params_onefreq_t* lpFreq = &lpAgc->freq[i];
                Convert16((uint8* ) lpFreq->noise_power, sizeof(lpFreq->noise_power));
                Convert16((uint8* )&lpFreq->lna_switch_index, sizeof(lpFreq->lna_switch_index));
                Convert16((uint8* )&lpFreq->relative_powerx64_on, sizeof(lpFreq->relative_powerx64_on));
                Convert16((uint8* )&lpFreq->relative_powerx64_off, sizeof(lpFreq->relative_powerx64_off));
                Convert16((uint8* ) lpFreq->iq_rssi, sizeof(lpFreq->iq_rssi));
            }
        }
        break;

    case WCDMA_CAL_NV_TYPE_AGC_DIV_1stBAND:
    case WCDMA_CAL_NV_TYPE_AGC_DIV_2ndBAND:
    case WCDMA_CAL_NV_TYPE_AGC_DIV_3rdBAND:
    case WCDMA_CAL_NV_TYPE_AGC_DIV_4thBAND:
    case WCDMA_CAL_NV_TYPE_AGC_DIV_5veBAND:
        {
            CAL_DIV* lpAgc = (CAL_DIV* )lpData;
            for (int i=0; i<10; i++)
            {
                W_agc_params_onefreq_t* lpFreq = &lpAgc->freq[i];
                Convert16((uint8* )lpFreq->noise_power, sizeof(lpFreq->noise_power));
                Convert16((uint8* )lpFreq->iq_rssi,     sizeof(lpFreq->iq_rssi));
            }
        }
        break;
    case WCDMA_CAL_COMANCHE_NV_TYPE_RX_FILTER_PRI_1stBAND:
    case WCDMA_CAL_COMANCHE_NV_TYPE_RX_FILTER_PRI_2ndBAND:
    case WCDMA_CAL_COMANCHE_NV_TYPE_RX_FILTER_PRI_3rdBAND:
    case WCDMA_CAL_COMANCHE_NV_TYPE_RX_FILTER_PRI_4thBAND:
    case WCDMA_CAL_COMANCHE_NV_TYPE_RX_FILTER_PRI_5veBAND:
        {
            comanche_w_rx_filter_cal_oneband_t* lpICI = (comanche_w_rx_filter_cal_oneband_t* )lpData;
            for (int i=0; i<10; i++)
            {
                comanche_w_rx_filter_cal_onefreq_t* lpFreq = &lpICI->freq[i];
                Convert16((uint8* )lpFreq->start_uarfcn,  sizeof(lpFreq->start_uarfcn));
                Convert16((uint8* )lpFreq->end_uarfcn,    sizeof(lpFreq->end_uarfcn));
                Convert16((uint8* )lpFreq->real_coeff,    sizeof(lpFreq->real_coeff));
                Convert16((uint8* )lpFreq->image_coeff,   sizeof(lpFreq->image_coeff));
            }
        }
        break;

    default:
        break;
    }
}

uint32 CCaliCmd::wcdmaGetExpDLNvSize(WCDMA_DL_NV_TYPE eNvType)
{
    uint32 u32ExpNvSize = 0;
    switch(eNvType)
    {
    case WCDMA_DOWNLOAD_NV_INFOR_BAND_SEQ:
        u32ExpNvSize = sizeof(DOL_BAND_INFOR);
        break;
    case WCDMA_DOWNLOAD_NV_INFOR_CAL_CONFIG:
        u32ExpNvSize = sizeof(DOL_PA_APT_HDET);
        break;
    case WCDMA_DOWNLOAD_NV_INFOR_TEMP_COMP:
        u32ExpNvSize = sizeof(sr3130_apc_temp_compensation_t);
        break;
    case WCDMA_DOWNLOAD_NV_INFOR_TS_COMP:
        u32ExpNvSize = sizeof(sr3130_gainSwitch_Pwr_Ts_compensation_t);
        break;
    case WCDMA_DOWNLOAD_NV_INFOR_PHASE_COMP:
        u32ExpNvSize = sizeof(sr3130_gainSwitch_phase_compensation_t);
        break;
    case WCDMA_DOWNLOAD_NV_INFOR_VOL_COMP:
        u32ExpNvSize = sizeof(sr3130_apc_voltage_compensation_t);
        break;
    case WCDMA_DOWNLOAD_NV_INFOR_TRX_PATH:
        u32ExpNvSize = sizeof(uint16);
        break;
    case WCDMA_DOWNLOAD_NV_INFOR_FIX_POWER_CONFIG:
        u32ExpNvSize = sizeof(sr3130_fix_max_power_config_t);
        break;
    case WCDMA_DOWNLOAD_NV_INFOR_RSCP_COMP:
        u32ExpNvSize = sizeof(w_rscp_temp_compensation_t);
        break;
    case WCDMA_DOWMLOAD_NV_INFOR_HDT_TEMP_COMP:
        u32ExpNvSize = sizeof(sr3130_hdt_temp_comp_config_t);
        break;
    case WCDMA_DOWMLOAD_NV_INFOR_TRANCEIVER_TEMP_INDICATOR:
        u32ExpNvSize = sizeof(sr3130_tranceiver_temp_indicator_t);
        break;
    case WCDMA_DOWNLOAD_NV_INFOR_CAL_CONFIG2:
        u32ExpNvSize = sizeof(w_cal_config2_all_t);
        break;
    case WCDMA_DOWNLOAD_NV_INFOR_CALI_SETTING:
        u32ExpNvSize = sizeof(w_band_cali_setting_t);
        break;
	case WCDMA_DOWNLOAD_NV_INFOR_TXCAL_CONFIG:
		u32ExpNvSize = sizeof(w_band_cali_txgain_t);
		break;
    default:
        break;
    }

    return u32ExpNvSize;
}

void CCaliCmd::wcdmaDLNvEndianConv(WCDMA_DL_NV_TYPE eNvType, uint8* lpData, uint32 u32Size)
{
    DWORD dwLowLvEndian = SP_LITTLE_ENDIAN;
    GetProperty(SP_ATTR_ENDIAN, 0, (LPVOID)&dwLowLvEndian);
    if (SP_LITTLE_ENDIAN == dwLowLvEndian)
    {
        return ;
    }

    switch(eNvType)
    {
    case WCDMA_DOWNLOAD_NV_INFOR_BAND_SEQ:
    case WCDMA_DOWNLOAD_NV_INFOR_PHASE_COMP:
    case WCDMA_DOWNLOAD_NV_INFOR_VOL_COMP:
    case WCDMA_DOWNLOAD_NV_INFOR_TRX_PATH:
    case WCDMA_DOWNLOAD_NV_INFOR_RSCP_COMP:
    case WCDMA_DOWMLOAD_NV_INFOR_HDT_TEMP_COMP:
        Convert16(lpData, u32Size);
        break;
    default:
        break;
    }
}

SPRESULT CCaliCmd::wcdmaLoadCalNV(PC_WCDMA_NV_PARAM_T* lpNV)
{
    CheckValidPointer(lpNV);

    LogFmtStrA(SPLOGLV_INFO, "Load WCDMA calibration NV: nv = %d, size = %d", lpNV->eNvType, lpNV->nDataSize);

    uint32 u32ExpNvSize = wcdmaGetExpCalNvSize(static_cast<WCDMA_CAL_NV_TYPE_E>(lpNV->eNvType));
    if (lpNV->nDataSize != u32ExpNvSize)
    {
        LogFmtStrA(SPLOGLV_WARN, "Incorrect NV[%d] size %d, adjust size to %d.", lpNV->eNvType, lpNV->nDataSize, u32ExpNvSize);
        lpNV->nDataSize = static_cast<uint16>(u32ExpNvSize);
    }

    if (lpNV->nDataSize > WCDMA_MAX_NV_DATA_SIZE)
    {
        LogFmtStrA(SPLOGLV_ERROR, "Too big NV[%d] size! %d < %d", lpNV->eNvType, lpNV->nDataSize, WCDMA_MAX_NV_DATA_SIZE);
        return SP_E_PHONE_INVALID_PARAMETER;
    }

    L1_WCDMA_NV_DATA_READ_T L1;
    ZeroMemory((void* )&L1, sizeof(L1));
    L1.eNvType     = Convert16((uint16)lpNV->eNvType);
    L1.nDataSize   = Convert16((uint16)lpNV->nDataSize);    

    DeclareDiagHeader(hd, DIAG_WCDMA_NV, WCDMA_NV_READ); 

    uint32 recvLen = 0;
    SPRESULT   res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);
    if (SP_OK == res)
    {
        uint32 u32ExpSize = sizeof(L1_WCDMA_NV_DATA_READ_T) + lpNV->nDataSize;
        if (recvLen < u32ExpSize)
        {
            LogFmtStrA(SPLOGLV_ERROR, "Invalid response length %d < %d!", recvLen, u32ExpSize);
            return SP_E_PHONE_INVALID_LENGTH;
        }

        L1_WCDMA_NV_DATA_WRITE_T* lpData = (L1_WCDMA_NV_DATA_WRITE_T* )m_diagBuff;
        wcdmaCalNvEndianConv(static_cast<WCDMA_CAL_NV_TYPE_E>(lpNV->eNvType),  (uint8* )lpData->nData, lpNV->nDataSize);
        CopyMemory((void* )lpNV->nData, (const void* )lpData->nData, lpNV->nDataSize);
    }

    return res;
}

SPRESULT CCaliCmd::wcdmaSaveCalNV(const PC_WCDMA_NV_PARAM_T* lpNV)
{
    CheckValidPointer(lpNV);

    LogFmtStrA(SPLOGLV_INFO, "Save WCDMA calibration NV: nv = %d, size = %d", lpNV->eNvType, lpNV->nDataSize);

    uint32 u32ExpNvSize = wcdmaGetExpCalNvSize(static_cast<WCDMA_CAL_NV_TYPE_E>(lpNV->eNvType));
    if (lpNV->nDataSize > u32ExpNvSize)
    {
        LogFmtStrA(SPLOGLV_ERROR, "Incorrect NV[%d] size %d <> %d!", lpNV->eNvType, lpNV->nDataSize, u32ExpNvSize);
        return SP_E_PHONE_INVALID_PARAMETER;
    }

    if (lpNV->nDataSize > WCDMA_MAX_NV_DATA_SIZE)
    {
        LogFmtStrA(SPLOGLV_ERROR, "Too big NV[%d] size! %d < %d", lpNV->eNvType, lpNV->nDataSize, WCDMA_MAX_NV_DATA_SIZE);
        return SP_E_PHONE_INVALID_PARAMETER;
    }

    L1_WCDMA_NV_DATA_WRITE_T L1;
    ZeroMemory((void* )&L1, sizeof(L1));

    L1.eNvType     = Convert16((uint16)lpNV->eNvType);
    L1.nDataSize   = Convert16((uint16)lpNV->nDataSize);  

    CopyMemory((void* )L1.nData,(const void* )lpNV->nData, lpNV->nDataSize);
    wcdmaCalNvEndianConv(static_cast<WCDMA_CAL_NV_TYPE_E>(lpNV->eNvType), (uint8* )L1.nData, lpNV->nDataSize);

    DeclareDiagHeader(hd, DIAG_WCDMA_NV, WCDMA_NV_WRITE); 

    SPRESULT ret =  SendAndRecv(hd, (const void* )&L1, (sizeof(L1_WCDMA_NV_DATA_READ_T) + lpNV->nDataSize), hd, NULL, 0, NULL, m_dwTimeOut);

    if (ret == SP_OK && m_pContainer != NULL)
    {
        std::wstring strKey= CwcdmaUtility::GetShareKey(CwcdmaUtility::Cal, lpNV->eNvType);
        m_pContainer->SetValue(strKey.c_str(), lpNV->nData, lpNV->nDataSize * sizeof(lpNV->nData[0]));
    }

    return ret;
}

SPRESULT CCaliCmd::wcdmaSaveToFlash(uint32 u32TimeOut)
{
    LogFmtStrA(SPLOGLV_INFO, "%s: TimeOut = %d", __FUNCTION__, u32TimeOut);

    DeclareDiagHeader(hd, DIAG_WCDMA_NV, WCDMA_NV_SAVE_TO_FLASH); 
    uint32 recvLen = 0;
    SPRESULT   res = SendAndRecv(hd, NULL, 0, hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvLen, u32TimeOut);
    if (SP_OK == res)
    {
        uint32 u32ExpSize = sizeof(L1_CALI_WCDMA_SAVETOFLASH_RLT_T);
        if (recvLen < u32ExpSize)
        {
            LogFmtStrA(SPLOGLV_ERROR, "Invalid response length %d < %d!", recvLen, u32ExpSize);
            return SP_E_PHONE_INVALID_LENGTH;
        }

        L1_CALI_WCDMA_SAVETOFLASH_RLT_T* pRLT = (L1_CALI_WCDMA_SAVETOFLASH_RLT_T* )m_diagBuff;
        uint32 u32State = Convert32(pRLT->result);
        if (RF_OP_SUCCESS != u32State)
        {
            LogFmtStrA(SPLOGLV_ERROR, "WCDMA SaveToFlash failed, state = %d.", u32State);
            return SP_E_PHONE_INVALID_STATE;
        }
    }

    return res;
}

SPRESULT CCaliCmd::wcdmaSaveAfcToFlash(uint32 u32TimeOut)
{
    LogFmtStrA(SPLOGLV_INFO, "%s: TimeOut = %d", __FUNCTION__, u32TimeOut);

    DeclareDiagHeader(hd, DIAG_WCDMA_NV, WCDMA_NV_SAVE_AFC_TO_FLASH); 
    uint32 recvLen = 0;
    SPRESULT   res = SendAndRecv(hd, NULL, 0, hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvLen, u32TimeOut);
    if (SP_OK == res)
    {
        uint32 u32ExpSize = sizeof(L1_CALI_WCDMA_SAVETOFLASH_RLT_T);
        if (recvLen < u32ExpSize)
        {
            LogFmtStrA(SPLOGLV_ERROR, "Invalid response length %d < %d!", recvLen, u32ExpSize);
            return SP_E_PHONE_INVALID_LENGTH;
        }

        L1_CALI_WCDMA_SAVETOFLASH_RLT_T* pRLT = (L1_CALI_WCDMA_SAVETOFLASH_RLT_T* )m_diagBuff;
        uint32 u32State = Convert32(pRLT->result);
        if (RF_OP_SUCCESS != u32State)
        {
            LogFmtStrA(SPLOGLV_ERROR, "WCDMA SaveToFlash failed, state = %d.", u32State);
            return SP_E_PHONE_INVALID_STATE;
        }
    }

    return res;
}


SPRESULT CCaliCmd::wcdmaLoadDLNV(PC_WCDMA_NV_PARAM_T *lpNV)
{
    CheckValidPointer(lpNV);

    LogFmtStrA(SPLOGLV_INFO, "Load WCDMA download NV: nv = %d, size = %d", lpNV->eNvType, lpNV->nDataSize);
    
    uint32 u32ExpNvSize = wcdmaGetExpDLNvSize(static_cast<WCDMA_DL_NV_TYPE>(lpNV->eNvType));
    if (lpNV->nDataSize != u32ExpNvSize)
    {
        LogFmtStrA(SPLOGLV_WARN, "Incorrect NV[%d] size %d, adjust size to %d.", lpNV->eNvType, lpNV->nDataSize, u32ExpNvSize);
        lpNV->nDataSize = static_cast<uint16>(u32ExpNvSize);
    }

    if (lpNV->nDataSize > MAX_WCDMA_DL_NV_SIZE)
    {
        LogFmtStrA(SPLOGLV_ERROR, "Too big NV[%d] size! %d < %d", lpNV->eNvType, lpNV->nDataSize, MAX_WCDMA_DL_NV_SIZE);
        return SP_E_PHONE_INVALID_PARAMETER;
    }

    L1_CALI_WCDMA_READ_CONFIG_INFOR_REQ_T L1;
    ZeroMemory((void* )&L1, sizeof(L1));

    L1.SignalCode  = Convert16(CALI_WCDMA_READ_CONFIG_INFOR);
    L1.SignalSize  = Convert16((uint16)sizeof(L1));

    L1.infor_type  = Convert32((uint32)lpNV->eNvType);
    L1.infor_len   = Convert32((uint32)lpNV->nDataSize);    

    DeclareDiagHeader(hd, DIAG_RF_WCDMA_F, ZERO_SUBTYPE); 

    uint32 recvLen = 0;
    SPRESULT   res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);
    if (SP_OK == res)
    {
        uint32 u32ExpSize = wcdmaGetExpDLNvSize(static_cast<WCDMA_DL_NV_TYPE>(lpNV->eNvType));//sizeof(L1_CALI_WCDMA_READ_CONFIG_INFOR_RLT_T);
        if (recvLen < u32ExpSize)
        {
            LogFmtStrA(SPLOGLV_ERROR, "Invalid response length %d < %d!", recvLen, u32ExpSize);
            return SP_E_PHONE_INVALID_LENGTH;
        }

        L1_CALI_WCDMA_READ_CONFIG_INFOR_RLT_T* pRLT = (L1_CALI_WCDMA_READ_CONFIG_INFOR_RLT_T* )m_diagBuff;
        uint32 u32State = Convert32(pRLT->result);
        if (RF_OP_SUCCESS != u32State)
        {
            LogFmtStrA(SPLOGLV_ERROR, "WCDMA Load DL NV failed, state = %d.", u32State);
            return SP_E_PHONE_INVALID_STATE;
        }

        uint32 u32Type = Convert32(pRLT->infor_type);
        if (u32Type != lpNV->eNvType)
        {
            LogFmtStrA(SPLOGLV_ERROR, "Response NV type %d is not equal to request NV type %d!", u32Type, lpNV->eNvType);
            return SP_E_PHONE_INVALID_DATA;
        }

        wcdmaDLNvEndianConv(static_cast<WCDMA_DL_NV_TYPE>(lpNV->eNvType), (uint8* )pRLT->infor_data, u32ExpNvSize);
        CopyMemory((void* )lpNV->nData, (const void* )pRLT->infor_data, u32ExpNvSize);   
    }

    return res;
}

SPRESULT CCaliCmd::wcdmaSaveDLNV(const PC_WCDMA_NV_PARAM_T* lpNV)
{
    CheckValidPointer(lpNV);

    LogFmtStrA(SPLOGLV_INFO, "Save WCDMA download NV: nv = %d, size = %d", lpNV->eNvType, lpNV->nDataSize);

    uint32 u32ExpNvSize = wcdmaGetExpCalNvSize(static_cast<WCDMA_CAL_NV_TYPE_E>(lpNV->eNvType));
    if (lpNV->nDataSize > u32ExpNvSize)
    {
        LogFmtStrA(SPLOGLV_ERROR, "Incorrect NV[%d] size %d <> %d!", lpNV->eNvType, lpNV->nDataSize, u32ExpNvSize);
        return SP_E_PHONE_INVALID_PARAMETER;
    }

    if (lpNV->nDataSize > MAX_WCDMA_DL_NV_SIZE)
    {
        LogFmtStrA(SPLOGLV_ERROR, "Too big NV[%d] size! %d < %d", lpNV->eNvType, lpNV->nDataSize, MAX_WCDMA_DL_NV_SIZE);
        return SP_E_PHONE_INVALID_PARAMETER;
    }

    L1_CALI_WCDMA_WRITE_CONFIG_INFOR_REQ_T L1;
    ZeroMemory((void* )&L1, sizeof(L1));

    L1.SignalCode  = Convert16(CALI_WCDMA_WRITE_CONFIG_INFOR);
    L1.SignalSize  = Convert16((uint16)sizeof(L1));

    L1.infor_type  = Convert32((uint32)lpNV->eNvType);
    L1.infor_len   = Convert32((uint32)lpNV->nDataSize);  

    CopyMemory((void* )L1.infor_data, (const void* )lpNV->nData, u32ExpNvSize);
    wcdmaDLNvEndianConv(static_cast<WCDMA_DL_NV_TYPE>(lpNV->eNvType), (uint8* )L1.infor_data, u32ExpNvSize);

    DeclareDiagHeader(hd, DIAG_RF_WCDMA_F, ZERO_SUBTYPE); 
    uint32 recvLen = 0;
    SPRESULT   res = SendAndRecv(hd, NULL, 0, hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);
    if (SP_OK == res)
    {
        uint32 u32ExpSize = sizeof(L1_CALI_WCDMA_WRITE_CONFIG_INFOR_RLT_T);
        if (recvLen < u32ExpSize)
        {
            LogFmtStrA(SPLOGLV_ERROR, "Invalid response length %d < %d!", recvLen, u32ExpSize);
            return SP_E_PHONE_INVALID_LENGTH;
        }

        L1_CALI_WCDMA_WRITE_CONFIG_INFOR_RLT_T* pRLT = (L1_CALI_WCDMA_WRITE_CONFIG_INFOR_RLT_T* )m_diagBuff;
        uint32 u32State = Convert32(pRLT->result);
        if (RF_OP_SUCCESS != u32State)
        {
            LogFmtStrA(SPLOGLV_ERROR, "WCDMA Save DL NV failed, state = %d.", u32State);
            return SP_E_PHONE_INVALID_STATE;
        }

        if (m_pContainer != NULL)
        {
            std::wstring strKey= CwcdmaUtility::GetShareKey(CwcdmaUtility::Dl, lpNV->eNvType);
            m_pContainer->SetValue(strKey.c_str(), lpNV->nData, lpNV->nDataSize * sizeof(lpNV->nData[0]));
        }
    }
    return res;
}

SPRESULT CCaliCmd::wcdmaSaveDLNVToFlash(uint32 u32TimeOut)
{
    LogFmtStrA(SPLOGLV_INFO, "%s: TimeOut = %d", __FUNCTION__, u32TimeOut);

    L1_CALI_WCDMA_REQ_T req;
    ZeroMemory((void* )&req, sizeof(req));

    req.SignalCode  = Convert16((uint16)CALI_WCDMA_SAVE_CONFIG_INFOR);
    req.SignalSize  = Convert16((uint16)sizeof(req));

    DeclareDiagHeader(hd, DIAG_RF_WCDMA_F, ZERO_SUBTYPE); 
    uint32 recvLen = 0;
    SPRESULT   res = SendAndRecv(hd, NULL, 0, hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvLen, u32TimeOut);
    if (SP_OK == res)
    {
        uint32 u32ExpSize = sizeof(L1_CALI_WCDMA_SAVE_CONFIG_INFOR_RLT_T);
        if (recvLen < u32ExpSize)
        {
            LogFmtStrA(SPLOGLV_ERROR, "Invalid response length %d < %d!", recvLen, u32ExpSize);
            return SP_E_PHONE_INVALID_LENGTH;
        }

        L1_CALI_WCDMA_SAVE_CONFIG_INFOR_RLT_T* pRLT = (L1_CALI_WCDMA_SAVE_CONFIG_INFOR_RLT_T* )m_diagBuff;
        uint32 u32State = Convert32(pRLT->result);
        if (RF_OP_SUCCESS != u32State)
        {
            LogFmtStrA(SPLOGLV_ERROR, "WCDMA Save DL NV to flash failed, state = %d.", u32State);
            return SP_E_PHONE_INVALID_STATE;
        }
    }

    return res;
}

SPRESULT CCaliCmd::wcdmaLoadCalFlag(uint16* lpflag)
{
    CheckValidPointer(lpflag);

    LogRawStrA(SPLOGLV_INFO, "Load WCDMA calibration flag:");

    PC_WCDMA_NV_PARAM_T nv;
    ZeroMemory((void* )&nv, sizeof(nv));
    nv.eNvType   = WCDMA_CAL_NV_TYPE_CAL_FLAG;
    nv.nDataSize = 2;
    SPRESULT res = wcdmaLoadCalNV(&nv);
    if (SP_OK == res)
    {
        *lpflag = *((uint16 *)(nv.nData));
        LogFmtStrA(SPLOGLV_INFO, "WCDMA calibration flag = 0x%X!", *lpflag);
    }

    return res; 
}

SPRESULT CCaliCmd::wcdmaSaveCalFlag(uint16 flag)
{
    LogFmtStrA(SPLOGLV_INFO, "Save WCDMA calibration flag 0x%X.", flag);

    PC_WCDMA_NV_PARAM_T nv;
    ZeroMemory((void* )&nv, sizeof(nv));
    nv.eNvType    = WCDMA_CAL_NV_TYPE_CAL_FLAG;
    nv.nDataSize  = 2;
    memcpy((void* )&nv.nData, (const void* )&flag, nv.nDataSize);

    return wcdmaSaveCalNV(&nv);
}

SPRESULT CCaliCmd::wcdmaStartCaptureIQ(RF_WCDMA_PATH_E ePath, uint32* lpu32nbrofIQBytes)
{
    CheckValidPointer(lpu32nbrofIQBytes);

    LogFmtStrA(SPLOGLV_INFO, "Start to capture WCDMA IQ: path = %d", ePath);

    L1_CALI_WCDMA_RX_CAPTURE_DATA_REQ_T  L1;
    ZeroMemory((void *)&L1, sizeof(L1));

    L1.SignalCode  = Convert16(CALI_WCDMA_RX_CAPTURE_DATE_REQ);
    L1.SignalSize  = Convert16((uint16)sizeof(L1));

    L1.path        = Convert32((uint32)ePath);
   
    DeclareDiagHeader(hd, DIAG_RF_WCDMA_F, ZERO_SUBTYPE); 

    uint32 recvLen = 0;
    SPRESULT   res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);
    if (SP_OK == res)
    {
        uint32 u32ExpSize = sizeof(L1_CALI_WCDMA_RX_CAPTURE_DATA_RLT_T);
        if (recvLen < u32ExpSize)
        {
            LogFmtStrA(SPLOGLV_ERROR, "Invalid response length %d < %d!", recvLen, u32ExpSize);
            return SP_E_PHONE_INVALID_LENGTH;
        }

        L1_CALI_WCDMA_RX_CAPTURE_DATA_RLT_T* pRLT = (L1_CALI_WCDMA_RX_CAPTURE_DATA_RLT_T* )m_diagBuff;
        uint32 u32State = Convert32(pRLT->result);
        if (RF_OP_SUCCESS != u32State)
        {
            LogFmtStrA(SPLOGLV_ERROR, "Start to capture WCDMA IQ data failed, state = %d.", u32State);
            return SP_E_PHONE_INVALID_STATE;
        }

        *lpu32nbrofIQBytes = Convert32(pRLT->len);
        LogFmtStrA(SPLOGLV_INFO, "Total bytes of IQ data = %d", *lpu32nbrofIQBytes);
    }

    return res;
}

SPRESULT CCaliCmd::wcdmaCapturingIQ(uint32 u32Offset, uint32 u32nbrOfBytesToCapture, LPVOID lpData, uint32* lpu32nbrOfBytesCaptured)
{
    if (NULL == lpData || 0 == u32nbrOfBytesToCapture || u32nbrOfBytesToCapture > MAX_WCDMA_CAPTURED_IQ_SIZE)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid parameters!", __FUNCTION__);
        return SP_E_PHONE_INVALID_PARAMETER;
    }

    LogFmtStrA(SPLOGLV_INFO, "Capturing WCDMA IQ data: Offset = %d, To Capture = %d Bytes.", u32Offset, u32nbrOfBytesToCapture);
    
    L1_CALI_WCDMA_RX_READ_CAPTURED_DATA_REQ_T L1;
    ZeroMemory((void* )&L1, sizeof(L1));

    L1.SignalCode  = Convert16(CALI_WCDMA_RX_READ_CAPTURED_DATE_REQ);
    L1.SignalSize  = Convert16((uint16)sizeof(L1));

    L1.offest      = Convert32((uint32)u32Offset);
    L1.len         = Convert32((uint32)u32nbrOfBytesToCapture);
    
    DeclareDiagHeader(hd, DIAG_RF_WCDMA_F, ZERO_SUBTYPE);

    uint32 recvLen = 0;
    SPRESULT   res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);
    if (SP_OK == res)
    {
        uint32 u32ExpSize = sizeof(L1_CALI_WCDMA_RX_READ_CAPTURED_DATA_RLT_T);
        if (recvLen < u32ExpSize)
        {
            LogFmtStrA(SPLOGLV_ERROR, "Invalid response length %d < %d!", recvLen, u32ExpSize);
            return SP_E_PHONE_INVALID_LENGTH;
        }

        L1_CALI_WCDMA_RX_READ_CAPTURED_DATA_RLT_T* pRLT = (L1_CALI_WCDMA_RX_READ_CAPTURED_DATA_RLT_T* )m_diagBuff;
        uint32 u32State = Convert32(pRLT->result);
        if (RF_OP_SUCCESS != u32State)
        {
            LogFmtStrA(SPLOGLV_ERROR, "Capturing WCDMA IQ data failed, state = %d.", u32State);
            return SP_E_PHONE_INVALID_STATE;
        }

        uint32 u32NbrOfBytesCaptured = Convert32(pRLT->len);
        if (u32NbrOfBytesCaptured != u32nbrOfBytesToCapture)
        {
            LogFmtStrA(SPLOGLV_WARN, "Total bytes of IQ data captured = %d", u32NbrOfBytesCaptured);
        }

        memcpy(lpData, (const void* )pRLT->data, u32NbrOfBytesCaptured);
        if (NULL != lpu32nbrOfBytesCaptured)
        {
            *lpu32nbrOfBytesCaptured = u32NbrOfBytesCaptured;
        }
    }

    return res;
}

SPRESULT CCaliCmd::wcdmaIQImbalance(const PC_WCDMA_IQ_IMBALANCE_REQ_T* req, LPPC_WCDMA_IQ_IMBALANCE_RLT_T rlt)
{
    if (NULL == req || NULL == rlt)
    {
        LogFmtStrA(SPLOGLV_INFO, "%s: Invalid parameters!", __FUNCTION__);
        return SP_E_PHONE_INVALID_PARAMETER;
    }    

    LogRawStrA(SPLOGLV_INFO, "WCDMA IQ Imbalance: ");

    L1_CALI_WCDMA_IQ_IMBALANCE_REQ_T L1;
    ZeroMemory((void *)&L1, sizeof(L1));

    L1.SignalCode  = Convert16(CALI_WCDMA_IQ_IMBALANCE_REQ);
    L1.SignalSize  = Convert16((uint16)sizeof(L1));

    L1.type        = Convert32(req->type);	
    
    DeclareDiagHeader(hd, DIAG_RF_WCDMA_F, ZERO_SUBTYPE); 

    uint32 recvLen = 0;
    SPRESULT   res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);
    if (SP_OK == res)
    {
        uint32 u32ExpSize = sizeof(L1_CALI_WCDMA_IQ_IMBALANCE_RLT_T);
        if (recvLen < u32ExpSize)
        {
            LogFmtStrA(SPLOGLV_ERROR, "Invalid response length %d < %d!", recvLen, u32ExpSize);
            return SP_E_PHONE_INVALID_LENGTH;
        }

        L1_CALI_WCDMA_IQ_IMBALANCE_RLT_T* pRLT = (L1_CALI_WCDMA_IQ_IMBALANCE_RLT_T* )m_diagBuff;
        uint32 u32State = Convert32(pRLT->result);
        if (RF_OP_SUCCESS != u32State)
        {
            LogFmtStrA(SPLOGLV_ERROR, "WCDMA IQ balance operation failed, state = %d.", u32State);
            return SP_E_PHONE_INVALID_STATE;
        }

        rlt->res_I2Q    = Convert32(pRLT->res_I2Q);
        rlt->Phdeg_best = Convert32(pRLT->Phdeg_best);

        LogFmtStrA(SPLOGLV_INFO, "I2Q:%d, Phdeg_best:%d", rlt->res_I2Q, rlt->Phdeg_best);
    }

    return res;
}

SPRESULT CCaliCmd::wcdmaReadCaliVer(const PC_CALI_WCDMA_DBG_CMD_REQ* req, LPPC_CALI_WCDMA_DBG_CMD_RLT rlt)
{
    if (NULL == req || NULL == rlt)
    {
        LogFmtStrA(SPLOGLV_INFO, "%s: Invalid parameters!", __FUNCTION__);
        return SP_E_PHONE_INVALID_PARAMETER;
    }    

    LogRawStrA(SPLOGLV_INFO, "Load WCDMA calibration version:");


    L1_CALI_WCDMA_DBG_CMD_REQ_T L1;
    ZeroMemory((void* )&L1, sizeof(L1));

    L1.SignalCode  = Convert16(CALC_WCDMA_DEBUG_COMMAND);
    L1.SignalSize  = Convert16((uint16)sizeof(L1));
 
    L1.type		   = Convert32(DBG_CMD_READ_CALI_VER);	
    memcpy(L1.para, req->param, sizeof(L1.para));
    Convert32((uint8* )&L1.para[0], sizeof(L1.para));

    DeclareDiagHeader(hd, DIAG_RF_WCDMA_F, ZERO_SUBTYPE); 

    uint32 recvLen = 0;
    SPRESULT   res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);
    if (SP_OK == res)
    {
        uint32 u32ExpSize = sizeof(L1_CALI_WCDMA_DBG_CMD_RLT_T);
        if (recvLen < u32ExpSize)
        {
            LogFmtStrA(SPLOGLV_ERROR, "Invalid response length %d < %d!", recvLen, u32ExpSize);
            return SP_E_PHONE_INVALID_LENGTH;
        }

        L1_CALI_WCDMA_DBG_CMD_RLT_T* pRLT = (L1_CALI_WCDMA_DBG_CMD_RLT_T* )m_diagBuff;
        uint32 u32State = Convert32(pRLT->state);
        if (RF_OP_SUCCESS != u32State)
        {
            LogFmtStrA(SPLOGLV_ERROR, "Load WCDMA calibration version failed, state = %d.", u32State);
            return SP_E_PHONE_INVALID_STATE;
        }

        rlt->type = Convert32(pRLT->type);

        Convert32((uint8* )&pRLT->rlt[0], sizeof(pRLT->rlt[0]));
        memcpy((void* )rlt->rlt, (const void* )pRLT->rlt, sizeof(rlt->rlt));   

        LogFmtStrA(SPLOGLV_INFO, "Version parameter: %d-%d", rlt->rlt[0] ,rlt->rlt[1]);
    }

    return res;
}

SPRESULT CCaliCmd::wcdmaReadICICompensation(const PC_CALI_WCDMA_RX_ICI_COMP_REQ_T* req, LPPC_CALI_WCDMA_RX_ICI_COMP_RLT_T rlt)
{
    if (NULL == req || NULL == rlt)
    {
        LogFmtStrA(SPLOGLV_INFO, "%s: Invalid parameters!", __FUNCTION__);
        return SP_E_PHONE_INVALID_PARAMETER;
    } 

    LogRawStrA(SPLOGLV_INFO, "Load ICI compensation values:");

    L1_CALI_WCDMA_RX_ICI_COMP_REQ_T L1;
    ZeroMemory((void* )&L1,  sizeof(L1));

    L1.SignalCode  = Convert16(CALI_WCDMA_RX_ICI_COMP_REQ);
    L1.SignalSize  = Convert16((uint16)sizeof(L1));

    L1.band        = Convert16(wcdmaIoBand(req->band));
    L1.dl_uarfcn   = Convert16(req->dl_uarfcn);
    L1.gain_index  = Convert16(req->gain_index);
    L1.path        = Convert16((uint16)req->path);
    
    DeclareDiagHeader(hd, DIAG_RF_WCDMA_F, ZERO_SUBTYPE); 

    uint32 recvLen = 0;
    SPRESULT   res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);
    if (SP_OK == res)
    {
        uint32 u32ExpSize = sizeof(L1_CALI_WCDMA_RX_ICI_COMP_RLT_T);
        if (recvLen < u32ExpSize)
        {
            LogFmtStrA(SPLOGLV_ERROR, "Invalid response length %d < %d!", recvLen, u32ExpSize);
            return SP_E_PHONE_INVALID_LENGTH;
        }

        L1_CALI_WCDMA_RX_ICI_COMP_RLT_T* pRLT = (L1_CALI_WCDMA_RX_ICI_COMP_RLT_T* )m_diagBuff;
        uint32 u32State = Convert32(pRLT->result);
        if (RF_OP_SUCCESS != u32State)
        {
            LogFmtStrA(SPLOGLV_ERROR, "Load WCDMA ICI compensation failed, state = %d.", u32State);
            return SP_E_PHONE_INVALID_STATE;
        }

        rlt->rssi = Convert32(pRLT->rssi);

        Convert32((uint8* )pRLT->real_coeff,  sizeof(pRLT->real_coeff ));
        Convert32((uint8* )pRLT->image_coeff, sizeof(pRLT->image_coeff));
        memcpy((void* )rlt->real_coeff,  pRLT->real_coeff,  sizeof(pRLT->real_coeff ));  
        memcpy((void* )rlt->image_coeff, pRLT->image_coeff, sizeof(pRLT->image_coeff));
        // [33] is always 0
        rlt->real_coeff[33]  = 0x00;
        rlt->image_coeff[33] = 0x00;
    }

    return res;
}

SPRESULT CCaliCmd::wcdmaDebug(uint32 input[6], uint32 ouput[6])
{
    LogFmtStrA(SPLOGLV_INFO, "%s: type = 0x%X, input = 0x%X, 0x%X, 0x%X, 0x%X, 0x%X", __FUNCTION__, \
                        input[0], input[1], input[2], input[3], input[4], input[5]);

    L1_CALI_WCDMA_DEBUG_COMMAND_REQ_T L1;
    ZeroMemory((void* )&L1, sizeof(L1));
    L1.SignalCode  = Convert16((uint16)CALC_WCDMA_DEBUG_COMMAND);
    L1.SignalSize  = Convert16((uint16)sizeof(L1));

    L1.type        = Convert32(input[0]);
    for (int i=0; i<5; i++)
    {
        L1.param[i]= Convert32(input[i+1]);
    }

    DeclareDiagHeader(hd, DIAG_RF_WCDMA_F, ZERO_SUBTYPE); 

    uint32 recvLen = 0;
    SPRESULT   res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);
    if (SP_OK == res)
    {
        uint32 u32ExpSize = sizeof(L1_CALI_WCDMA_DEBUG_COMMAND_RLT_T);
        if (recvLen < u32ExpSize)
        {
            LogFmtStrA(SPLOGLV_ERROR, "Invalid response length %d < %d!", recvLen, u32ExpSize);
            return SP_E_PHONE_INVALID_LENGTH;
        }

        L1_CALI_WCDMA_DEBUG_COMMAND_RLT_T* pRLT = (L1_CALI_WCDMA_DEBUG_COMMAND_RLT_T* )m_diagBuff;
        uint32 u32State = Convert32(pRLT->result);
        if (RF_OP_SUCCESS != u32State)
        {
            LogFmtStrA(SPLOGLV_ERROR, "WCDMA debug failed, state = %d.", u32State);
            return SP_E_PHONE_INVALID_STATE;
        }

        ouput[0] = Convert32(pRLT->type);
        LogFmtStrA(SPLOGLV_INFO, "type = %d", ouput[0]);

        for (int i=0; i<5; i++)
        {
            ouput[i+1] = Convert32(pRLT->param[i]);
            LogFmtStrA(SPLOGLV_INFO, "output[%d] = %d (0x%X)", i, ouput[i+1], ouput[i+1]);
        }
    }

    return res;
}

SPRESULT CCaliCmd::wcdmaNST_Init(void)
{
    LogRawStrA(SPLOGLV_INFO, "WCDMA NST initialize:");

    L1_CALI_WCDMA_REQ_T L1;
    ZeroMemory((void* )&L1, sizeof(L1));
    L1.SignalCode  = Convert16(WCDMA_NS_TEST_INIT_REQ);
    L1.SignalSize  = Convert16((uint16)sizeof(L1));

    DeclareDiagHeader(hd, DIAG_WCDMA_NS_TEST, ZERO_SUBTYPE); 

    uint32 recvLen = 0;
    SPRESULT   res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);
    if (SP_OK == res)
    {
        uint32 u32ExpSize = sizeof(L1_WCDMA_NS_TEST_INIT_RLT_T);
        if (recvLen < u32ExpSize)
        {
            LogFmtStrA(SPLOGLV_ERROR, "Invalid response length %d < %d!", recvLen, u32ExpSize);
            return SP_E_PHONE_INVALID_LENGTH;
        }

        L1_WCDMA_NS_TEST_INIT_RLT_T* pRLT = (L1_WCDMA_NS_TEST_INIT_RLT_T* )m_diagBuff;
        uint32 u32State = Convert32(pRLT->result);
        if (RF_OP_SUCCESS != u32State)
        {
            LogFmtStrA(SPLOGLV_ERROR, "WCDMA NST initiate failed, state = %d.", u32State);
            return SP_E_PHONE_INVALID_STATE;
        }
    }

    return res;
}

SPRESULT CCaliCmd::wcdmaNST_Start(const PC_WNST_START_PARAM_T* req)
{
    CheckValidPointer(req);

    LogFmtStrA(SPLOGLV_INFO, "WCDMA NST start: DL_UARFCN = %d, Band = %d, Slot_Format = %d, PGC = %d, Chnl_Code = %d, TPC_Alg = %d, TPC_Step_Size = %d, Scrambling_Code_Num = %d", 
        req->DL_UARFCN, 
        wcdmaIoBand(req->eBand), 
        req->Slot_Format, 
        req->PGC, 
        req->Chnl_Code, 
        req->TPC_Alg, 
        req->TPC_Step_Size, 
        req->Scrambling_Code_Num
        );

    L1_WCDMA_NS_TEST_START_REQ_T   L1;
    ZeroMemory((void* )&L1, sizeof(L1));
    L1.SignalCode  = Convert16(WCDMA_NS_TEST_START_REQ);
    L1.SignalSize  = Convert16((uint16)sizeof(L1));

    L1.start.DL_UARFCN             = Convert32((uint32)(req->DL_UARFCN));
    L1.start.Band                  = Convert32((uint32)(wcdmaIoBand(req->eBand)));
    L1.start.Slot_Format           = Convert32((uint32)(req->Slot_Format));
    L1.start.PGC                   = Convert32((uint32)(req->PGC));
    L1.start.Chnl_Code             = Convert32((uint32)(req->Chnl_Code));
    L1.start.TPC_Alg               = Convert32((uint32)(req->TPC_Alg));
    L1.start.TPC_Step_Size         = Convert32((uint32)(req->TPC_Step_Size));
    L1.start.Scrambling_Code_Num   = Convert32((uint32)(req->Scrambling_Code_Num));

    DeclareDiagHeader(hd, DIAG_WCDMA_NS_TEST, ZERO_SUBTYPE); 

    uint32 recvLen = 0;
    SPRESULT   res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);
    if (SP_OK == res)
    {
        uint32 u32ExpSize = sizeof(L1_WCDMA_NS_TEST_START_RLT_T);
        if (recvLen < u32ExpSize)
        {
            LogFmtStrA(SPLOGLV_ERROR, "Invalid response length %d < %d!", recvLen, u32ExpSize);
            return SP_E_PHONE_INVALID_LENGTH;
        }

        L1_WCDMA_NS_TEST_START_RLT_T* pRLT = (L1_WCDMA_NS_TEST_START_RLT_T* )m_diagBuff;
        uint32 u32State = Convert32(pRLT->result);
        if (RF_OP_SUCCESS != u32State)
        {
            LogFmtStrA(SPLOGLV_ERROR, "WCDMA NST start failed, state = %d.", u32State);
            return SP_E_PHONE_INVALID_STATE;
        }
    }

    return res;
}

SPRESULT CCaliCmd::wcdmaNST_ReConfig(const PC_WNST_RECONFIG_T* req)
{
    CheckValidPointer(req);

    LogFmtStrA(SPLOGLV_INFO, "WCDMA NST reconfig: Slot_Format = %d, Chnl_Code = %d, TPC_Alg = %d, TPC_Step_Size = %d, Scrambling_Code_Num = %d", 
        req->Slot_Format, 
        req->Chnl_Code, 
        req->TPC_Alg, 
        req->TPC_Step_Size, 
        req->Scrambling_Code_Num
        );

    L1_WCDMA_NS_TEST_RECONFIG_REQ_T L1;
    ZeroMemory((void* )&L1,  sizeof(L1));
    L1.SignalCode  = Convert16(WCDMA_NS_TEST_RECONFIG_REQ);
    L1.SignalSize  = Convert16((uint16)sizeof(L1));

    L1.reconfig.Slot_Format           = Convert32((uint32)(req->Slot_Format));
    L1.reconfig.Chnl_Code             = Convert32((uint32)(req->Chnl_Code));
    L1.reconfig.TPC_Alg               = Convert32((uint32)(req->TPC_Alg));
    L1.reconfig.TPC_Step_Size         = Convert32((uint32)(req->TPC_Step_Size));
    L1.reconfig.Scrambling_Code_Num   = Convert32((uint32)(req->Scrambling_Code_Num));
	L1.reconfig.TPC_Type              = Convert32((uint32)(req->TPC_Type));
	L1.reconfig.Power                 = Convert32((uint32)(req->Power));

    DeclareDiagHeader(hd, DIAG_WCDMA_NS_TEST, ZERO_SUBTYPE); 

    uint32 recvLen = 0;
    SPRESULT   res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);
    if (SP_OK == res)
    {
        uint32 u32ExpSize = sizeof(L1_WCDMA_NS_TEST_RECONFIG_RLT_T);
        if (recvLen < u32ExpSize)
        {
            LogFmtStrA(SPLOGLV_ERROR, "Invalid response length %d < %d!", recvLen, u32ExpSize);
            return SP_E_PHONE_INVALID_LENGTH;
        }

        L1_WCDMA_NS_TEST_RECONFIG_RLT_T* pRLT = (L1_WCDMA_NS_TEST_RECONFIG_RLT_T* )m_diagBuff;
        uint32 u32State = Convert32(pRLT->result);
        if (RF_OP_SUCCESS != u32State)
        {
            LogFmtStrA(SPLOGLV_ERROR, "WCDMA NST reconfig failed, state = %d.", u32State);
            return SP_E_PHONE_INVALID_STATE;
        }
    }

    return res;
}

SPRESULT CCaliCmd::wcdmaNST_Stop(void)
{
    LogRawStrA(SPLOGLV_INFO, "WCDMA NST stop:");

    L1_WCDMA_NS_TEST_STOP_REQ_T    L1;
    ZeroMemory((void* )&L1, sizeof(L1));
    L1.SignalCode  = Convert16(WCDMA_NS_TEST_STOP_REQ);
    L1.SignalSize  = Convert16((uint16)sizeof(L1));

    DeclareDiagHeader(hd, DIAG_WCDMA_NS_TEST, ZERO_SUBTYPE); 

    uint32 recvLen = 0;
    SPRESULT   res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);
    if (SP_OK == res)
    {
        uint32 u32ExpSize = sizeof(L1_WCDMA_NS_TEST_STOP_RLT_T);
        if (recvLen < u32ExpSize)
        {
            LogFmtStrA(SPLOGLV_ERROR, "Invalid response length %d < %d!", recvLen, u32ExpSize);
            return SP_E_PHONE_INVALID_LENGTH;
        }

        L1_WCDMA_NS_TEST_STOP_RLT_T* pRLT = (L1_WCDMA_NS_TEST_STOP_RLT_T* )m_diagBuff;
        uint32 u32State = Convert32(pRLT->result);
        if (RF_OP_SUCCESS != u32State)
        {
            LogFmtStrA(SPLOGLV_ERROR, "WCDMA NST stop failed, state = %d.", u32State);
            return SP_E_PHONE_INVALID_STATE;
        }
    }

    return res;
}

SPRESULT CCaliCmd::wcdmaNST_GetRSCP(LPPC_WNST_RSCP_RLT_T rlt)
{
	CheckValidPointer(rlt);

	LogRawStrA(SPLOGLV_INFO, "WCDMA NST get RSCP:");

	L1_CALI_WCDMA_REQ_T    L1;
	ZeroMemory((void* )&L1, sizeof(L1));
	L1.SignalCode  = Convert16(WCDMA_NS_TEST_GET_RSCP);
	L1.SignalSize  = Convert16((uint16)sizeof(L1));

	DeclareDiagHeader(hd, DIAG_WCDMA_NS_TEST, ZERO_SUBTYPE); 

	uint32 recvLen = 0;
	SPRESULT   res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);
	if (SP_OK == res)
	{
		uint32 u32ExpSize = sizeof(L1_WCDMA_NS_TEST_RSCP_RLT_T);
		if (recvLen < u32ExpSize)
		{
			LogFmtStrA(SPLOGLV_ERROR, "Invalid response length %d < %d!", recvLen, u32ExpSize);
			return SP_E_PHONE_INVALID_LENGTH;
		}

		L1_WCDMA_NS_TEST_RSCP_RLT_T* pRLT = (L1_WCDMA_NS_TEST_RSCP_RLT_T* )m_diagBuff;
		uint32 u32State = Convert32(pRLT->result);
		if (RF_OP_SUCCESS == u32State)
		{
			rlt->rscp     = Convert32(pRLT->rscp);
			rlt->rssi     = Convert32(pRLT->rssi);
			LogFmtStrA(SPLOGLV_INFO, "    RSCP = 0x%X, RSSI = 0x%X", rlt->rscp, rlt->rssi);
		}
		else if (RF_OP_SUCCESS_V2 == u32State)
		{
			L1_WCDMA_NS_TEST_RSCP_RLT_T* pRLT = (L1_WCDMA_NS_TEST_RSCP_RLT_T* )m_diagBuff;
			rlt->rscp     = Convert32(pRLT->rscp);
			rlt->rssi     = Convert32(pRLT->rssi);

			LogFmtStrA(SPLOGLV_INFO, "    RSCP = 0x%X, RSSI = 0x%X", rlt->rscp, rlt->rssi);
		}
		else
		{
			LogFmtStrA(SPLOGLV_ERROR, "WCDMA NST get RSCP failed, state = %d.", u32State);
			return SP_E_PHONE_INVALID_STATE;
		}
	}

	return res;
}

SPRESULT CCaliCmd::wcdmaNST_GetAusRSCP(LPPC_WNST_AUS_REQ_T req, LPPC_WNST_AUS_RLT_T rlt, uint32 u32TimeOut)
{
    CheckValidPointer(rlt);
    LogRawStrA(SPLOGLV_INFO, "WCDMA NST get Aus RSCP:");
    L1_WCDMA_NS_AUS_RSCP_REQ_T    L1;
    ZeroMemory((void* )&L1, sizeof(L1));
	for(int i =0; i<5; i++)
	{
		L1.data[i].nRscpDelta64 = req->data[i].nRscpDelta64;
		L1.data[i].again = req->data[i].nAgain;
	}
	
	switch(req->eType)
	{
		case WCDMA_AUS_RSCP_PRIM_CAL:
			L1.SignalCode  = Convert16(WCDMA_NS_TEST_GET_AUS_RSCP_CAL);
			break;
		case WCDMA_AUS_RSCP_DIV_CAL:
			L1.SignalCode  = Convert16(WCDMA_NS_TEST_GET_AUS_DIVRSCP_CAL);
			break;
		case WCDMA_AUS_RSCP_PRIM_VERIFY:
			L1.SignalCode  = Convert16(WCDMA_NS_TEST_GET_AUS_RSCP_VERIFY);
			break;
		case WCDMA_AUS_RSCP_DIV_VERIFY:
			L1.SignalCode  = Convert16(WCDMA_NS_TEST_GET_AUS_DIVRSCP_VERIFY);
			break;
		default:
			LogFmtStrA(SPLOGLV_ERROR, "Error op Type = %d", req->eType);
			return SP_E_PHONE_INVALID_PARAMETER;
	}
    
    L1.SignalSize  = Convert16((uint16)sizeof(L1));
    DeclareDiagHeader(hd, DIAG_WCDMA_NS_TEST, ZERO_SUBTYPE); 
    uint32 recvLen = 0;
    SPRESULT   res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvLen, u32TimeOut);
    if (SP_OK == res)
    {
        uint32 u32ExpSize = sizeof(L1_WCDMA_NS_TEST_AUS_RLT_T);
        if (recvLen < u32ExpSize)
        {
            LogFmtStrA(SPLOGLV_ERROR, "Invalid response length %d < %d!", recvLen, u32ExpSize);
            return SP_E_PHONE_INVALID_LENGTH;
        }
        L1_WCDMA_NS_TEST_AUS_RLT_T* pRLT = (L1_WCDMA_NS_TEST_AUS_RLT_T* )m_diagBuff;
        uint32 u32State = Convert32(pRLT->result);
        if (RF_OP_SUCCESS == u32State)
        {
			for(int i=0; i<5; i++)
			{
				rlt->data[i].rscp     = Convert32(pRLT->data[i].rscp);
				rlt->data[i].rssi     = Convert32(pRLT->data[i].rssi);
				rlt->data[i].again    = pRLT->data[i].again;
				LogFmtStrA(SPLOGLV_INFO, "Index:%d: RSCP = 0x%X, RSSI = 0x%X, AGAIN = 0x%X", i, pRLT->data[i].rscp, pRLT->data[i].rssi, pRLT->data[i].again);
			}
           
        }
        else
        {
            LogFmtStrA(SPLOGLV_ERROR, "WCDMA NST get RSCP failed, state = %d.", u32State);
            return SP_E_PHONE_INVALID_STATE;
        }
    }
    return res;
}

SPRESULT CCaliCmd::wcdmaNST_GetSEBER(LPPC_WNST_SEBER_RLT_T rlt)
{
    CheckValidPointer(rlt);

    LogRawStrA(SPLOGLV_INFO, "WCDMA NST get SEBER:");

    L1_WCDMA_NS_TEST_SEBER_REQ_T   L1;
    ZeroMemory((void* )&L1, sizeof(L1));

    L1.SignalCode  = Convert16(WCDMA_NS_TEST_GET_SEBER);
    L1.SignalSize  = Convert16((uint16)sizeof(L1));

    L1.total_bits  = Convert32(rlt->nTotalBits);

    DeclareDiagHeader(hd, DIAG_WCDMA_NS_TEST, ZERO_SUBTYPE); 

    uint32 recvLen = 0;
    SPRESULT   res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);
    if (SP_OK == res)
    {
        uint32 u32ExpSize = sizeof(L1_WCDMA_NS_TEST_SEBER_RLT_T);
        if (recvLen < u32ExpSize)
        {
            LogFmtStrA(SPLOGLV_ERROR, "Invalid response length %d < %d!", recvLen, u32ExpSize);
            return SP_E_PHONE_INVALID_LENGTH;
        }

        L1_WCDMA_NS_TEST_SEBER_RLT_T* pRLT = (L1_WCDMA_NS_TEST_SEBER_RLT_T* )m_diagBuff;
        uint32 u32State = Convert32(pRLT->result);
        if (RF_OP_SUCCESS != u32State)
        {
            LogFmtStrA(SPLOGLV_ERROR, "WCDMA NST get SEBER failed, state = %d.", u32State);
            return SP_E_PHONE_INVALID_STATE;
        }

        rlt->nFailBits = Convert32(pRLT->fail_bits);
    }

    return res;
}

//////////////////////////////////////////////////////////////////////////
// WCDMA FAT NST
SPRESULT CCaliCmd::wcdmaLMT_Init(void)
{
	LogRawStrA(SPLOGLV_INFO, "WCDMA LMT initialize:");

	L1_CALI_WCDMA_REQ_T req;
	ZeroMemory((void* )&req,  sizeof(req));
	req.SignalCode  = Convert16((uint16)WCDMA_FAST_NS_TEST_INIT_REQ);
	req.SignalSize  = Convert16((uint16)sizeof(req));

	DeclareDiagHeader(hd, DIAG_WCDMA_NS_TEST, ZERO_SUBTYPE); 

	uint32 recvLen = 0;
	SPRESULT   res = SendAndRecv(hd, (const void* )&req, sizeof(req), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);
	if (SP_OK == res)
	{                 
        uint32 u32ExpSize = sizeof(L1_CALI_WCDMA_FAST_NS_INIT_RLT_T);
        if (recvLen < u32ExpSize)
        {
            LogFmtStrA(SPLOGLV_ERROR, "Invalid response length %d < %d!", recvLen, u32ExpSize);
            return SP_E_PHONE_INVALID_LENGTH;
        }

		L1_CALI_WCDMA_FAST_NS_INIT_RLT_T* pRLT = (L1_CALI_WCDMA_FAST_NS_INIT_RLT_T* )m_diagBuff;
		uint32 u32State = Convert32((uint32)(pRLT->result));
		if (RF_OP_SUCCESS != u32State)
		{
            LogFmtStrA(SPLOGLV_ERROR, "WCDMA LMT Init failed, state = %d.", u32State);
            return SP_E_PHONE_INVALID_STATE;
		}
	}

	return res;
}

SPRESULT CCaliCmd::wcdmaLMT_Start(const PC_WCDMA_LMT_START_T* pStart)
{
    CheckValidPointer(pStart);
	LogFmtStrA(SPLOGLV_INFO, "WCDMA LMT Start: seg %d", pStart->segment_num);

	L1_CALI_WCDMA_FAST_NS_START_REQ_T  req;
	ZeroMemory((void* )&req,    sizeof(req));
	req.SignalCode  = Convert16((uint16)WCDMA_FAST_NS_TEST_START_REQ);
    uint32 u32Size  = sizeof(L1_CALI_WCDMA_FAST_NS_START_REQ_T) - (MAX_WCDMA_LMT_SEG_NUMBER - pStart->segment_num)*sizeof(W_FAST_NS_Star_Seg_t);
	req.SignalSize  = Convert16((uint16)u32Size);

    req.data.Slot_Format            = Convert32(pStart->Slot_Format);
    req.data.PGC                    = Convert32(pStart->PGC);
    req.data.Scrambling_Code_Num    = Convert32(pStart->Scrambling_Code_Num);
    req.data.Chnl_Code              = Convert32(pStart->Chnl_Code);
    req.data.TPC_Alg                = Convert32(pStart->TPC_Alg);
    req.data.TPC_Step_Size          = Convert32(pStart->TPC_Step_Size);
    req.data.segment_num            = Convert32(pStart->segment_num);
    for (uint32 i=0; i<pStart->segment_num; i++)
    {
        req.data.seg[i].DL_UARFCN           = Convert32(pStart->seg[i].DL_UARFCN);
        req.data.seg[i].Band                = Convert32(pStart->seg[i].Band);
        req.data.seg[i].seg_length          = Convert32(pStart->seg[i].seg_length);
        req.data.seg[i].tx_offset           = Convert32(pStart->seg[i].tx_offset);
        req.data.seg[i].tx_length           = Convert32(pStart->seg[i].tx_length);
        req.data.seg[i].tx_power_Q16        = (int32)Convert32(pStart->seg[i].tx_power_Q16);
        req.data.seg[i].total_SeBer_num     = Convert32(pStart->seg[i].total_SeBer_num);
        req.data.seg[i].measure_type_mask   = Convert32(pStart->seg[i].measure_type_mask);
    }
	//memcpy(&req.data,pStart,sizeof(req.data));

	DeclareDiagHeader(hd, DIAG_WCDMA_NS_TEST, ZERO_SUBTYPE); 

	ZeroMemory((void *)&m_diagBuff, sizeof(m_diagBuff));
	uint32 recvLen = 0;
	SPRESULT   res = SendAndRecv(hd, (const void* )&req, u32Size, hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);
	if (SP_OK == res)
	{              
        uint32 u32ExpSize = sizeof(L1_CALI_WCDMA_FAST_NS_START_RLT_T);
        if (recvLen < u32ExpSize)
        {
            LogFmtStrA(SPLOGLV_ERROR, "Invalid response length %d < %d!", recvLen, u32ExpSize);
            return SP_E_PHONE_INVALID_LENGTH;
        }

		L1_CALI_WCDMA_FAST_NS_START_RLT_T* pRLT = (L1_CALI_WCDMA_FAST_NS_START_RLT_T* )m_diagBuff;
        uint32 u32State = Convert32((uint32)(pRLT->result));
        if (RF_OP_SUCCESS != u32State)
        {
            LogFmtStrA(SPLOGLV_ERROR, "WCDMA LMT Start failed, state = %d.", u32State);
            return SP_E_PHONE_INVALID_STATE;
        }
	}

	return res;
}

SPRESULT CCaliCmd::wcdmaLMT_GetResult(const PC_WCDMA_LMT_GETRLT_REQ_T* pReq, PC_WCDMA_LMT_GETRLT_RLT_T* pRlt)
{
    CheckValidPointer(pReq);
    CheckValidPointer(pRlt);
	LogFmtStrA(SPLOGLV_INFO, "WCDMA LMT Get result: type = %d", pReq->req_type);

	L1_CALI_WCDMA_FAST_NS_GETRLT_REQ_T req;
	ZeroMemory((void* )&req, sizeof(req));
	req.SignalCode    = Convert16((uint16)WCDMA_FAST_NS_TEST_GET_RESULT_REQ);
	req.SignalSize    = Convert16((uint16)sizeof(req));

    req.data.req_type = Convert32(pReq->req_type);
    for (uint32 i=0; i<ARRAY_SIZE(pReq->reserved); i++)
    {
        req.data.reserved[i] = Convert32(pReq->reserved[i]);
    }

	DeclareDiagHeader(hd, DIAG_WCDMA_NS_TEST, ZERO_SUBTYPE); 
	uint32 recvLen = 0;
	SPRESULT   res = SendAndRecv(hd,(const void* )&req, sizeof(req), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);
	if (SP_OK == res)
	{                        
		L1_CALI_WCDMA_FAST_NS_GETRLT_RLT_T* pACK = (L1_CALI_WCDMA_FAST_NS_GETRLT_RLT_T* )m_diagBuff;
        pRlt->count = Convert32(pACK->data.length);
		if (pRlt->count > MAX_WCDMA_LMT_SEG_NUMBER)
		{
            LogFmtStrA(SPLOGLV_ERROR, "Invalid WCDMA LMT response data %d > %d!", pRlt->count, MAX_WCDMA_LMT_SEG_NUMBER);
            return SP_E_PHONE_INVALID_DATA;
		}

        uint32 u32ExpSize = sizeof(L1_CALI_WCDMA_FAST_NS_GETRLT_RLT_T) - \
            (MAX_WCDMA_LMT_SEG_NUMBER - pRlt->count)*(sizeof(uint32)*MAX_WCDMA_LMT_RLT_DATA_PER_SEG);
        if (recvLen < u32ExpSize)
        {
            LogFmtStrA(SPLOGLV_ERROR, "Invalid response length %d < %d!", recvLen, u32ExpSize);
            return SP_E_PHONE_INVALID_LENGTH;
        }
       
        uint32 u32State = Convert32((uint32)(pACK->result));
        if (RF_OP_SUCCESS != u32State)
        {
            LogFmtStrA(SPLOGLV_ERROR, "WCDMA LMT Get Result failed, state = %d.", u32State);
            return SP_E_PHONE_INVALID_STATE;
        }

		pRlt->res_type = Convert32((uint32)(pACK->data.res_type));
		if (pReq->req_type != pRlt->res_type)
		{
            LogFmtStrA(SPLOGLV_ERROR, "Invalid WCDMA LMT response data type %d <> %d !", pRlt->res_type, pReq->req_type);
            return SP_E_PHONE_INVALID_DATA;
		}

        for (uint32 i=0; i<pRlt->count; i++)
        {
            for (uint32 j=0; j<MAX_WCDMA_LMT_RLT_DATA_PER_SEG; j++)
            {
                pRlt->data[i][j] = Convert32(pACK->data.data[i][j]);
            }
        }
	}

	return res;
}

SPRESULT CCaliCmd::wcdmaLMT_Stop(void)
{
    LogRawStrA(SPLOGLV_INFO, "WCDMA LMT Stop:");

    return SP_OK;
}

SPRESULT CCaliCmd::wcdmaGetFDTTxValues_V6(LPPC_CALI_WCDMA_TX_SEQ_RSSI_V6_RLT_T rlt)
{
	CheckValidPointer(rlt);
	LogRawStrA(SPLOGLV_INFO, "wcdmaGetFDTTxValues_V6:");
	L1_CALI_WCDMA_REQ_T L1;
	ZeroMemory((void *)&L1,    sizeof(L1));
	L1.SignalCode  = Convert16((uint16)CALI_WCDMA_TX_SEQ_RSSI_V6_REQ);
	L1.SignalSize  = Convert16((uint16)sizeof(L1_CALI_WCDMA_REQ_T));
	DeclareDiagHeader(hd, DIAG_RF_WCDMA_F, ZERO_SUBTYPE); 
	uint32 recvLen = 0;
	SPRESULT   res = SendAndRecv(hd,(const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);
	if (SP_OK == res)
	{   
		L1_CALI_WCDMA_TX_RSSI_SEQ_RLT_V6_T *pRLT = (L1_CALI_WCDMA_TX_RSSI_SEQ_RLT_V6_T *)m_diagBuff;
		if(pRLT->data.txdb_num > 10000)
		{
			LogFmtStrA(SPLOGLV_ERROR, "[wcdmaGetFDTTxValues_V6] rx rssi num = %d bigger than 10000!", pRLT->data.txdb_num);
			return SP_E_PHONE_INVALID_LENGTH;
		}
		uint32 length = sizeof(L1_CALI_WCDMA_TX_RSSI_SEQ_RLT_V6_T) - (10000 - pRLT->data.txdb_num)*2;
		if (recvLen < length)
		{
			LogFmtStrA(SPLOGLV_ERROR, "[wcdmaGetFDTTxValues_V6] rx rssi num = %d, Invalid response length %d!", pRLT->data.txdb_num, recvLen);
			return SP_E_PHONE_INVALID_LENGTH;
		}
		uint32 state = Convert32((uint32)(pRLT->state));
		if (RF_OP_SUCCESS != state)
		{
			LogFmtStrA(SPLOGLV_ERROR, "[wcdmaGetFDTTxValues_V6]  failed, state = %d.", state);
			return SP_E_PHONE_INVALID_STATE;
		}
		memcpy((void*)rlt,(void*)&pRLT->data,length);
	}
	return res;
}

SPRESULT  CCaliCmd::wcdmaGetFDTTxDebug_V6(LPPC_CALI_WCDMA_TX_RSSI_DEBUG_V6_T rlt)
{
	CheckValidPointer(rlt);
	LogRawStrA(SPLOGLV_INFO, "wcdmaGetFDTTxDebug_V6:");
	L1_CALI_WCDMA_REQ_T L1;
	ZeroMemory((void *)&L1,    sizeof(L1));
	L1.SignalCode  = Convert16((uint16)CALI_WCDMA_TX_DEBUG_RSSI_V6_REQ);
	L1.SignalSize  = Convert16((uint16)sizeof(L1_CALI_WCDMA_REQ_T));
	DeclareDiagHeader(hd, DIAG_RF_WCDMA_F, ZERO_SUBTYPE); 
	ZeroMemory((void *)&m_diagBuff, sizeof(m_diagBuff));
	uint32 recvLen = 0;
	SPRESULT   res = SendAndRecv(hd,(const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);
	if (SP_OK == res)
	{   
		L1_CALI_WCDMA_TX_RSSI_DEBUG_RLT_V6_T *pRLT = (L1_CALI_WCDMA_TX_RSSI_DEBUG_RLT_V6_T *)m_diagBuff;
		if(pRLT->data.array_num > 2000)
		{
			LogFmtStrA(SPLOGLV_ERROR, "[wcdmaGetFDTTxDebug_V6] rx rssi num = %d bigger than 2000!", pRLT->data.array_num);
			return SP_E_PHONE_INVALID_LENGTH;
		}
		uint32 length = sizeof(PC_CALI_WCDMA_TX_RSSI_DEBUG_V6_T) - (2000 - pRLT->data.array_num)*sizeof(PC_CALI_WCDMA_TX_RSSI_DEBUG_SEG_V6_T);
		if (recvLen < length)
		{
			LogFmtStrA(SPLOGLV_ERROR, "[wcdmaGetFDTTxDebug_V6] rx rssi num = %d, Invalid response length %d!",pRLT->data.array_num, recvLen);
			return SP_E_PHONE_INVALID_LENGTH;
		}
		memcpy((void*)rlt,(void*)&pRLT->data,length);
	}
	return res;
}


SPRESULT CCaliCmd::wcdmaInternalCal_Init()
{
	LogRawStrA(SPLOGLV_INFO, "wcdmaInternalCal_Init:");

	L1_CALI_WCDMA_REQ_T     L1;
	ZeroMemory((void *)&L1, sizeof(L1));
	L1.SignalCode  = Convert16((uint16)CALI_WCDMA_INTCAL_INIT_V1_REQ);
	L1.SignalSize  = Convert16((uint16)sizeof(L1_CALI_WCDMA_REQ_T));

	DeclareDiagHeader(hd, DIAG_RF_WCDMA_F, ZERO_SUBTYPE); 
	uint32 recvLen = 0;
	SPRESULT   res = SendAndRecv(hd,(const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);
	if (SP_OK == res)
	{                        
		if (recvLen < sizeof(L1_CALI_WCDMA_REQ_T))
		{
			LogFmtStrA(SPLOGLV_ERROR, "[wcdmaInternalCal_Init] Invalid response length %d!", recvLen);
			return SP_E_PHONE_INVALID_LENGTH;
		}

		L1_CALI_WCDMA_INTCAL_INIT_V1_RLT_T *pRLT = (L1_CALI_WCDMA_INTCAL_INIT_V1_RLT_T *)m_diagBuff;
		int state = Convert32((uint32)( pRLT->result));
		if (RF_OP_SUCCESS != state)
		{
			LogFmtStrA(SPLOGLV_ERROR, "[wcdmaInternalCal_Init]Operation failed, state = %d.",  state);
			return SP_E_PHONE_INVALID_STATE;
		}
	}
	return res;
}

SPRESULT CCaliCmd::wcdmaInternalCal_Start(const PC_CALI_WCDMA_INTCAL_START_V1_REQ_T *pReq,PC_CALI_WCDMA_INTCAL_START_V1_RLT_T *pResult)
{
	CheckValidPointer(pReq);
	CheckValidPointer(pResult);
	LogRawStrA(SPLOGLV_INFO, "wcdmaInternalCal_Start:");
	L1_CALI_WCDMA_INTCAL_START_V1_REQ_T L1;
	ZeroMemory((void *)&L1, sizeof(L1));
	memcpy(&L1.txdata,pReq,sizeof(L1.txdata));

	L1.SignalCode  = Convert16((uint16)CALI_WCDMA_INTCAL_START_V1_REQ);
	L1.SignalSize  = Convert16((uint16)(sizeof(L1_CALI_WCDMA_INTCAL_START_V1_REQ_T) - (1000 - pReq->txdata.start_gain_index_num)*sizeof(uint16)));
	DeclareDiagHeader(hd, DIAG_RF_WCDMA_F, ZERO_SUBTYPE); 
	uint32 recvLen = 0;
	SPRESULT   res = SendAndRecv(hd,(const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvLen, TIMEOUT_30S);
	if (SP_OK == res)
	{
		if (recvLen < sizeof(L1_CALI_WCDMA_INTCAL_START_V1_RLT_T))
		{
			LogFmtStrA(SPLOGLV_ERROR, "[wcdmaInternalCal_Start] Invalid response length %d!", recvLen);
			return SP_E_PHONE_INVALID_LENGTH;
		}
		L1_CALI_WCDMA_INTCAL_START_V1_RLT_T *pRLT = (L1_CALI_WCDMA_INTCAL_START_V1_RLT_T *)m_diagBuff;
		uint32 state = Convert32((uint32)(pRLT->result));
		pResult->result = pRLT->result;
		memcpy(pResult->message,pRLT->message,sizeof(pResult->message));
		if (RF_OP_SUCCESS != state)
		{
			LogFmtStrA(SPLOGLV_ERROR, "[wcdmaInternalCal_Start] Operation failed, state = %d.", state);
			return SP_E_PHONE_INVALID_STATE;
		}

	}
	return res;
}

SPRESULT CCaliCmd::wcdmaInternalCal_Stop(uint32 bSaveNv)
{
	LogRawStrA(SPLOGLV_INFO, "wcdmaInternalCal_Stop:");

	L1_CALI_WCDMA_INTCAL_STOP_V1_REQ_T     L1;
	ZeroMemory((void *)&L1, sizeof(L1));
	L1.SignalCode  = Convert16((uint16)CALI_WCDMA_INTCAL_STOP_V1_REQ);
	L1.SignalSize  = Convert16((uint16)sizeof(L1_CALI_WCDMA_INTCAL_STOP_V1_REQ_T));
	L1.is_save_nv  = Convert32(bSaveNv);

	DeclareDiagHeader(hd, DIAG_RF_WCDMA_F, ZERO_SUBTYPE); 
	uint32 recvLen = 0;
	SPRESULT   res = SendAndRecv(hd,(const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);
	if (SP_OK == res)
	{                        
		if (recvLen < sizeof(L1_CALI_WCDMA_INTCAL_STOP_V1_RLT_T))
		{
			LogFmtStrA(SPLOGLV_ERROR, "[wcdmaInternalCal_Stop]Invalid response length %d!", recvLen);
			return SP_E_PHONE_INVALID_LENGTH;
		}

		L1_CALI_WCDMA_INTCAL_STOP_V1_RLT_T *pRLT = (L1_CALI_WCDMA_INTCAL_STOP_V1_RLT_T *)m_diagBuff;
		int state = Convert32((uint32)( pRLT->result));
		if (RF_OP_SUCCESS != state)
		{
			LogFmtStrA(SPLOGLV_ERROR, "[wcdmaInternalCal_Stop]  Operation failed, state = %d.", state);
			return SP_E_PHONE_INVALID_STATE;
		}
	}
	return res;
}


SPRESULT  CCaliCmd::wcdmaInternal_Debug(const PC_CALI_WCDMA_TX_SEQ_DEBUG_PS_REQ_T *pReq,PC_CALI_WCDMA_TX_SEQ_DEBUG_PS_RLT_T *pResult)
{
	CheckValidPointer(pReq);
	CheckValidPointer(pResult);
	LogRawStrA(SPLOGLV_INFO, "wcdmaInternal_Debug:");

	L1_CALI_WCDMA_TX_SEQ_DEBUG_PS_REQ_T     L1;
	ZeroMemory((void *)&L1, sizeof(L1));
	L1.SignalCode  = Convert16((uint16)CALI_WCDMA_TX_SEQ_DEBUG_REQ);
	L1.SignalSize  = Convert16((uint16)sizeof(L1_CALI_WCDMA_TX_SEQ_DEBUG_PS_REQ_T));

	L1.type = pReq->type;
	L1.reserve1 = pReq->reserve1;
	L1.reserve2 = pReq->reserve2;
	DeclareDiagHeader(hd, DIAG_RF_WCDMA_F, ZERO_SUBTYPE); 

	uint32 recvLen = 0;
	SPRESULT   res = SendAndRecv(hd,(const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);
	if (SP_OK == res)
	{                        
		L1_CALI_WCDMA_TX_SEQ_DEBUG_PS_RLT_T *pRLT = (L1_CALI_WCDMA_TX_SEQ_DEBUG_PS_RLT_T *)m_diagBuff;

		if (recvLen < pRLT->length + (sizeof(L1_CALI_WCDMA_TX_SEQ_DEBUG_PS_RLT_T) - sizeof(pRLT->buffer)))
		{
			LogFmtStrA(SPLOGLV_ERROR, "[wcdmaInternal_Debug] Invalid response length %d!", recvLen);
			return SP_E_PHONE_INVALID_LENGTH;
		}
		if(pRLT->type != L1.type)
		{
			LogFmtStrA(SPLOGLV_ERROR, "[wcdmaInternal_Debug] req type =%d,rlt type = %d,not same!", L1.type, pRLT->type);
			return SP_E_PHONE_INVALID_DATA;
		}
		pResult->type   = Convert16(pRLT->type);
		pResult->count  = Convert16(pRLT->count);
		pResult->length = Convert32(pRLT->length);
		memcpy(pResult->buffer,pRLT->buffer,pResult->length);
	}
	return res;
}

SPRESULT CCaliCmd::wcdmaQueryBand(uint32* pBandNum)
{
    LogRawStrA(SPLOGLV_INFO, "WCDMA query band:");

    L1_CALI_WCDMA_REQ_T    L1;
    ZeroMemory((void* )&L1, sizeof(L1));
    L1.SignalCode  = Convert16(CALI_WCDMA_AUTO_BAND_QUERY);
    L1.SignalSize  = Convert16((uint16)sizeof(L1));

    DeclareDiagHeader(hd, DIAG_RF_WCDMA_F, ZERO_SUBTYPE); 
    uint32 recvLen = 0;
    SPRESULT   res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);
    if (SP_OK == res)
    {
        uint32 u32ExpSize = sizeof(L1_CALI_WCDMA_QUERY_BAND_RLT_T);
        if (recvLen < u32ExpSize)
        {
            LogFmtStrA(SPLOGLV_ERROR, "Invalid response length, %d < %d!", recvLen, u32ExpSize);
            return SP_E_PHONE_INVALID_LENGTH;
        }

        L1_CALI_WCDMA_QUERY_BAND_RLT_T* pRLT = (L1_CALI_WCDMA_QUERY_BAND_RLT_T* )m_diagBuff;
        uint32 u32State = Convert32(pRLT->result);
        if (RF_OP_SUCCESS != u32State)
        {
            LogFmtStrA(SPLOGLV_ERROR, "WCDMA operation failed, state = %d.", u32State);
            return SP_E_PHONE_INVALID_STATE;
        }
        else
        {
            *pBandNum = Convert32(pRLT->band_num);
            LogFmtStrA(SPLOGLV_INFO, "Band number = %d (0x%X)", *pBandNum, *pBandNum);
        }
    }

    return res;
}


SPRESULT CCaliCmd::wcdmaNST_GetDivRSCP(LPPC_WNST_RSCP_RLT_T rlt)
{
	    CheckValidPointer(rlt);

    LogRawStrA(SPLOGLV_INFO, "WCDMA NST get Div RSCP:");

    L1_CALI_WCDMA_REQ_T    L1;
    ZeroMemory((void* )&L1, sizeof(L1));
    L1.SignalCode  = Convert16(WCDMA_NS_TEST_GET_DIV_RSCP);
    L1.SignalSize  = Convert16((uint16)sizeof(L1));

    DeclareDiagHeader(hd, DIAG_WCDMA_NS_TEST, ZERO_SUBTYPE); 

    uint32 recvLen = 0;
    SPRESULT   res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);
    if (SP_OK == res)
    {
        uint32 u32ExpSize = sizeof(L1_WCDMA_NS_TEST_RSCP_RLT_T);
        if (recvLen < u32ExpSize)
        {
            LogFmtStrA(SPLOGLV_ERROR, "Invalid response length %d < %d!", recvLen, u32ExpSize);
            return SP_E_PHONE_INVALID_LENGTH;
        }

        L1_WCDMA_NS_TEST_RSCP_RLT_T* pRLT = (L1_WCDMA_NS_TEST_RSCP_RLT_T* )m_diagBuff;
        uint32 u32State = Convert32(pRLT->result);
        if (RF_OP_SUCCESS == u32State)
        {
            rlt->rscp     = Convert32(pRLT->rscp);
            rlt->rssi     = Convert32(pRLT->rssi);
			LogFmtStrA(SPLOGLV_INFO, "DIV RSCP = 0x%X, RSSI = 0x%X", rlt->rscp, rlt->rssi);
        }
        else
        {
            LogFmtStrA(SPLOGLV_ERROR, "WCDMA NST get RSCP failed, state = %d.", u32State);
            return SP_E_PHONE_INVALID_STATE;
        }
    }

    return res;
}

SPRESULT CCaliCmd::wcdmaNST_GetDivSEBER(LPPC_WNST_SEBER_RLT_T rlt)
{
    CheckValidPointer(rlt);

    LogRawStrA(SPLOGLV_INFO, "WCDMA NST get SEBER:");

    L1_WCDMA_NS_TEST_SEBER_REQ_T   L1;
    ZeroMemory((void* )&L1, sizeof(L1));

    L1.SignalCode  = Convert16(WCDMA_NS_TEST_GET_DIVSEBER);
    L1.SignalSize  = Convert16((uint16)sizeof(L1));

    L1.total_bits  = Convert32(rlt->nTotalBits);

    DeclareDiagHeader(hd, DIAG_WCDMA_NS_TEST, ZERO_SUBTYPE); 

    uint32 recvLen = 0;
    SPRESULT   res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvLen, m_dwTimeOut);
    if (SP_OK == res)
    {
        uint32 u32ExpSize = sizeof(L1_WCDMA_NS_TEST_SEBER_RLT_T);
        if (recvLen < u32ExpSize)
        {
            LogFmtStrA(SPLOGLV_ERROR, "Invalid response length %d < %d!", recvLen, u32ExpSize);
            return SP_E_PHONE_INVALID_LENGTH;
        }

        L1_WCDMA_NS_TEST_SEBER_RLT_T* pRLT = (L1_WCDMA_NS_TEST_SEBER_RLT_T* )m_diagBuff;
        uint32 u32State = Convert32(pRLT->result);
        if (RF_OP_SUCCESS != u32State)
        {
            LogFmtStrA(SPLOGLV_ERROR, "WCDMA NST get SEBER failed, state = %d.", u32State);
            return SP_E_PHONE_INVALID_STATE;
        }

        rlt->nFailBits = Convert32(pRLT->fail_bits);
    }

    return res;
}

/// wcdma v3 begin
SPRESULT CCaliCmd::ModemV3_TraceLogInfor(uint32 nStatus)
{
    if (nStatus == RF_APP_OP_FAIL_FNST_STATUS_INVALID)
    {
        LogFmtStrA(SPLOGLV_INFO, "wcdma se ber is tesing.");
        return SP_E_PHONE_INVALID_STATE;
    }

    LogFmtStrA(SPLOGLV_ERROR, "v3 phone returned:%d", nStatus);
    return nStatus == RF_APP_OP_SUCCESS ? SP_OK : SP_E_PHONE_INVALID_DATA;
}


SPRESULT CCaliCmd::ModemV3_WCDMA_CalActive(BOOL bActive)
{
    LogFmtStrA(SPLOGLV_INFO, "%s", bActive ? "WCDMA Calibration Active" : "WCDMA Calibration DeActive");

    SPRESULT    res = SP_OK;
    uint32 recvSize = 0;

    if (bActive)
    {
        L1_CAL_WCD_ACTIVE_REQ_T L1;
        ZeroMemory((void* )&L1,  sizeof(L1));
        L1.subcmd_head.SubCmdCode  = Convert16(CAL_WCD_ACTIVE_REQ);
        L1.subcmd_head.SubCmdSize  = Convert16((uint16)sizeof(L1));

        DeclareDiagHeader(hd, DIAG_MODEM_RF_E, ZERO_SUBTYPE); 
        res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvSize, m_dwTimeOut);
    }
    else
    {
        L1_CAL_WCD_ACTIVE_REQ_T L1;
        ZeroMemory((void* )&L1,    sizeof(L1));

        L1.subcmd_head.SubCmdCode  = Convert16(CAL_WCD_DEACTIVE_REQ);
        L1.subcmd_head.SubCmdSize  = Convert16((uint16)sizeof(L1));

        DeclareDiagHeader(hd, DIAG_MODEM_RF_E, ZERO_SUBTYPE); 
        res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvSize, m_dwTimeOut);
    }

    if (SP_OK == res)
    {
        L1_CAL_WCD_ACTIVE_RLT_T* pRLT = (L1_CAL_WCD_ACTIVE_RLT_T* )m_diagBuff;        
        res = pRLT->status;        
    }
    else
    {
        LogFmtStrA(SPLOGLV_ERROR, "ModemV3_WCDMA_CalActive Fail!");
    }

    return ModemV3_TraceLogInfor(res);
}

SPRESULT CCaliCmd::ModemV3_WCDMA_ComCmd(const PC_CAL_WCD_COM_CMD_REQ_T* pReq, PC_CAL_WCD_COM_CMD_RLT_T* pRlt)
{
    CheckValidPointer(pReq);
    CheckValidPointer(pRlt);
    LogFmtStrA(SPLOGLV_INFO, "wcdma common command debug, type:%d,parameter:%d,%d,%d,%d,%d",
        pReq->type, pReq->param[0], pReq->param[1], pReq->param[2], pReq->param[3], pReq->param[4]);

    SPRESULT    res = SP_OK;
    uint32 recvSize = 0;

    L1_CAL_WCD_COM_CMD_REQ_T L1;
    ZeroMemory((void* )&L1,    sizeof(L1));

    L1.subcmd_head.SubCmdCode  = Convert16(CAL_WCD_COM_CMD_REQ);
    L1.subcmd_head.SubCmdSize  = Convert16((uint16)sizeof(L1));
    L1.type = pReq->type;
    CopyMemory(L1.param, pReq->param, sizeof(L1.param));

    DeclareDiagHeader(hd, DIAG_MODEM_RF_E, ZERO_SUBTYPE); 
    res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvSize, m_dwTimeOut);

    if (SP_OK == res)
    {
        L1_CAL_WCD_COM_CMD_RLT_T* pTempRlt = (L1_CAL_WCD_COM_CMD_RLT_T* )m_diagBuff;   
        LogFmtStrA(SPLOGLV_INFO, "status:%d, result:%d,%d,%d,%d,%d",
            pTempRlt->status, pTempRlt->result[0], pTempRlt->result[1], pTempRlt->result[2], pTempRlt->result[3], pTempRlt->result[4]);       
        pRlt->status = pTempRlt->status;
        CopyMemory(pRlt->result, pTempRlt->result, sizeof(pRlt->result));
        res = pRlt->status;        
    }

    return ModemV3_TraceLogInfor(res);
}

SPRESULT CCaliCmd::ModemV3_WCDMA_SetAfc(const PC_CAL_WCD_AFC_REQ_T* pReq, PC_CAL_WCD_AFC_RLT_T* pRlt)
{
    CheckValidPointer(pReq);
    CheckValidPointer(pRlt);
    LogFmtStrA(SPLOGLV_INFO, "wcdma set afc parameter, cdac:%d,cafc:%d,paramter:%d,%d,%d,%d",
        pReq->cdac, pReq->cafc, pReq->param[0], pReq->param[1], pReq->param[2], pReq->param[3]);

    SPRESULT    res = SP_OK;
    uint32 recvSize = 0;

    L1_CAL_WCD_AFC_REQ_T L1;
    ZeroMemory((void* )&L1,    sizeof(L1));

    L1.subcmd_head.SubCmdCode  = Convert16(CAL_WCD_AFC_REQ);
    L1.subcmd_head.SubCmdSize  = Convert16((uint16)sizeof(L1));
    L1.cdac = pReq->cdac;
    L1.cafc = pReq->cafc;
    CopyMemory(L1.param, pReq->param, sizeof(L1.param));

    DeclareDiagHeader(hd, DIAG_MODEM_RF_E, ZERO_SUBTYPE); 
    res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvSize, m_dwTimeOut);

    if (SP_OK == res)
    {
        L1_CAL_WCD_AFC_RLT_T* pTempRlt = (L1_CAL_WCD_AFC_RLT_T* )m_diagBuff; 
        LogFmtStrA(SPLOGLV_INFO, "status:%d, result:%d,%d", pTempRlt->status, pTempRlt->result[0], pTempRlt->result[1]);       
        pRlt->status = pTempRlt->status;
        CopyMemory(pRlt->result, pTempRlt->result, sizeof(pRlt->result));
        res = pRlt->status;        
    }

    return ModemV3_TraceLogInfor(res);
}

SPRESULT CCaliCmd::ModemV3_WCDMA_SetTx(const PC_CAL_WCD_TX_REQ_T* pReq, PC_CAL_WCD_TX_RLT_T* pRlt)
{
    CheckValidPointer(pReq);
    CheckValidPointer(pRlt);
    LogFmtStrA(SPLOGLV_INFO, "wcdma set tx parameter, band:%d,channel:%d,ant:%d,op_type:%d,uarfcn:%d,mode:%d,cmd_mask:%d,dcdc_value:%d,\
                             gain_index:%d, iq_com:%d, param:%d,%d,%d",
                             pReq->eBand,pReq->channel,pReq->ant,pReq->op_type,pReq->uarfcn,pReq->mode,
                             pReq->cmd_mask,pReq->dcdc_value,pReq->gain_index,pReq->iq_com,pReq->param[0],pReq->param[1],pReq->param[2]);

    SPRESULT    res = SP_OK;
    uint32 recvSize = 0;

    L1_CAL_WCD_TX_REQ_T L1;
    ZeroMemory((void* )&L1,    sizeof(L1));

    L1.subcmd_head.SubCmdCode  = Convert16(CAL_WCD_TX_REQ);
    L1.subcmd_head.SubCmdSize  = Convert16((uint16)sizeof(L1));
    L1.band = wcdmaIoBand(pReq->eBand);
    L1.channel = pReq->channel;
    L1.ant = pReq->ant;
    L1.op_type = (uint16)pReq->op_type;
    L1.uarfcn = pReq->uarfcn;
    L1.mode = (uint16)pReq->mode;
    L1.cmd_mask = (uint16)pReq->cmd_mask;
    L1.dcdc_value = pReq->dcdc_value;
    L1.gain_index = pReq->gain_index;
    L1.iq_com = pReq->iq_com;
    CopyMemory(L1.param, pReq->param, sizeof(L1.param));

    DeclareDiagHeader(hd, DIAG_MODEM_RF_E, ZERO_SUBTYPE); 
    res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvSize, m_dwTimeOut);

    if (SP_OK == res)
    {
        L1_CAL_WCD_TX_RLT_T* pTempRlt = (L1_CAL_WCD_TX_RLT_T* )m_diagBuff; 
        LogFmtStrA(SPLOGLV_INFO, "status:%d, gain_code:%d,pdet_value:%d,%d,%d,dcdc_value:%d,result:%d,%d,%d,%d", 
            pTempRlt->status, pTempRlt->gain_code, pTempRlt->pdet_value[0], pTempRlt->pdet_value[1], pTempRlt->pdet_value[2],
            pTempRlt->dcdc_value, pTempRlt->result[0], pTempRlt->result[1], pTempRlt->result[2], pTempRlt->result[3]);       
        pRlt->status = pTempRlt->status;
        pRlt->gain_code = pTempRlt->gain_code;
        pRlt->dcdc_value = pTempRlt->dcdc_value;
        CopyMemory(pRlt->pdet_value, pTempRlt->pdet_value, sizeof(pRlt->pdet_value));
        CopyMemory(pRlt->result, pTempRlt->result, sizeof(pRlt->result));
        res = pRlt->status;        
    }

    return ModemV3_TraceLogInfor(res);
}

SPRESULT CCaliCmd::ModemV3_WCDMA_SetRx(const PC_CAL_WCD_RX_REQ_T* pReq, PC_CAL_WCD_RX_RLT_T* pRlt)
{
    CheckValidPointer(pReq);
    CheckValidPointer(pRlt);
    LogFmtStrA(SPLOGLV_INFO, "wcdma set rx parameter, band:%d,channel:%d,ant:%d,op_type:%d,uarfcn:%d,pri_gain_index:%d,\
                              div_gain_index:%d, param:%d,%d,%d,%d",
                             pReq->eBand,pReq->channel,pReq->ant,pReq->op_type,pReq->uarfcn,pReq->pri_gain_index,
                             pReq->div_gain_index, pReq->param[0],pReq->param[1],pReq->param[2],pReq->param[3]);

    SPRESULT    res = SP_OK;
    uint32 recvSize = 0;

    L1_CAL_WCD_RX_REQ_T L1;
    ZeroMemory((void* )&L1,    sizeof(L1));

    L1.subcmd_head.SubCmdCode  = Convert16(CAL_WCD_RX_REQ);
    L1.subcmd_head.SubCmdSize  = Convert16((uint16)sizeof(L1));
    L1.band = wcdmaIoBand(pReq->eBand);
    L1.channel = pReq->channel;
    L1.ant = pReq->ant;
    L1.op_type = (uint16)pReq->op_type;
    L1.uarfcn = pReq->uarfcn;
    L1.pri_gain_index = pReq->pri_gain_index;
    L1.div_gain_index = pReq->div_gain_index;
    CopyMemory(L1.param, pReq->param, sizeof(L1.param));

    DeclareDiagHeader(hd, DIAG_MODEM_RF_E, ZERO_SUBTYPE); 
    res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvSize, m_dwTimeOut);

    if (SP_OK == res)
    {
        L1_CAL_WCD_RX_RLT_T* pTempRlt = (L1_CAL_WCD_RX_RLT_T* )m_diagBuff; 
        LogFmtStrA(SPLOGLV_INFO, "status:%d, pri_gain_code:%d,div_gain_code:%d,pri_rfic_rssi:%d,div_rfic_rssi:%d,\
                                 pri_dfe_rssi:%d,div_dfe_rssi:%d,result:%d,%d,%d,%d", 
            pTempRlt->status, pTempRlt->pri_gain_code, pTempRlt->div_gain_code, pTempRlt->pri_rfic_rssi, pTempRlt->div_rfic_rssi,
            pTempRlt->pri_dfe_rssi, pTempRlt->div_dfe_rssi, pTempRlt->result[0], pTempRlt->result[1], pTempRlt->result[2], pTempRlt->result[3]);       
        pRlt->status = pTempRlt->status;
        pRlt->pri_gain_code = pTempRlt->pri_gain_code;
        pRlt->div_gain_code = pTempRlt->div_gain_code;
        pRlt->pri_rfic_rssi = pTempRlt->pri_rfic_rssi;
        pRlt->div_rfic_rssi = pTempRlt->div_rfic_rssi;
        pRlt->pri_dfe_rssi = pTempRlt->pri_dfe_rssi;
        pRlt->div_dfe_rssi = pTempRlt->div_dfe_rssi;
        CopyMemory(pRlt->result, pTempRlt->result, sizeof(pRlt->result));
        res = pRlt->status;        
    }

    return ModemV3_TraceLogInfor(res);
}

SPRESULT CCaliCmd::ModemV3_WCDMA_SetIrr(const PC_CAL_WCD_IRR_REQ_T* pReq, PC_CAL_WCD_IRR_RLT_T* pRlt)
{
    CheckValidPointer(pReq);
    CheckValidPointer(pRlt);
    LogFmtStrA(SPLOGLV_INFO, "wcdma set irr parameter, algo_version:%d, param:%d,%d",
                             pReq->algo_ver, pReq->param[0],pReq->param[1]);

    SPRESULT    res = SP_OK;
    uint32 recvSize = 0;

    L1_CAL_WCD_IRR_REQ_T L1;
    ZeroMemory((void* )&L1,    sizeof(L1));

    L1.subcmd_head.SubCmdCode  = Convert16(CAL_WCD_IRR_REQ);
    L1.subcmd_head.SubCmdSize  = Convert16((uint16)sizeof(L1));
    L1.algo_ver = pReq->algo_ver;
    CopyMemory(L1.param, pReq->param, sizeof(L1.param));

    DeclareDiagHeader(hd, DIAG_MODEM_RF_E, ZERO_SUBTYPE); 
    res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvSize, m_dwTimeOut);

    if (SP_OK == res)
    {
        L1_CAL_WCD_IRR_RLT_T* pTempRlt = (L1_CAL_WCD_IRR_RLT_T* )m_diagBuff; 
        LogFmtStrA(SPLOGLV_INFO, "status:%d, result:%d,%d,%d,%d", 
                                 pTempRlt->status, pTempRlt->result[0], pTempRlt->result[1], pTempRlt->result[2], pTempRlt->result[3]);       
        pRlt->status = pTempRlt->status;
        CopyMemory(pRlt->result, pTempRlt->result, sizeof(pRlt->result));
        res = pRlt->status;        
    }

    return ModemV3_TraceLogInfor(res);
}

SPRESULT CCaliCmd::ModemV3_WCDMA_SetTxRx(const PC_CAL_WCD_TXRX_SEQ_REQ_T* pReq, PC_CAL_WCD_TXRX_SEQ_RLT_T* pRlt)
{
    CheckValidPointer(pReq);
    CheckValidPointer(pRlt);
    LogFmtStrA(SPLOGLV_INFO, "wcdma set txrx parameter, inerval:%d,inter_cnt_change_ch:%d,version:%d,reserved:%d,ant:%d,\
                             dft_rx_pri_gain_index:%d,dft_rx_div_gain_index:%d,dft_tx_mode:%d,dft_tx_gain_index:%d,\
                             dft_tx_dcdc_value:%d,tx_cmd_mask:%d,total_band_cnt:%d,total_rx_cnt:%d,total_tx_cnt:%d",
                             pReq->config.interval,pReq->config.inter_cnt_change_ch,pReq->config.version,pReq->config.reserved,
                             pReq->config.ant,pReq->config.dft_rx_pri_gain_index,pReq->config.dft_rx_div_gain_index,
                             pReq->config.dft_tx_mode, pReq->config.dft_tx_gain_index,pReq->config.dft_tx_dcdc_value,
                             pReq->config.tx_cmd_mask,pReq->config.freq_total_cnt,pReq->config.rx_total_cnt,pReq->config.tx_total_cnt);

    /// trace band,tx,rx information
    uint32 i = 0;    
    L1_CAL_WCD_TXRX_SEQ_REQ_T L1;
    ZeroMemory((void* )&L1,    sizeof(L1));
    L1.subcmd_head.SubCmdCode  = Convert16(CAL_WCD_TXRX_SEQ_REQ);    

    L1.config.ant = pReq->config.ant;
	L1.config.freq_total_cnt =  pReq->config.freq_total_cnt;
    L1.config.dft_rx_div_gain_index = pReq->config.dft_rx_div_gain_index;
    L1.config.dft_rx_pri_gain_index = pReq->config.dft_rx_pri_gain_index;
    L1.config.dft_tx_dcdc_value = pReq->config.dft_tx_dcdc_value;
    L1.config.dft_tx_gain_index = pReq->config.dft_tx_gain_index;
    L1.config.dft_tx_mode = (uint8)pReq->config.dft_tx_mode;
    L1.config.inter_cnt_change_ch = pReq->config.inter_cnt_change_ch;
    L1.config.interval = pReq->config.interval;
    L1.config.reserved = pReq->config.reserved;
    L1.config.rx_total_cnt = pReq->config.rx_total_cnt;
    L1.config.tx_cmd_mask = (uint16)pReq->config.tx_cmd_mask;
    L1.config.tx_total_cnt = pReq->config.tx_total_cnt;
    L1.config.version = pReq->config.version;

    uint8* pBuf = NULL;
    int nBufSize = sizeof(L1_SUBCMD_HEAD_T)
            +sizeof(L1_CAL_WCD_TXRX_CONFIG_ENTRY_T)
            +pReq->config.freq_total_cnt*sizeof(L1_CAL_WCD_BAND_ENTRY_T)
            +pReq->config.rx_total_cnt*sizeof(L1_CAL_WCD_RX_ENTRY_T)
            +pReq->config.tx_total_cnt*sizeof(L1_CAL_WCD_TX_ENTRY_T);
    L1.subcmd_head.SubCmdSize  = (uint16)nBufSize;
    try
    {
        pBuf = new uint8[nBufSize];
    }
    catch (const std::bad_alloc& /*e*/)
    {
        pBuf = NULL;
    }
    if (NULL == pBuf)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Memory allocation failed.", __FUNCTION__);
        return SP_E_PHONE_ALLOC_MEMORY;
    }

    int nPos = 0;
    CopyMemory(pBuf+nPos, &L1.subcmd_head, sizeof(L1_SUBCMD_HEAD_T));
    nPos += sizeof(L1_SUBCMD_HEAD_T);

    CopyMemory(pBuf+nPos, &L1.config, sizeof(L1_CAL_WCD_TXRX_CONFIG_ENTRY_T));
    nPos += sizeof(L1_CAL_WCD_TXRX_CONFIG_ENTRY_T);
    LogFmtStrA(SPLOGLV_INFO, "Freq information:");
    for (i = 0; i < pReq->config.freq_total_cnt; i++)
    {
        LogFmtStrA(SPLOGLV_INFO, "index:%d,band:%d,rx_channel:%d,tx_channel:%d,reserved:%d,rx_cnt:%d,tx_cnt:%d,rx_uarfcn:%d,tx_uarfcn:%d",
            i,pReq->config.freq[i].band,pReq->config.freq[i].rx_channel,pReq->config.freq[i].tx_channel,pReq->config.freq[i].reserved,
            pReq->config.freq[i].rx_cnt,pReq->config.freq[i].tx_cnt,pReq->config.freq[i].rx_uarfcn,pReq->config.freq[i].tx_uarfcn);
        CopyMemory(pBuf+nPos, (void*)&pReq->config.freq[i], sizeof(L1_CAL_WCD_BAND_ENTRY_T));
        nPos += sizeof(L1_CAL_WCD_BAND_ENTRY_T);
    }

    // rx
    LogFmtStrA(SPLOGLV_INFO, "Rx information:");
    for(i = 0; i < pReq->config.rx_total_cnt; i++)
    {
        LogFmtStrA(SPLOGLV_INFO, "index:%d,pri_gain_index:%d,div_gain_index:%d", i,pReq->config.rx[i].pri_gain_index,pReq->config.rx[i].div_gain_index);
        CopyMemory(pBuf+nPos, (void*)&pReq->config.rx[i], sizeof(L1_CAL_WCD_RX_ENTRY_T));
        nPos += sizeof(L1_CAL_WCD_RX_ENTRY_T);
    }

    // tx
    LogFmtStrA(SPLOGLV_INFO, "Tx information:");
    for(i = 0; i < pReq->config.tx_total_cnt; i++)
    {
        LogFmtStrA(SPLOGLV_INFO, "index:%d,start_gain_index:%d,gain_index_step:%d,step_num:%d,mode:%d,dcdc_value:%d",
            i,pReq->config.tx[i].start_gain_index,pReq->config.tx[i].gain_index_step,pReq->config.tx[i].step_num,
            pReq->config.tx[i].mode,pReq->config.tx[i].dcdc_value);
        CopyMemory(pBuf+nPos, (void*)&pReq->config.tx[i], sizeof(L1_CAL_WCD_TX_ENTRY_T));
        nPos += sizeof(L1_CAL_WCD_TX_ENTRY_T);
    }
      


    SPRESULT    res = SP_OK;
    uint32 recvSize = 0;      

    DeclareDiagHeader(hd, DIAG_MODEM_RF_E, ZERO_SUBTYPE); 
    res = SendAndRecv(hd, (const void* )pBuf, nBufSize, hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvSize, m_dwTimeOut);

    if (SP_OK == res)
    {
        L1_CAL_WCD_TXRX_SEQ_RLT_T* pTempRlt = (L1_CAL_WCD_TXRX_SEQ_RLT_T* )m_diagBuff; 
        LogFmtStrA(SPLOGLV_INFO, "status:%d, result:%d,%d", pTempRlt->status, pTempRlt->result[0], pTempRlt->result[1]);        
        pRlt->status = pTempRlt->status;
        CopyMemory(pRlt->result, pTempRlt->result, sizeof(pRlt->result));
        res = pRlt->status;        
    }

    delete[] pBuf;
    return ModemV3_TraceLogInfor(res);
}

SPRESULT CCaliCmd::ModemV3_WCDMA_GetTxRx(const PC_CAL_WCD_TXRX_RLT_REQ_T* pReq, PC_CAL_WCD_TXRX_RLT_RLT_T* pRlt)
{
    CheckValidPointer(pReq);
    CheckValidPointer(pRlt);
    LogFmtStrA(SPLOGLV_INFO, "wcdma get txrx result, type:%d, param:%d,%d",pReq->type, pReq->param[0], pReq->param[1]);

    SPRESULT    res = SP_OK;
    uint32 recvSize = 0;

    L1_CAL_WCD_TXRX_RLT_REQ_T L1;
    ZeroMemory((void* )&L1,    sizeof(L1));

    L1.subcmd_head.SubCmdCode  = Convert16(CAL_WCD_TXRX_RLT_REQ);
    L1.subcmd_head.SubCmdSize  = Convert16((uint16)sizeof(L1));

    L1.type = pReq->type;
    CopyMemory(L1.param, pReq->param, sizeof(L1.param));

    DeclareDiagHeader(hd, DIAG_MODEM_RF_E, ZERO_SUBTYPE); 
    res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvSize, m_dwTimeOut);

    if (SP_OK == res)
    {
        L1_CAL_WCD_TXRX_RLT_RLT_T* pTempRlt = (L1_CAL_WCD_TXRX_RLT_RLT_T*)m_diagBuff;
        LogFmtStrA(SPLOGLV_INFO, "status:%d", pTempRlt->status);

        int nPos = sizeof(L1_SUBCMD_HEAD_T) + sizeof(uint32) + sizeof(uint32);
        uint32 nDataLen = pTempRlt->len;
        uint32 nExpectDataLen = pRlt->len;

        if (nDataLen != nExpectDataLen)
        {
            LogFmtStrA(SPLOGLV_ERROR, "The data length returned from dut is invalid. Current length:%d, expcectd:%d",
                nDataLen, nExpectDataLen);
            return SP_E_SPAT_INVALID_RESPONSE;
        }
        int nDataSize = 0;
        /// trace result
        uint32 i = 0;
        if (L1.type == RF_WCD_TX_RX_SEQ_RLT_PDET)
        {
            L1_CAL_WCD_TX_PDET_GROUP_T* pPdetRlt = (L1_CAL_WCD_TX_PDET_GROUP_T*)(m_diagBuff+nPos);
            LogFmtStrA(SPLOGLV_INFO, "Hdet Result:");
            for (i = 0; i < nDataLen; i++)
            {
                LogFmtStrA(SPLOGLV_INFO, "index:%d,hdet:%d,%d,%d", i, pPdetRlt[i].pdet[0], pPdetRlt[i].pdet[1],pPdetRlt[i].pdet[2]);
            }
            nDataSize = sizeof(L1_CAL_WCD_TX_PDET_GROUP_T);
        }
        else if (L1.type == RF_WCD_TX_RX_SEQ_RLT_RX_RSSI)
        {
            L1_CAL_WCD_RX_RSSI_GROUP_T* pRssiRlt = (L1_CAL_WCD_RX_RSSI_GROUP_T*)(m_diagBuff+nPos);
            LogFmtStrA(SPLOGLV_INFO, "RX Rssi Result:");
            for (i = 0; i < nDataLen; i++)
            {
                LogFmtStrA(SPLOGLV_INFO, "index:%d,pri_rssi:%d,div_rssi:%d", i, pRssiRlt[i].pri_rssi, pRssiRlt[i].div_rssi);
            }
            nDataSize = sizeof(L1_CAL_WCD_RX_RSSI_GROUP_T);
        }
        else if (L1.type == RF_WCD_TX_RX_SEQ_RLT_TX_RSSI)
        {
            L1_CAL_WCD_TX_RSSI_GROUP_T* pRssiRlt = (L1_CAL_WCD_TX_RSSI_GROUP_T*)(m_diagBuff+nPos);
            LogFmtStrA(SPLOGLV_INFO, "TX Rssi Result:");
            for (i = 0; i < nDataLen; i++)
            {
                LogFmtStrA(SPLOGLV_INFO, "index:%d,rssi:%d", i, pRssiRlt[i].rssi);
            }
            nDataSize = sizeof(L1_CAL_WCD_TX_RSSI_GROUP_T);
        }
        else if (L1.type == RF_WCD_TX_RX_SEQ_RLT_TX_RSSI_DBG)
        {
            L1_CAL_WCD_TX_RSSI_DEBUG_GROUP_T* pRssiRlt = (L1_CAL_WCD_TX_RSSI_DEBUG_GROUP_T*)(m_diagBuff+nPos);
            LogFmtStrA(SPLOGLV_INFO, "TX Debug Rssi Result:");
            for (i = 0; i < nDataLen; i++)
            {
                LogFmtStrA(SPLOGLV_INFO, "index:%d,band:%d,tx_gain_mode:%d,tx_dcdc_index:%d,tx_gain_index:%d,tx_uarfcn:%d,\
                                 rx_pri_rssi_gain_index:%d,rx_pri_gain_index_db_delta:%d,rx_pri_gain_code:%d", 
                                 i, pRssiRlt[i].band,pRssiRlt[i].tx_gain_mode,pRssiRlt[i].tx_dcdc_index,
                                 pRssiRlt[i].tx_gain_index,pRssiRlt[i].tx_uarfcn,pRssiRlt[i].rx_pri_rssi_gain_index,
                                 pRssiRlt[i].rx_pri_gain_index_db_delta,pRssiRlt[i].rx_pri_gain_code);
            }
            nDataSize = sizeof(L1_CAL_WCD_TX_RSSI_DEBUG_GROUP_T);
        }
        pRlt->status = pTempRlt->status;
        CopyMemory((void*)(pRlt->data), m_diagBuff+nPos, nDataLen*nDataSize);
        res = pRlt->status;        
    }

    return ModemV3_TraceLogInfor(res);
}

SPRESULT CCaliCmd::ModemV3_WCDMA_QueryBand(const PC_CAL_WCD_BAND_QUERY_REQ_T* pReq, PC_CAL_WCD_BAND_QUERY_RLT_T* pRlt)
{
    CheckValidPointer(pReq);
    CheckValidPointer(pRlt);
    LogFmtStrA(SPLOGLV_INFO, "wcdma query band, param:%d,%d", pReq->param[0], pReq->param[1]);

    SPRESULT    res = SP_OK;
    uint32 recvSize = 0;

    L1_CAL_WCD_BAND_QUERY_REQ_T L1;
    ZeroMemory((void* )&L1,    sizeof(L1));

    L1.subcmd_head.SubCmdCode  = Convert16(CAL_WCD_BAND_QUERY_REQ);
    L1.subcmd_head.SubCmdSize  = Convert16((uint16)sizeof(L1));

    CopyMemory(L1.param, pReq->param, sizeof(L1.param));

    DeclareDiagHeader(hd, DIAG_MODEM_RF_E, ZERO_SUBTYPE); 
    res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvSize, m_dwTimeOut);

    if (SP_OK == res)
    {
        L1_CAL_WCD_BAND_QUERY_RLT_T* pTempRlt = (L1_CAL_WCD_BAND_QUERY_RLT_T* )m_diagBuff; 
        char szBand[64] = {0};
        char szTemp[8] = {0};
        for (int i = 0; i < 32; i++)
        {
            if ((pTempRlt->band_bitmap & (1<<i)) != 0)
            {
                sprintf_s(szTemp, "%d,", i+1);
                strcat_s(szBand, szTemp);
            }
        }
        int nEnd = strlen(szBand);
        szBand[nEnd-1] = '\0';
        
        LogFmtStrA(SPLOGLV_INFO, "status:%d, band:%s", pTempRlt->status, szBand);    
        pRlt->status = pTempRlt->status;
        pRlt->band_bitmap = pTempRlt->band_bitmap;
        CopyMemory(pRlt->result, pTempRlt->result, sizeof(pRlt->result));
        res = pRlt->status;        
    }

    return ModemV3_TraceLogInfor(res);
}

SPRESULT CCaliCmd::ModemV3_WCDMA_NstActive(BOOL bActive)
{
    LogFmtStrA(SPLOGLV_INFO, "%s", bActive ? "WCDMA NST Active" : "WCDMA NST DeActive");

    SPRESULT    res = SP_OK;
    uint32 recvSize = 0;

    if (bActive)
    {
        L1_NST_WCD_ACTIVE_REQ_T L1;
        ZeroMemory((void* )&L1,  sizeof(L1));
        L1.subcmd_head.SubCmdCode  = Convert16(NST_WCD_ACTIVE_REQ);
        L1.subcmd_head.SubCmdSize  = Convert16((uint16)sizeof(L1));

        DeclareDiagHeader(hd, DIAG_MODEM_RF_E, ZERO_SUBTYPE); 
        res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvSize, m_dwTimeOut);
    }
    else
    {
        L1_NST_WCD_ACTIVE_REQ_T L1;
        ZeroMemory((void* )&L1,    sizeof(L1));

        L1.subcmd_head.SubCmdCode  = Convert16(NST_WCD_DEACTIVE_REQ);
        L1.subcmd_head.SubCmdSize  = Convert16((uint16)sizeof(L1));

        DeclareDiagHeader(hd, DIAG_MODEM_RF_E, ZERO_SUBTYPE); 
        res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvSize, m_dwTimeOut);
    }

    if (SP_OK == res)
    {
        L1_NST_WCD_ACTIVE_RLT_T* pRLT = (L1_NST_WCD_ACTIVE_RLT_T* )m_diagBuff;    
        res = pRLT->status;        
    }

    return ModemV3_TraceLogInfor(res);
}

SPRESULT CCaliCmd::ModemV3_WCDMA_NstSetMode(NST_WCD_MODE_TYPE_E eMode)
{
    LogFmtStrA(SPLOGLV_INFO, "ModemV3_WCDMA_NstSetMode:%d", eMode);

    SPRESULT    res = SP_OK;
    uint32 recvSize = 0;

     L1_NST_WCD_COM_CMD_REQ_T L1;
     ZeroMemory((void* )&L1,    sizeof(L1));

     L1.subcmd_head.SubCmdCode  = Convert16(NST_WCD_COM_CMD_REQ);
     L1.subcmd_head.SubCmdSize  = Convert16((uint16)sizeof(L1));
     L1.type = 0;
     L1.param[0] = eMode;

     DeclareDiagHeader(hd, DIAG_MODEM_RF_E, ZERO_SUBTYPE); 
     res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvSize, m_dwTimeOut);

    if (SP_OK == res)
    {
        L1_NST_WCD_COM_CMD_RLT_T* pRLT = (L1_NST_WCD_COM_CMD_RLT_T* )m_diagBuff;    
        res = pRLT->status;        
    }

    return ModemV3_TraceLogInfor(res);
}

SPRESULT CCaliCmd::ModemV3_WCDMA_NstStart(const PC_NST_WCD_START_REQ_T* pReq, PC_NST_WCD_START_RLT_T* pRlt)
{
    CheckValidPointer(pReq);
    CheckValidPointer(pRlt);
    LogFmtStrA(SPLOGLV_INFO, "wcdma nst start, band:%d,dl_uarfcn:%d,tx_channel:%d,rx_channel:%d\
                             slot_format:%d,pgc:%d,channel_code:%d,tpc_algo:%d,tpc_step_size:%d,\
                             scrambling_code_num:%d",
                             pReq->band, pReq->dl_uarfcn,pReq->tx_channel,pReq->rx_channel,pReq->slot_format,
                             pReq->pgc,pReq->channel_code,pReq->tpc_algo, pReq->tpc_step_size,pReq->scrambling_code_num);

    SPRESULT    res = SP_OK;
    uint32 recvSize = 0;

    L1_NST_WCD_START_REQ_T L1;
    ZeroMemory((void* )&L1,    sizeof(L1));

    L1.subcmd_head.SubCmdCode  = Convert16(NST_WCD_START_REQ);
    L1.subcmd_head.SubCmdSize  = Convert16((uint16)sizeof(L1));
    
    L1.band = wcdmaIoBand(pReq->band);
    L1.channel_code = pReq->channel_code;
    L1.dl_uarfcn = pReq->dl_uarfcn;
    L1.pgc = pReq->pgc;
    L1.rx_channel = pReq->rx_channel;
    L1.scrambling_code_num = pReq->scrambling_code_num;
    L1.slot_format = pReq->slot_format;
    L1.tpc_algo = pReq->tpc_algo;
    L1.tpc_step_size = pReq->tpc_step_size;
    L1.tx_channel = pReq->tx_channel;

    CopyMemory(L1.reserved, pReq->reserved, sizeof(L1.reserved));

    DeclareDiagHeader(hd, DIAG_MODEM_RF_E, ZERO_SUBTYPE); 
    res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvSize, m_dwTimeOut);

    if (SP_OK == res)
    {
        L1_NST_WCD_START_RLT_T* pTempRlt = (L1_NST_WCD_START_RLT_T* )m_diagBuff;
        LogFmtStrA(SPLOGLV_INFO, "status:%d, result:%d,%d", pTempRlt->status, pTempRlt->result[0],pTempRlt->result[1]);       
        pRlt->status = pTempRlt->status;
        CopyMemory(pRlt->result, pTempRlt->result, sizeof(pRlt->result));
        res = pRlt->status;        
    }

    return ModemV3_TraceLogInfor(res);
}

SPRESULT CCaliCmd::ModemV3_WCDMA_NstReconfig(const PC_NST_WCD_RECONFIG_REQ_T* pReq, PC_NST_WCD_RECONFIG_RLT_T* pRlt)
{
    CheckValidPointer(pReq);
    CheckValidPointer(pRlt);
    LogFmtStrA(SPLOGLV_INFO, "wcdma nst reconfig, slot_format:%d,channel_code:%d,tpc_algo:%d,tpc_step_size:%d,\
                             scrambling_code_num:%d, power_type:%d, power_value:%d", 
                             pReq->slot_format,pReq->channel_code,pReq->tpc_algo,pReq->tpc_step_size,
                             pReq->scrambling_code_num,pReq->power_type, pReq->power_value);

    SPRESULT    res = SP_OK;
    uint32 recvSize = 0;

    L1_NST_WCD_RECONFIG_REQ_T L1;
    ZeroMemory((void* )&L1,    sizeof(L1));

    L1.subcmd_head.SubCmdCode  = Convert16(NST_WCD_RECONFIG_REQ);
    L1.subcmd_head.SubCmdSize  = Convert16((uint16)sizeof(L1));

    L1.channel_code = pReq->channel_code;
    L1.scrambling_code_num = pReq->scrambling_code_num;
    L1.slot_format = pReq->slot_format;
    L1.tpc_algo = pReq->tpc_algo;
    L1.tpc_step_size = pReq->tpc_step_size;
    L1.power_type = (uint32)pReq->power_type;
    L1.power_value = pReq->power_value;

    CopyMemory(L1.reserved, pReq->reserved, sizeof(L1.reserved));

    DeclareDiagHeader(hd, DIAG_MODEM_RF_E, ZERO_SUBTYPE); 
    res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvSize, m_dwTimeOut);

    if (SP_OK == res)
    {
        L1_NST_WCD_RECONFIG_RLT_T* pTempRlt = (L1_NST_WCD_RECONFIG_RLT_T* )m_diagBuff;
        LogFmtStrA(SPLOGLV_INFO, "status:%d, result:%d,%d", pTempRlt->status, pTempRlt->result[0],pTempRlt->result[1]);       
        pRlt->status = pTempRlt->status;
        CopyMemory(pRlt->result, pTempRlt->result, sizeof(pRlt->result));
        res = pRlt->status;        
    }

    return ModemV3_TraceLogInfor(res);
}

SPRESULT CCaliCmd::ModemV3_WCDMA_NstStop(const PC_NST_WCD_STOP_REQ_T* pReq, PC_NST_WCD_STOP_RLT_T* pRlt)
{
    CheckValidPointer(pReq);
    CheckValidPointer(pRlt);
    LogFmtStrA(SPLOGLV_INFO, "wcdma nst stop, dummy:%d",pReq->dummy);

    SPRESULT    res = SP_OK;
    uint32 recvSize = 0;

    L1_NST_WCD_STOP_REQ_T L1;
    ZeroMemory((void* )&L1,    sizeof(L1));

    L1.subcmd_head.SubCmdCode  = Convert16(NST_WCD_STOP_REQ);
    L1.subcmd_head.SubCmdSize  = Convert16((uint16)sizeof(L1));

    L1.dummy = pReq->dummy;

    DeclareDiagHeader(hd, DIAG_MODEM_RF_E, ZERO_SUBTYPE); 
    res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvSize, m_dwTimeOut);

    if (SP_OK == res)
    {
        L1_NST_WCD_STOP_RLT_T* pTempRlt = (L1_NST_WCD_STOP_RLT_T* )m_diagBuff;
        LogFmtStrA(SPLOGLV_INFO, "status:%d, result:%d,%d", pTempRlt->status, pTempRlt->result[0],pTempRlt->result[1]);      
        pRlt->status = pTempRlt->status;
        CopyMemory(pRlt->result, pTempRlt->result, sizeof(pRlt->result));
        res = pRlt->status;        
    }

    return ModemV3_TraceLogInfor(res);
}

SPRESULT CCaliCmd::ModemV3_WCDMA_NstStartSeBer(const PC_NST_WCD_START_SEBER_REQ_T* pReq, PC_NST_WCD_START_SEBER_RLT_T* pRlt)
{
    CheckValidPointer(pReq);
    CheckValidPointer(pRlt);
    LogFmtStrA(SPLOGLV_INFO, "wcdma nst start se-ber, ant:%d,padding:%d,total_bit_num:%d,param:%d,%d",
        pReq->ant,pReq->padding,pReq->total_bit_num,pReq->param[0],pReq->param[1]);

    SPRESULT    res = SP_OK;
    uint32 recvSize = 0;

    L1_NST_WCD_START_SEBER_REQ_T L1;
    ZeroMemory((void* )&L1,    sizeof(L1));

    L1.subcmd_head.SubCmdCode  = Convert16(NST_WCD_START_SEBER_SEQ);
    L1.subcmd_head.SubCmdSize  = Convert16((uint16)sizeof(L1));

    L1.ant = pReq->ant;
    L1.padding = pReq->padding;
    L1.total_bit_num = pReq->total_bit_num;
    CopyMemory(L1.param, pReq->param, sizeof(L1.param));

    DeclareDiagHeader(hd, DIAG_MODEM_RF_E, ZERO_SUBTYPE); 
    res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvSize, m_dwTimeOut);

    if (SP_OK == res)
    {
        L1_NST_WCD_START_SEBER_RLT_T* pTempRlt = (L1_NST_WCD_START_SEBER_RLT_T* )m_diagBuff;
        LogFmtStrA(SPLOGLV_INFO, "status:%d, result:%d,%d", pTempRlt->status, pTempRlt->result[0],pTempRlt->result[1]);        
        pRlt->status = pTempRlt->status;
        CopyMemory(pRlt->result, pTempRlt->result, sizeof(pRlt->result));
        res = pRlt->status;        
    }

    return ModemV3_TraceLogInfor(res);
}

SPRESULT CCaliCmd::ModemV3_WCDMA_NstGetAusRSCP( const PC_NST_WCD_AUS_RSCP_REQ_T* pReq, PC_NST_WCD_AUS_RSCP_RLT_T* pRlt, uint32 u32TimeOut )
{
    CheckValidPointer( pReq );
    CheckValidPointer( pRlt );
    LogFmtStrA( SPLOGLV_INFO, "wcdma nst get aus rscp result, type:%d", pReq->type );
    
    uint32 recvSize = 0;
    
    L1_NST_WCD_AUS_RSCP_REQ_T L1;
    ZeroMemory( ( void* )&L1,    sizeof( L1 ) );
    
    L1.subcmd_head.SubCmdCode  = Convert16( NST_WCD_AUS_RSCP_REQ );
    L1.subcmd_head.SubCmdSize  = Convert16( ( uint16 )sizeof( L1 ) );
    
    L1.type = ( uint32 )pReq->type;
    
    for ( int i = 0; i < 5; i++ )
    {
        L1.data[i].nRscpDelta64 = pReq->data[i].nRscpDelta64;
        L1.data[i].nAgain = pReq->data[i].nAgain;
    }
    
    DeclareDiagHeader( hd, DIAG_MODEM_RF_E, ZERO_SUBTYPE );
    CHKRESULT( SendAndRecv( hd, ( const void* )&L1, sizeof( L1 ), hd, ( void* )m_diagBuff, sizeof( m_diagBuff ), &recvSize, u32TimeOut ) );
    
	if (recvSize < sizeof(L1_NST_WCD_AUS_RSCP_RLT_T))
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid response package size.", __FUNCTION__);
		return SP_E_PHONE_INVALID_LENGTH;
	}

	CHKRESULT(DetermineStatus(NST_WCD_AUS_RSCP_REQ, m_diagBuff, recvSize));

    L1_NST_WCD_AUS_RSCP_RLT_T* pTempRlt = ( L1_NST_WCD_AUS_RSCP_RLT_T* )( m_diagBuff );

    LogFmtStrA( SPLOGLV_INFO, "status:%d, type:%d", pTempRlt->status, pTempRlt->type );
        
	for ( int i = 0; i < 5; i++ )
	{
		pRlt->data[i].rscp = pTempRlt->data[i].rscp ;
		pRlt->data[i].rssi = pTempRlt->data[i].rssi ;
		pRlt->data[i].again = pTempRlt->data[i].again ;
		LogFmtStrA( SPLOGLV_INFO, "Index:%d: RSCP = 0x%X, RSSI = 0x%X, AGAIN = 0x%X, Reserved0  = 0x%X, Reserved1  = 0x%X, Reserved2  = 0x%X",
			i, 
			pRlt->data[i].rscp, 
			pRlt->data[i].rssi, 
			pRlt->data[i].again, 
			pTempRlt->data[i].reserved[0], 
			pTempRlt->data[i].reserved[1], 
			pTempRlt->data[i].reserved[2] );
	}

    return SP_OK;
}

SPRESULT CCaliCmd::ModemV3_WCDMA_NstGetRscp(const PC_NST_WCD_GET_RLT_REQ_T* pReq, PC_NST_WCD_GET_RLT_RSCP_T* pRlt)
{
    CheckValidPointer(pReq);
    CheckValidPointer(pRlt);
    LogFmtStrA(SPLOGLV_INFO, "wcdma nst get rscp result, type:%d,ant:%d,param:%d,%d",pReq->type,pReq->ant,pReq->param[0],pReq->param[1]);
    if (pReq->type != NST_WCD_RLT_RX_LEVEL)
    {
        LogFmtStrA(SPLOGLV_ERROR, "The type of input is invalid.");
        return SP_E_INVALID_PARAMETER;
    }

    SPRESULT    res = SP_OK;
    uint32 recvSize = 0;

    L1_NST_WCD_GET_RLT_REQ_T L1;
    ZeroMemory((void* )&L1,    sizeof(L1));

    L1.subcmd_head.SubCmdCode  = Convert16(NST_WCD_GET_RLT_REQ);
    L1.subcmd_head.SubCmdSize  = Convert16((uint16)sizeof(L1));

    L1.ant = pReq->ant;
    L1.type = (uint16)pReq->type;
    CopyMemory(L1.param, pReq->param, sizeof(L1.param));

    DeclareDiagHeader(hd, DIAG_MODEM_RF_E, ZERO_SUBTYPE); 
    res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvSize, m_dwTimeOut);

    if (SP_OK == res)
    {
        L1_NST_WCD_GET_RLT_RSCP_T* pTempRlt = (L1_NST_WCD_GET_RLT_RSCP_T*)(m_diagBuff);
        LogFmtStrA(SPLOGLV_INFO, "status:%d", pTempRlt->status); 

        LogFmtStrA(SPLOGLV_INFO, "pri_rscp:%d,pri_rssi:%d,div_rscp:%d,div_rssi:%d",
            pTempRlt->pri_rscp, pTempRlt->pri_rssi, pTempRlt->div_rscp, pTempRlt->div_rssi);

        pRlt->status = pTempRlt->status;
        pRlt->pri_rscp = pTempRlt->pri_rscp;
        pRlt->pri_rssi = pTempRlt->pri_rssi;
        pRlt->div_rscp = pTempRlt->div_rscp;
        pRlt->div_rssi = pTempRlt->div_rssi;
        res = pRlt->status;        
    }

    return ModemV3_TraceLogInfor(res);
}

SPRESULT CCaliCmd::ModemV3_WCDMA_NstGetSeBer(const PC_NST_WCD_GET_RLT_REQ_T* pReq, PC_NST_WCD_GET_RLT_SEBER_T* pRlt)
{
    CheckValidPointer(pReq);
    CheckValidPointer(pRlt);
    LogFmtStrA(SPLOGLV_INFO, "wcdma nst get seber result, type:%d,ant:%d,param:%d,%d",pReq->type,pReq->ant,pReq->param[0],pReq->param[1]);
    if (pReq->type != NST_WCD_RLT_RX_SEBER)
    {
        LogFmtStrA(SPLOGLV_ERROR, "The type of input is invalid.");
        return SP_E_INVALID_PARAMETER;
    }

    SPRESULT    res = SP_OK;
    uint32 recvSize = 0;

    L1_NST_WCD_GET_RLT_REQ_T L1;
    ZeroMemory((void* )&L1,    sizeof(L1));

    L1.subcmd_head.SubCmdCode  = Convert16(NST_WCD_GET_RLT_REQ);
    L1.subcmd_head.SubCmdSize  = Convert16((uint16)sizeof(L1));

    L1.ant = pReq->ant;
    L1.type = (uint16)pReq->type;
    CopyMemory(L1.param, pReq->param, sizeof(L1.param));

    DeclareDiagHeader(hd, DIAG_MODEM_RF_E, ZERO_SUBTYPE); 
    res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvSize, m_dwTimeOut);

    if (SP_OK == res)
    {
        L1_NST_WCD_GET_RLT_SEBER_T* pTempRlt = (L1_NST_WCD_GET_RLT_SEBER_T*)(m_diagBuff);
        LogFmtStrA(SPLOGLV_INFO, "status:%d", pTempRlt->status); 

        LogFmtStrA(SPLOGLV_INFO, "se_ber_error:%d,se_ber_status:%d", pTempRlt->se_ber_error, pTempRlt->se_ber_status);

        pRlt->status = pTempRlt->status;
        pRlt->se_ber_error = pTempRlt->se_ber_error;
        pRlt->se_ber_status = pTempRlt->se_ber_status;
        res = pRlt->status;        
    }

    return ModemV3_TraceLogInfor(res);
}

SPRESULT CCaliCmd::ModemV3_WCDMA_NstFstStart(const PC_NST_WCD_FST_START_REQ_T* pReq, PC_NST_WCD_FST_START_RLT_T* pRlt)
{
    CheckValidPointer(pReq);
    CheckValidPointer(pRlt);
    LogFmtStrA(SPLOGLV_INFO, "wcdma nst fst start, segment_number:%d,slot_format:%d,pgc:%d,scrambling_code_num:%d,\
                             channel_code:%d,tpc_algo:%d,tpc_step_size:%d",
                             pReq->param.seg_num,pReq->param.slot_format,pReq->param.pgc,pReq->param.scrambling_code_num,
                             pReq->param.channel_code,pReq->param.tpc_algo,pReq->param.tpc_step_size);

    uint32 i = 0;
    L1_NST_WCD_FST_START_REQ_T L1;
    ZeroMemory((void* )&L1,    sizeof(L1));

    L1.subcmd_head.SubCmdCode  = Convert16(NST_WCD_FAST_START_REQ);    

    L1.param.channel_code = pReq->param.channel_code;
    L1.param.pgc = pReq->param.pgc;
    L1.param.scrambling_code_num = pReq->param.scrambling_code_num;
    L1.param.seg_num = pReq->param.seg_num;
    L1.param.slot_format = pReq->param.slot_format;
    L1.param.tpc_algo = pReq->param.tpc_algo;
    L1.param.tpc_step_size = pReq->param.tpc_step_size;
    L1.seg = (L1_NST_WCD_FST_SEG_T*)pReq->seg;

    uint16 nBufSize = (uint16)(sizeof(L1_SUBCMD_HEAD_T) + sizeof(L1_NST_WCD_FST_PARAM_T) + sizeof(L1_NST_WCD_FST_SEG_T)*pReq->param.seg_num);
    L1.subcmd_head.SubCmdSize  = nBufSize;

    uint8* pBuf = NULL;
    try
    {
        pBuf = new uint8[nBufSize];
    }
    catch (const std::bad_alloc& /*e*/)
    {
        pBuf = NULL;
    }
    if (NULL == pBuf)
    {
        LogFmtStrA(SPLOGLV_ERROR, "%s: Memory allocation failed.", __FUNCTION__);
        return SP_E_PHONE_ALLOC_MEMORY;
    }
    int nPos = 0;
    CopyMemory(pBuf+nPos, &L1.subcmd_head, sizeof(L1_SUBCMD_HEAD_T));
    nPos += sizeof(L1_SUBCMD_HEAD_T);

    CopyMemory(pBuf+nPos, &L1.param, sizeof(L1_NST_WCD_FST_PARAM_T));
    nPos += sizeof(L1_NST_WCD_FST_PARAM_T);

    LogFmtStrA(SPLOGLV_INFO, "Segment information:");
    for (i = 0; i < pReq->param.seg_num; i++)
    {
        LogFmtStrA(SPLOGLV_INFO, "index:%d,dl_uarfcn:%d,band:%d,rx_channel:%d,tx_channel:%d,total_length:%d,tx_frames_offset:%d,\
                         tx_length:%d,tx_power_q4:%d,total_seber_num:%d,measure_type_mask:%d",
                         i, pReq->seg[i].dl_uarfcn,pReq->seg[i].band,pReq->seg[i].rx_channel,pReq->seg[i].tx_channel,
                         pReq->seg[i].total_length,pReq->seg[i].tx_frames_offset,pReq->seg[i].tx_length,
                         pReq->seg[i].tx_power_q4,pReq->seg[i].total_seber_num,pReq->seg[i].measure_type_mask);

        CopyMemory(pBuf+nPos, &pReq->seg[i], sizeof(L1_NST_WCD_FST_SEG_T));
        nPos += sizeof(L1_NST_WCD_FST_SEG_T);
    }

    SPRESULT    res = SP_OK;
    uint32 recvSize = 0;    

    DeclareDiagHeader(hd, DIAG_MODEM_RF_E, ZERO_SUBTYPE); 
    res = SendAndRecv(hd, (const void* )pBuf, nBufSize, hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvSize, m_dwTimeOut);

    if (SP_OK == res)
    {
        L1_NST_WCD_FST_START_RLT_T* pTempRlt = (L1_NST_WCD_FST_START_RLT_T* )m_diagBuff;
        LogFmtStrA(SPLOGLV_INFO, "status:%d, result:%d,%d", pTempRlt->status, pTempRlt->result[0],pTempRlt->result[1]);  
        pRlt->status = pTempRlt->status;
        CopyMemory(pRlt->result, pTempRlt->result, sizeof(pRlt->result));
        res = pRlt->status;        
    }

    delete[] pBuf;
    return ModemV3_TraceLogInfor(res);
}

SPRESULT CCaliCmd::ModemV3_WCDMA_NstFstGetRlt(const PC_NST_WCD_FST_GET_RLT_REQ_T* pReq, PC_NST_WCD_FST_GET_RLT_RLT_T* pRlt)
{
    CheckValidPointer(pReq);
    CheckValidPointer(pRlt);
    LogFmtStrA(SPLOGLV_INFO, "wcdma nst fst get result, type:%d, reserved:%d,%d,%d,%d,%d,%d,%d,%d,%d,%d",pReq->type,
        pReq->reserved[0],pReq->reserved[1],pReq->reserved[2],pReq->reserved[3],pReq->reserved[4],
        pReq->reserved[5],pReq->reserved[6],pReq->reserved[7],pReq->reserved[8],pReq->reserved[9]);

    SPRESULT    res = SP_OK;
    uint32 recvSize = 0;

    L1_NST_WCD_FST_GET_RLT_REQ_T L1;
    ZeroMemory((void* )&L1,    sizeof(L1));

    L1.subcmd_head.SubCmdCode  = Convert16(NST_WCD_FAST_GET_RLT_REQ);
    L1.subcmd_head.SubCmdSize  = Convert16((uint16)sizeof(L1));

    L1.type = (uint32)pReq->type;
    CopyMemory(L1.reserved, pReq->reserved, sizeof(L1.reserved));

    DeclareDiagHeader(hd, DIAG_MODEM_RF_E, ZERO_SUBTYPE); 
    res = SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvSize, m_dwTimeOut);

    if (SP_OK == res)
    {
        L1_NST_WCD_FST_GET_RLT_RLT_T* pTempRlt = (L1_NST_WCD_FST_GET_RLT_RLT_T* )m_diagBuff;
        LogFmtStrA(SPLOGLV_INFO, "status:%d", pTempRlt->status);
        int nPos = sizeof(L1_SUBCMD_HEAD_T) + sizeof(uint32)*3;

        if (pTempRlt->type != L1.type)
        {
            LogFmtStrA(SPLOGLV_ERROR, "The type of response is mismatch with the one of request");
            return SP_E_PHONE_INVALID_DATA;
        }

        if (pTempRlt->seg_count > pRlt->seg_count)
        {
            LogFmtStrA(SPLOGLV_ERROR, "The segment count of response is invalid");
            return SP_E_PHONE_INVALID_DATA;
        }

        uint32 i = 0;
        if (L1.type == NST_WCD_RLT_RX_LEVEL)
        {
            for(i = 0; i < pTempRlt->seg_count; i++)
            {
                int nStep = sizeof(L1_NST_WCD_FST_GET_RLT_RSCP_T);
                
                L1_NST_WCD_FST_GET_RLT_RSCP_T* pRscpRlt = (L1_NST_WCD_FST_GET_RLT_RSCP_T*)(m_diagBuff+nPos);
                LogFmtStrA(SPLOGLV_INFO, "segment index:%d,pri_rscp:%d,pri_rssi:%d,div_rscp:%d,div_rssi:%d",
                    i, pRscpRlt->pri_rscp, pRscpRlt->pri_rssi, pRscpRlt->div_rscp, pRscpRlt->div_rssi);
            
                CopyMemory((void*)(pRlt->data+(i*nStep)), pRscpRlt, sizeof(L1_NST_WCD_FST_GET_RLT_RSCP_T));
                nPos += nStep;            
            }            
            
        }
        else if (L1.type == NST_WCD_RLT_RX_SEBER)
        {
            for(i = 0; i < pTempRlt->seg_count; i++)
            {
                int nStep = sizeof(L1_NST_WCD_FST_GET_RLT_SEBER_T);
                L1_NST_WCD_FST_GET_RLT_SEBER_T* pSeberRlt = (L1_NST_WCD_FST_GET_RLT_SEBER_T*)(m_diagBuff+nPos);

                LogFmtStrA(SPLOGLV_INFO, "segment index:%d, se_ber_error:%d,total_block:%d", i, pSeberRlt->se_ber_error, pSeberRlt->total_block);
                CopyMemory((void*)(pRlt->data+(i*nStep)), pSeberRlt, sizeof(L1_NST_WCD_FST_GET_RLT_SEBER_T));
                nPos += nStep;
            }
            
        }

        pRlt->status = pTempRlt->status;
        res = pRlt->status;        
    }

    return ModemV3_TraceLogInfor(res);
}

SPRESULT CCaliCmd::ModemV3_WCDMA_Save2Flash()
{
    PC_MODEM_RF_V3_NV_SAVE_REQ_CMD_T req;
    req.eNvType = NV_RF_SAVE_BY_MODE;
    req.eType = RF_MODE_WCDMA;
    PC_MODEM_RF_V3_NV_SAVE_RSP_RLST_T rlt;
    return ModemV3_Nv_Save2Flash(&req, &rlt);
}

SPRESULT CCaliCmd::ModemV3_WCDMA_LoadCalFlag(uint16* pflag)
{
    CheckValidPointer(pflag);
    uint32 nTestFlag;
    PC_MODEM_RF_V3_DATA_REQ_CMD_T tReq;
    ZeroMemory(&tReq, sizeof(tReq));

    tReq.eNvType = NVM_WCD_CAL_DATA_CAL_FLAG;

    PC_MODEM_RF_V3_DATA_PARAM_T   tNVData;
    ZeroMemory(&tNVData, sizeof(tNVData));
    tNVData.DataSize = sizeof(nTestFlag);

    CHKRESULT(ModemV3_Nv_Read(&tReq, &tNVData));
    memcpy(&nTestFlag, &tNVData.nData, sizeof(nTestFlag));
    *pflag = (uint16)nTestFlag;

    return SP_OK;
}

SPRESULT CCaliCmd::ModemV3_WCDMA_SaveCalFlag(uint16 flag)
{
    uint32 nTestFlag = flag;
    PC_MODEM_RF_V3_DATA_REQ_CMD_T tReq;
    ZeroMemory(&tReq, sizeof(tReq));
    tReq.eNvType = NVM_WCD_CAL_DATA_CAL_FLAG;
    PC_MODEM_RF_V3_DATA_PARAM_T   tNVData;
    ZeroMemory(&tNVData, sizeof(tNVData));
    tNVData.DataSize = sizeof(nTestFlag);
    memcpy(&tNVData.nData, &nTestFlag, sizeof(nTestFlag));

    CHKRESULT(ModemV3_Nv_Write(&tReq, &tNVData));
    return SP_OK;
}

SPRESULT CCaliCmd::ModemV3_WCDMA_COMMOM_GetVersion(WCDMA_VERSION_T* pVersion)
{
	CFnLog fuLog(GetISpLogObject(), L"ModemV3_WCDMA_COMMOM_GetVersion");

	if ( NULL == pVersion )
	{
		LogFmtStrA(SPLOGLV_INFO, "WCDMA version value is empty.");
		return SP_E_PHONE_INVALID_PARAMETER;
	}

	uint32 recvSize = 0;

	L1_RFB_COM_QUERY_CAL_IF_VERSION_REQ_T L1;
	ZeroMemory((void* )&L1, sizeof(L1));
	L1.head.SubCmdCode  = RF_COM_QUERY_CAL_IF_VERSION_REQ;
	L1.head.SubCmdSize  = (uint16)sizeof(L1);

	L1.rf_mode = RF_MODE_WCDMA;

	DeclareDiagHeader(hd, DIAG_MODEM_RF_E, ZERO_SUBTYPE);

	CHKRESULT(SendAndRecv(hd, (const void* )&L1, sizeof(L1), hd, (void* )m_diagBuff, sizeof(m_diagBuff), &recvSize, m_dwTimeOut));

	if (recvSize < sizeof(L1_RFB_COM_QUERY_CAL_IF_VERSION_RLT_T))
	{
		LogFmtStrA(SPLOGLV_ERROR, "%s: Invalid response package size.", __FUNCTION__);
		return SP_E_PHONE_INVALID_LENGTH;
	}

	CHKRESULT(DetermineStatus(RF_COM_QUERY_CAL_IF_VERSION_REQ, m_diagBuff, recvSize));

	L1_RFB_COM_QUERY_CAL_IF_VERSION_RLT_T* pRLT = (L1_RFB_COM_QUERY_CAL_IF_VERSION_RLT_T*)m_diagBuff;
	memcpy(pVersion, pRLT->if_version, sizeof(WCDMA_VERSION_T));

	return SP_OK;
}
/// wcdma v3 end